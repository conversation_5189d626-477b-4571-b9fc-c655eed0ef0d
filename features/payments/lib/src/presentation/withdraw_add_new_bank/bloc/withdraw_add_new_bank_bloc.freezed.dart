// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_add_new_bank_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WithdrawAddNewBankEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawAddNewBankEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankEvent()';
}


}

/// @nodoc
class $WithdrawAddNewBankEventCopyWith<$Res>  {
$WithdrawAddNewBankEventCopyWith(WithdrawAddNewBankEvent _, $Res Function(WithdrawAddNewBankEvent) __);
}


/// @nodoc


class OnLocationOfBankChangedEvent implements WithdrawAddNewBankEvent {
  const OnLocationOfBankChangedEvent({required this.index});
  

 final  int index;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnLocationOfBankChangedEventCopyWith<OnLocationOfBankChangedEvent> get copyWith => _$OnLocationOfBankChangedEventCopyWithImpl<OnLocationOfBankChangedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnLocationOfBankChangedEvent&&(identical(other.index, index) || other.index == index));
}


@override
int get hashCode => Object.hash(runtimeType,index);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onLocationOfBankChanged(index: $index)';
}


}

/// @nodoc
abstract mixin class $OnLocationOfBankChangedEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $OnLocationOfBankChangedEventCopyWith(OnLocationOfBankChangedEvent value, $Res Function(OnLocationOfBankChangedEvent) _then) = _$OnLocationOfBankChangedEventCopyWithImpl;
@useResult
$Res call({
 int index
});




}
/// @nodoc
class _$OnLocationOfBankChangedEventCopyWithImpl<$Res>
    implements $OnLocationOfBankChangedEventCopyWith<$Res> {
  _$OnLocationOfBankChangedEventCopyWithImpl(this._self, this._then);

  final OnLocationOfBankChangedEvent _self;
  final $Res Function(OnLocationOfBankChangedEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? index = null,}) {
  return _then(OnLocationOfBankChangedEvent(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class OnTransferTypeChangedEvent implements WithdrawAddNewBankEvent {
  const OnTransferTypeChangedEvent({required this.index});
  

 final  int index;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnTransferTypeChangedEventCopyWith<OnTransferTypeChangedEvent> get copyWith => _$OnTransferTypeChangedEventCopyWithImpl<OnTransferTypeChangedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnTransferTypeChangedEvent&&(identical(other.index, index) || other.index == index));
}


@override
int get hashCode => Object.hash(runtimeType,index);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onTransferTypeChanged(index: $index)';
}


}

/// @nodoc
abstract mixin class $OnTransferTypeChangedEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $OnTransferTypeChangedEventCopyWith(OnTransferTypeChangedEvent value, $Res Function(OnTransferTypeChangedEvent) _then) = _$OnTransferTypeChangedEventCopyWithImpl;
@useResult
$Res call({
 int index
});




}
/// @nodoc
class _$OnTransferTypeChangedEventCopyWithImpl<$Res>
    implements $OnTransferTypeChangedEventCopyWith<$Res> {
  _$OnTransferTypeChangedEventCopyWithImpl(this._self, this._then);

  final OnTransferTypeChangedEvent _self;
  final $Res Function(OnTransferTypeChangedEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? index = null,}) {
  return _then(OnTransferTypeChangedEvent(
index: null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class GetCountriesForLocationOfBankEvent implements WithdrawAddNewBankEvent {
  const GetCountriesForLocationOfBankEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetCountriesForLocationOfBankEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankEvent.getCountriesForLocationOfBank()';
}


}




/// @nodoc


class GetTransferTypeEvent implements WithdrawAddNewBankEvent {
  const GetTransferTypeEvent({required this.brokerId, required this.countryCodeInThreeCharacter});
  

 final  String brokerId;
 final  String countryCodeInThreeCharacter;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetTransferTypeEventCopyWith<GetTransferTypeEvent> get copyWith => _$GetTransferTypeEventCopyWithImpl<GetTransferTypeEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetTransferTypeEvent&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.countryCodeInThreeCharacter, countryCodeInThreeCharacter) || other.countryCodeInThreeCharacter == countryCodeInThreeCharacter));
}


@override
int get hashCode => Object.hash(runtimeType,brokerId,countryCodeInThreeCharacter);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.getTransferType(brokerId: $brokerId, countryCodeInThreeCharacter: $countryCodeInThreeCharacter)';
}


}

/// @nodoc
abstract mixin class $GetTransferTypeEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $GetTransferTypeEventCopyWith(GetTransferTypeEvent value, $Res Function(GetTransferTypeEvent) _then) = _$GetTransferTypeEventCopyWithImpl;
@useResult
$Res call({
 String brokerId, String countryCodeInThreeCharacter
});




}
/// @nodoc
class _$GetTransferTypeEventCopyWithImpl<$Res>
    implements $GetTransferTypeEventCopyWith<$Res> {
  _$GetTransferTypeEventCopyWithImpl(this._self, this._then);

  final GetTransferTypeEvent _self;
  final $Res Function(GetTransferTypeEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? brokerId = null,Object? countryCodeInThreeCharacter = null,}) {
  return _then(GetTransferTypeEvent(
brokerId: null == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String,countryCodeInThreeCharacter: null == countryCodeInThreeCharacter ? _self.countryCodeInThreeCharacter : countryCodeInThreeCharacter // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class OnFormChangedEvent implements WithdrawAddNewBankEvent {
  const OnFormChangedEvent({required this.withdrawAddNewAccountParam});
  

 final  WithdrawAddNewAccountParam withdrawAddNewAccountParam;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnFormChangedEventCopyWith<OnFormChangedEvent> get copyWith => _$OnFormChangedEventCopyWithImpl<OnFormChangedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnFormChangedEvent&&(identical(other.withdrawAddNewAccountParam, withdrawAddNewAccountParam) || other.withdrawAddNewAccountParam == withdrawAddNewAccountParam));
}


@override
int get hashCode => Object.hash(runtimeType,withdrawAddNewAccountParam);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onFormChanged(withdrawAddNewAccountParam: $withdrawAddNewAccountParam)';
}


}

/// @nodoc
abstract mixin class $OnFormChangedEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $OnFormChangedEventCopyWith(OnFormChangedEvent value, $Res Function(OnFormChangedEvent) _then) = _$OnFormChangedEventCopyWithImpl;
@useResult
$Res call({
 WithdrawAddNewAccountParam withdrawAddNewAccountParam
});


$WithdrawAddNewAccountParamCopyWith<$Res> get withdrawAddNewAccountParam;

}
/// @nodoc
class _$OnFormChangedEventCopyWithImpl<$Res>
    implements $OnFormChangedEventCopyWith<$Res> {
  _$OnFormChangedEventCopyWithImpl(this._self, this._then);

  final OnFormChangedEvent _self;
  final $Res Function(OnFormChangedEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? withdrawAddNewAccountParam = null,}) {
  return _then(OnFormChangedEvent(
withdrawAddNewAccountParam: null == withdrawAddNewAccountParam ? _self.withdrawAddNewAccountParam : withdrawAddNewAccountParam // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewAccountParam,
  ));
}

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewAccountParamCopyWith<$Res> get withdrawAddNewAccountParam {
  
  return $WithdrawAddNewAccountParamCopyWith<$Res>(_self.withdrawAddNewAccountParam, (value) {
    return _then(_self.copyWith(withdrawAddNewAccountParam: value));
  });
}
}

/// @nodoc


class OnRequestWithdrawButtonPressedEvent implements WithdrawAddNewBankEvent {
  const OnRequestWithdrawButtonPressedEvent({required this.withdrawAddNewAccountParam, required this.withdrawFlowParams});
  

 final  WithdrawAddNewAccountParam withdrawAddNewAccountParam;
 final  WithdrawFlowParams withdrawFlowParams;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnRequestWithdrawButtonPressedEventCopyWith<OnRequestWithdrawButtonPressedEvent> get copyWith => _$OnRequestWithdrawButtonPressedEventCopyWithImpl<OnRequestWithdrawButtonPressedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnRequestWithdrawButtonPressedEvent&&(identical(other.withdrawAddNewAccountParam, withdrawAddNewAccountParam) || other.withdrawAddNewAccountParam == withdrawAddNewAccountParam)&&(identical(other.withdrawFlowParams, withdrawFlowParams) || other.withdrawFlowParams == withdrawFlowParams));
}


@override
int get hashCode => Object.hash(runtimeType,withdrawAddNewAccountParam,withdrawFlowParams);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onRequestWithdrawButtonPressed(withdrawAddNewAccountParam: $withdrawAddNewAccountParam, withdrawFlowParams: $withdrawFlowParams)';
}


}

/// @nodoc
abstract mixin class $OnRequestWithdrawButtonPressedEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $OnRequestWithdrawButtonPressedEventCopyWith(OnRequestWithdrawButtonPressedEvent value, $Res Function(OnRequestWithdrawButtonPressedEvent) _then) = _$OnRequestWithdrawButtonPressedEventCopyWithImpl;
@useResult
$Res call({
 WithdrawAddNewAccountParam withdrawAddNewAccountParam, WithdrawFlowParams withdrawFlowParams
});


$WithdrawAddNewAccountParamCopyWith<$Res> get withdrawAddNewAccountParam;$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams;

}
/// @nodoc
class _$OnRequestWithdrawButtonPressedEventCopyWithImpl<$Res>
    implements $OnRequestWithdrawButtonPressedEventCopyWith<$Res> {
  _$OnRequestWithdrawButtonPressedEventCopyWithImpl(this._self, this._then);

  final OnRequestWithdrawButtonPressedEvent _self;
  final $Res Function(OnRequestWithdrawButtonPressedEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? withdrawAddNewAccountParam = null,Object? withdrawFlowParams = null,}) {
  return _then(OnRequestWithdrawButtonPressedEvent(
withdrawAddNewAccountParam: null == withdrawAddNewAccountParam ? _self.withdrawAddNewAccountParam : withdrawAddNewAccountParam // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewAccountParam,withdrawFlowParams: null == withdrawFlowParams ? _self.withdrawFlowParams : withdrawFlowParams // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewAccountParamCopyWith<$Res> get withdrawAddNewAccountParam {
  
  return $WithdrawAddNewAccountParamCopyWith<$Res>(_self.withdrawAddNewAccountParam, (value) {
    return _then(_self.copyWith(withdrawAddNewAccountParam: value));
  });
}/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.withdrawFlowParams, (value) {
    return _then(_self.copyWith(withdrawFlowParams: value));
  });
}
}

/// @nodoc


class OnWithdrawalDetailsPressedEvent implements WithdrawAddNewBankEvent {
  const OnWithdrawalDetailsPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnWithdrawalDetailsPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onWithdrawalDetailsPressed()';
}


}




/// @nodoc


class OnAccountManuallyEnteredEvent implements WithdrawAddNewBankEvent {
  const OnAccountManuallyEnteredEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnAccountManuallyEnteredEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onAccountManuallyEntered()';
}


}




/// @nodoc


class ResetProcessStateEvent implements WithdrawAddNewBankEvent {
  const ResetProcessStateEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ResetProcessStateEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankEvent.resetProcessState()';
}


}




/// @nodoc


class OnFeesChangedEvent implements WithdrawAddNewBankEvent {
  const OnFeesChangedEvent(this.fees, this.withdrawFlowParams);
  

 final  num fees;
 final  WithdrawFlowParams withdrawFlowParams;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnFeesChangedEventCopyWith<OnFeesChangedEvent> get copyWith => _$OnFeesChangedEventCopyWithImpl<OnFeesChangedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnFeesChangedEvent&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.withdrawFlowParams, withdrawFlowParams) || other.withdrawFlowParams == withdrawFlowParams));
}


@override
int get hashCode => Object.hash(runtimeType,fees,withdrawFlowParams);

@override
String toString() {
  return 'WithdrawAddNewBankEvent.onFeesChanged(fees: $fees, withdrawFlowParams: $withdrawFlowParams)';
}


}

/// @nodoc
abstract mixin class $OnFeesChangedEventCopyWith<$Res> implements $WithdrawAddNewBankEventCopyWith<$Res> {
  factory $OnFeesChangedEventCopyWith(OnFeesChangedEvent value, $Res Function(OnFeesChangedEvent) _then) = _$OnFeesChangedEventCopyWithImpl;
@useResult
$Res call({
 num fees, WithdrawFlowParams withdrawFlowParams
});


$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams;

}
/// @nodoc
class _$OnFeesChangedEventCopyWithImpl<$Res>
    implements $OnFeesChangedEventCopyWith<$Res> {
  _$OnFeesChangedEventCopyWithImpl(this._self, this._then);

  final OnFeesChangedEvent _self;
  final $Res Function(OnFeesChangedEvent) _then;

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? fees = null,Object? withdrawFlowParams = null,}) {
  return _then(OnFeesChangedEvent(
null == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as num,null == withdrawFlowParams ? _self.withdrawFlowParams : withdrawFlowParams // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of WithdrawAddNewBankEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.withdrawFlowParams, (value) {
    return _then(_self.copyWith(withdrawFlowParams: value));
  });
}
}

/// @nodoc
mixin _$WithdrawAddNewBankState {

 WithdrawAddNewBankProcessState get processState; bool get isButtonDisabled; bool get isRequestWithdrawButtonLoading; bool get isBankLocationLoading; bool get isTransferTypeLoading; int get selectedBankLocationIndex; int get selectedTransferTypeIndex; List<CountryData> get countries; List<String> get transferType; WithdrawAddNewAccountParam? get formData; bool get isAccountNameManuallyEntered; String get autoCreatedAccountNickname; WithdrawStatusType? get withdrawStatus; bool get hasInsufficientFunds;// Add validation fields
 String get insufficientFundsMessage; num? get withdrawalFees;// Store current fees for validation
// Store validation data for localized error message generation
 num? get validationWithdrawalAmount; num? get validationAccountBalance; num? get validationWithdrawalFees; String? get validationCurrency;
/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawAddNewBankStateCopyWith<WithdrawAddNewBankState> get copyWith => _$WithdrawAddNewBankStateCopyWithImpl<WithdrawAddNewBankState>(this as WithdrawAddNewBankState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawAddNewBankState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.isButtonDisabled, isButtonDisabled) || other.isButtonDisabled == isButtonDisabled)&&(identical(other.isRequestWithdrawButtonLoading, isRequestWithdrawButtonLoading) || other.isRequestWithdrawButtonLoading == isRequestWithdrawButtonLoading)&&(identical(other.isBankLocationLoading, isBankLocationLoading) || other.isBankLocationLoading == isBankLocationLoading)&&(identical(other.isTransferTypeLoading, isTransferTypeLoading) || other.isTransferTypeLoading == isTransferTypeLoading)&&(identical(other.selectedBankLocationIndex, selectedBankLocationIndex) || other.selectedBankLocationIndex == selectedBankLocationIndex)&&(identical(other.selectedTransferTypeIndex, selectedTransferTypeIndex) || other.selectedTransferTypeIndex == selectedTransferTypeIndex)&&const DeepCollectionEquality().equals(other.countries, countries)&&const DeepCollectionEquality().equals(other.transferType, transferType)&&(identical(other.formData, formData) || other.formData == formData)&&(identical(other.isAccountNameManuallyEntered, isAccountNameManuallyEntered) || other.isAccountNameManuallyEntered == isAccountNameManuallyEntered)&&(identical(other.autoCreatedAccountNickname, autoCreatedAccountNickname) || other.autoCreatedAccountNickname == autoCreatedAccountNickname)&&(identical(other.withdrawStatus, withdrawStatus) || other.withdrawStatus == withdrawStatus)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.withdrawalFees, withdrawalFees) || other.withdrawalFees == withdrawalFees)&&(identical(other.validationWithdrawalAmount, validationWithdrawalAmount) || other.validationWithdrawalAmount == validationWithdrawalAmount)&&(identical(other.validationAccountBalance, validationAccountBalance) || other.validationAccountBalance == validationAccountBalance)&&(identical(other.validationWithdrawalFees, validationWithdrawalFees) || other.validationWithdrawalFees == validationWithdrawalFees)&&(identical(other.validationCurrency, validationCurrency) || other.validationCurrency == validationCurrency));
}


@override
int get hashCode => Object.hashAll([runtimeType,processState,isButtonDisabled,isRequestWithdrawButtonLoading,isBankLocationLoading,isTransferTypeLoading,selectedBankLocationIndex,selectedTransferTypeIndex,const DeepCollectionEquality().hash(countries),const DeepCollectionEquality().hash(transferType),formData,isAccountNameManuallyEntered,autoCreatedAccountNickname,withdrawStatus,hasInsufficientFunds,insufficientFundsMessage,withdrawalFees,validationWithdrawalAmount,validationAccountBalance,validationWithdrawalFees,validationCurrency]);

@override
String toString() {
  return 'WithdrawAddNewBankState(processState: $processState, isButtonDisabled: $isButtonDisabled, isRequestWithdrawButtonLoading: $isRequestWithdrawButtonLoading, isBankLocationLoading: $isBankLocationLoading, isTransferTypeLoading: $isTransferTypeLoading, selectedBankLocationIndex: $selectedBankLocationIndex, selectedTransferTypeIndex: $selectedTransferTypeIndex, countries: $countries, transferType: $transferType, formData: $formData, isAccountNameManuallyEntered: $isAccountNameManuallyEntered, autoCreatedAccountNickname: $autoCreatedAccountNickname, withdrawStatus: $withdrawStatus, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, withdrawalFees: $withdrawalFees, validationWithdrawalAmount: $validationWithdrawalAmount, validationAccountBalance: $validationAccountBalance, validationWithdrawalFees: $validationWithdrawalFees, validationCurrency: $validationCurrency)';
}


}

/// @nodoc
abstract mixin class $WithdrawAddNewBankStateCopyWith<$Res>  {
  factory $WithdrawAddNewBankStateCopyWith(WithdrawAddNewBankState value, $Res Function(WithdrawAddNewBankState) _then) = _$WithdrawAddNewBankStateCopyWithImpl;
@useResult
$Res call({
 WithdrawAddNewBankProcessState processState, bool isButtonDisabled, bool isRequestWithdrawButtonLoading, bool isBankLocationLoading, bool isTransferTypeLoading, int selectedBankLocationIndex, int selectedTransferTypeIndex, List<CountryData> countries, List<String> transferType, WithdrawAddNewAccountParam? formData, bool isAccountNameManuallyEntered, String autoCreatedAccountNickname, WithdrawStatusType? withdrawStatus, bool hasInsufficientFunds, String insufficientFundsMessage, num? withdrawalFees, num? validationWithdrawalAmount, num? validationAccountBalance, num? validationWithdrawalFees, String? validationCurrency
});


$WithdrawAddNewBankProcessStateCopyWith<$Res> get processState;$WithdrawAddNewAccountParamCopyWith<$Res>? get formData;

}
/// @nodoc
class _$WithdrawAddNewBankStateCopyWithImpl<$Res>
    implements $WithdrawAddNewBankStateCopyWith<$Res> {
  _$WithdrawAddNewBankStateCopyWithImpl(this._self, this._then);

  final WithdrawAddNewBankState _self;
  final $Res Function(WithdrawAddNewBankState) _then;

/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? isButtonDisabled = null,Object? isRequestWithdrawButtonLoading = null,Object? isBankLocationLoading = null,Object? isTransferTypeLoading = null,Object? selectedBankLocationIndex = null,Object? selectedTransferTypeIndex = null,Object? countries = null,Object? transferType = null,Object? formData = freezed,Object? isAccountNameManuallyEntered = null,Object? autoCreatedAccountNickname = null,Object? withdrawStatus = freezed,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? withdrawalFees = freezed,Object? validationWithdrawalAmount = freezed,Object? validationAccountBalance = freezed,Object? validationWithdrawalFees = freezed,Object? validationCurrency = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewBankProcessState,isButtonDisabled: null == isButtonDisabled ? _self.isButtonDisabled : isButtonDisabled // ignore: cast_nullable_to_non_nullable
as bool,isRequestWithdrawButtonLoading: null == isRequestWithdrawButtonLoading ? _self.isRequestWithdrawButtonLoading : isRequestWithdrawButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,isBankLocationLoading: null == isBankLocationLoading ? _self.isBankLocationLoading : isBankLocationLoading // ignore: cast_nullable_to_non_nullable
as bool,isTransferTypeLoading: null == isTransferTypeLoading ? _self.isTransferTypeLoading : isTransferTypeLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedBankLocationIndex: null == selectedBankLocationIndex ? _self.selectedBankLocationIndex : selectedBankLocationIndex // ignore: cast_nullable_to_non_nullable
as int,selectedTransferTypeIndex: null == selectedTransferTypeIndex ? _self.selectedTransferTypeIndex : selectedTransferTypeIndex // ignore: cast_nullable_to_non_nullable
as int,countries: null == countries ? _self.countries : countries // ignore: cast_nullable_to_non_nullable
as List<CountryData>,transferType: null == transferType ? _self.transferType : transferType // ignore: cast_nullable_to_non_nullable
as List<String>,formData: freezed == formData ? _self.formData : formData // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewAccountParam?,isAccountNameManuallyEntered: null == isAccountNameManuallyEntered ? _self.isAccountNameManuallyEntered : isAccountNameManuallyEntered // ignore: cast_nullable_to_non_nullable
as bool,autoCreatedAccountNickname: null == autoCreatedAccountNickname ? _self.autoCreatedAccountNickname : autoCreatedAccountNickname // ignore: cast_nullable_to_non_nullable
as String,withdrawStatus: freezed == withdrawStatus ? _self.withdrawStatus : withdrawStatus // ignore: cast_nullable_to_non_nullable
as WithdrawStatusType?,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,withdrawalFees: freezed == withdrawalFees ? _self.withdrawalFees : withdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalAmount: freezed == validationWithdrawalAmount ? _self.validationWithdrawalAmount : validationWithdrawalAmount // ignore: cast_nullable_to_non_nullable
as num?,validationAccountBalance: freezed == validationAccountBalance ? _self.validationAccountBalance : validationAccountBalance // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalFees: freezed == validationWithdrawalFees ? _self.validationWithdrawalFees : validationWithdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationCurrency: freezed == validationCurrency ? _self.validationCurrency : validationCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewBankProcessStateCopyWith<$Res> get processState {
  
  return $WithdrawAddNewBankProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewAccountParamCopyWith<$Res>? get formData {
    if (_self.formData == null) {
    return null;
  }

  return $WithdrawAddNewAccountParamCopyWith<$Res>(_self.formData!, (value) {
    return _then(_self.copyWith(formData: value));
  });
}
}


/// @nodoc


class _WithdrawAddNewBankState implements WithdrawAddNewBankState {
  const _WithdrawAddNewBankState({this.processState = const WithdrawAddNewBankProcessState.loaded(), this.isButtonDisabled = true, this.isRequestWithdrawButtonLoading = false, this.isBankLocationLoading = false, this.isTransferTypeLoading = false, this.selectedBankLocationIndex = -1, this.selectedTransferTypeIndex = -1, final  List<CountryData> countries = const [], final  List<String> transferType = const [], this.formData, this.isAccountNameManuallyEntered = false, this.autoCreatedAccountNickname = "", this.withdrawStatus, this.hasInsufficientFunds = false, this.insufficientFundsMessage = '', this.withdrawalFees, this.validationWithdrawalAmount, this.validationAccountBalance, this.validationWithdrawalFees, this.validationCurrency}): _countries = countries,_transferType = transferType;
  

@override@JsonKey() final  WithdrawAddNewBankProcessState processState;
@override@JsonKey() final  bool isButtonDisabled;
@override@JsonKey() final  bool isRequestWithdrawButtonLoading;
@override@JsonKey() final  bool isBankLocationLoading;
@override@JsonKey() final  bool isTransferTypeLoading;
@override@JsonKey() final  int selectedBankLocationIndex;
@override@JsonKey() final  int selectedTransferTypeIndex;
 final  List<CountryData> _countries;
@override@JsonKey() List<CountryData> get countries {
  if (_countries is EqualUnmodifiableListView) return _countries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_countries);
}

 final  List<String> _transferType;
@override@JsonKey() List<String> get transferType {
  if (_transferType is EqualUnmodifiableListView) return _transferType;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_transferType);
}

@override final  WithdrawAddNewAccountParam? formData;
@override@JsonKey() final  bool isAccountNameManuallyEntered;
@override@JsonKey() final  String autoCreatedAccountNickname;
@override final  WithdrawStatusType? withdrawStatus;
@override@JsonKey() final  bool hasInsufficientFunds;
// Add validation fields
@override@JsonKey() final  String insufficientFundsMessage;
@override final  num? withdrawalFees;
// Store current fees for validation
// Store validation data for localized error message generation
@override final  num? validationWithdrawalAmount;
@override final  num? validationAccountBalance;
@override final  num? validationWithdrawalFees;
@override final  String? validationCurrency;

/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawAddNewBankStateCopyWith<_WithdrawAddNewBankState> get copyWith => __$WithdrawAddNewBankStateCopyWithImpl<_WithdrawAddNewBankState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawAddNewBankState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.isButtonDisabled, isButtonDisabled) || other.isButtonDisabled == isButtonDisabled)&&(identical(other.isRequestWithdrawButtonLoading, isRequestWithdrawButtonLoading) || other.isRequestWithdrawButtonLoading == isRequestWithdrawButtonLoading)&&(identical(other.isBankLocationLoading, isBankLocationLoading) || other.isBankLocationLoading == isBankLocationLoading)&&(identical(other.isTransferTypeLoading, isTransferTypeLoading) || other.isTransferTypeLoading == isTransferTypeLoading)&&(identical(other.selectedBankLocationIndex, selectedBankLocationIndex) || other.selectedBankLocationIndex == selectedBankLocationIndex)&&(identical(other.selectedTransferTypeIndex, selectedTransferTypeIndex) || other.selectedTransferTypeIndex == selectedTransferTypeIndex)&&const DeepCollectionEquality().equals(other._countries, _countries)&&const DeepCollectionEquality().equals(other._transferType, _transferType)&&(identical(other.formData, formData) || other.formData == formData)&&(identical(other.isAccountNameManuallyEntered, isAccountNameManuallyEntered) || other.isAccountNameManuallyEntered == isAccountNameManuallyEntered)&&(identical(other.autoCreatedAccountNickname, autoCreatedAccountNickname) || other.autoCreatedAccountNickname == autoCreatedAccountNickname)&&(identical(other.withdrawStatus, withdrawStatus) || other.withdrawStatus == withdrawStatus)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.withdrawalFees, withdrawalFees) || other.withdrawalFees == withdrawalFees)&&(identical(other.validationWithdrawalAmount, validationWithdrawalAmount) || other.validationWithdrawalAmount == validationWithdrawalAmount)&&(identical(other.validationAccountBalance, validationAccountBalance) || other.validationAccountBalance == validationAccountBalance)&&(identical(other.validationWithdrawalFees, validationWithdrawalFees) || other.validationWithdrawalFees == validationWithdrawalFees)&&(identical(other.validationCurrency, validationCurrency) || other.validationCurrency == validationCurrency));
}


@override
int get hashCode => Object.hashAll([runtimeType,processState,isButtonDisabled,isRequestWithdrawButtonLoading,isBankLocationLoading,isTransferTypeLoading,selectedBankLocationIndex,selectedTransferTypeIndex,const DeepCollectionEquality().hash(_countries),const DeepCollectionEquality().hash(_transferType),formData,isAccountNameManuallyEntered,autoCreatedAccountNickname,withdrawStatus,hasInsufficientFunds,insufficientFundsMessage,withdrawalFees,validationWithdrawalAmount,validationAccountBalance,validationWithdrawalFees,validationCurrency]);

@override
String toString() {
  return 'WithdrawAddNewBankState(processState: $processState, isButtonDisabled: $isButtonDisabled, isRequestWithdrawButtonLoading: $isRequestWithdrawButtonLoading, isBankLocationLoading: $isBankLocationLoading, isTransferTypeLoading: $isTransferTypeLoading, selectedBankLocationIndex: $selectedBankLocationIndex, selectedTransferTypeIndex: $selectedTransferTypeIndex, countries: $countries, transferType: $transferType, formData: $formData, isAccountNameManuallyEntered: $isAccountNameManuallyEntered, autoCreatedAccountNickname: $autoCreatedAccountNickname, withdrawStatus: $withdrawStatus, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, withdrawalFees: $withdrawalFees, validationWithdrawalAmount: $validationWithdrawalAmount, validationAccountBalance: $validationAccountBalance, validationWithdrawalFees: $validationWithdrawalFees, validationCurrency: $validationCurrency)';
}


}

/// @nodoc
abstract mixin class _$WithdrawAddNewBankStateCopyWith<$Res> implements $WithdrawAddNewBankStateCopyWith<$Res> {
  factory _$WithdrawAddNewBankStateCopyWith(_WithdrawAddNewBankState value, $Res Function(_WithdrawAddNewBankState) _then) = __$WithdrawAddNewBankStateCopyWithImpl;
@override @useResult
$Res call({
 WithdrawAddNewBankProcessState processState, bool isButtonDisabled, bool isRequestWithdrawButtonLoading, bool isBankLocationLoading, bool isTransferTypeLoading, int selectedBankLocationIndex, int selectedTransferTypeIndex, List<CountryData> countries, List<String> transferType, WithdrawAddNewAccountParam? formData, bool isAccountNameManuallyEntered, String autoCreatedAccountNickname, WithdrawStatusType? withdrawStatus, bool hasInsufficientFunds, String insufficientFundsMessage, num? withdrawalFees, num? validationWithdrawalAmount, num? validationAccountBalance, num? validationWithdrawalFees, String? validationCurrency
});


@override $WithdrawAddNewBankProcessStateCopyWith<$Res> get processState;@override $WithdrawAddNewAccountParamCopyWith<$Res>? get formData;

}
/// @nodoc
class __$WithdrawAddNewBankStateCopyWithImpl<$Res>
    implements _$WithdrawAddNewBankStateCopyWith<$Res> {
  __$WithdrawAddNewBankStateCopyWithImpl(this._self, this._then);

  final _WithdrawAddNewBankState _self;
  final $Res Function(_WithdrawAddNewBankState) _then;

/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? isButtonDisabled = null,Object? isRequestWithdrawButtonLoading = null,Object? isBankLocationLoading = null,Object? isTransferTypeLoading = null,Object? selectedBankLocationIndex = null,Object? selectedTransferTypeIndex = null,Object? countries = null,Object? transferType = null,Object? formData = freezed,Object? isAccountNameManuallyEntered = null,Object? autoCreatedAccountNickname = null,Object? withdrawStatus = freezed,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? withdrawalFees = freezed,Object? validationWithdrawalAmount = freezed,Object? validationAccountBalance = freezed,Object? validationWithdrawalFees = freezed,Object? validationCurrency = freezed,}) {
  return _then(_WithdrawAddNewBankState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewBankProcessState,isButtonDisabled: null == isButtonDisabled ? _self.isButtonDisabled : isButtonDisabled // ignore: cast_nullable_to_non_nullable
as bool,isRequestWithdrawButtonLoading: null == isRequestWithdrawButtonLoading ? _self.isRequestWithdrawButtonLoading : isRequestWithdrawButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,isBankLocationLoading: null == isBankLocationLoading ? _self.isBankLocationLoading : isBankLocationLoading // ignore: cast_nullable_to_non_nullable
as bool,isTransferTypeLoading: null == isTransferTypeLoading ? _self.isTransferTypeLoading : isTransferTypeLoading // ignore: cast_nullable_to_non_nullable
as bool,selectedBankLocationIndex: null == selectedBankLocationIndex ? _self.selectedBankLocationIndex : selectedBankLocationIndex // ignore: cast_nullable_to_non_nullable
as int,selectedTransferTypeIndex: null == selectedTransferTypeIndex ? _self.selectedTransferTypeIndex : selectedTransferTypeIndex // ignore: cast_nullable_to_non_nullable
as int,countries: null == countries ? _self._countries : countries // ignore: cast_nullable_to_non_nullable
as List<CountryData>,transferType: null == transferType ? _self._transferType : transferType // ignore: cast_nullable_to_non_nullable
as List<String>,formData: freezed == formData ? _self.formData : formData // ignore: cast_nullable_to_non_nullable
as WithdrawAddNewAccountParam?,isAccountNameManuallyEntered: null == isAccountNameManuallyEntered ? _self.isAccountNameManuallyEntered : isAccountNameManuallyEntered // ignore: cast_nullable_to_non_nullable
as bool,autoCreatedAccountNickname: null == autoCreatedAccountNickname ? _self.autoCreatedAccountNickname : autoCreatedAccountNickname // ignore: cast_nullable_to_non_nullable
as String,withdrawStatus: freezed == withdrawStatus ? _self.withdrawStatus : withdrawStatus // ignore: cast_nullable_to_non_nullable
as WithdrawStatusType?,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,withdrawalFees: freezed == withdrawalFees ? _self.withdrawalFees : withdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalAmount: freezed == validationWithdrawalAmount ? _self.validationWithdrawalAmount : validationWithdrawalAmount // ignore: cast_nullable_to_non_nullable
as num?,validationAccountBalance: freezed == validationAccountBalance ? _self.validationAccountBalance : validationAccountBalance // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalFees: freezed == validationWithdrawalFees ? _self.validationWithdrawalFees : validationWithdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationCurrency: freezed == validationCurrency ? _self.validationCurrency : validationCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewBankProcessStateCopyWith<$Res> get processState {
  
  return $WithdrawAddNewBankProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of WithdrawAddNewBankState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawAddNewAccountParamCopyWith<$Res>? get formData {
    if (_self.formData == null) {
    return null;
  }

  return $WithdrawAddNewAccountParamCopyWith<$Res>(_self.formData!, (value) {
    return _then(_self.copyWith(formData: value));
  });
}
}

/// @nodoc
mixin _$WithdrawAddNewBankProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawAddNewBankProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankProcessState()';
}


}

/// @nodoc
class $WithdrawAddNewBankProcessStateCopyWith<$Res>  {
$WithdrawAddNewBankProcessStateCopyWith(WithdrawAddNewBankProcessState _, $Res Function(WithdrawAddNewBankProcessState) __);
}


/// @nodoc


class LoadedState implements WithdrawAddNewBankProcessState {
  const LoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankProcessState.loaded()';
}


}




/// @nodoc


class SuccessState implements WithdrawAddNewBankProcessState {
  const SuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawAddNewBankProcessState.success()';
}


}




/// @nodoc


class ErrorState implements WithdrawAddNewBankProcessState {
  const ErrorState({required this.message});
  

 final  String message;

/// Create a copy of WithdrawAddNewBankProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ErrorStateCopyWith<ErrorState> get copyWith => _$ErrorStateCopyWithImpl<ErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorState&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'WithdrawAddNewBankProcessState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $ErrorStateCopyWith<$Res> implements $WithdrawAddNewBankProcessStateCopyWith<$Res> {
  factory $ErrorStateCopyWith(ErrorState value, $Res Function(ErrorState) _then) = _$ErrorStateCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$ErrorStateCopyWithImpl<$Res>
    implements $ErrorStateCopyWith<$Res> {
  _$ErrorStateCopyWithImpl(this._self, this._then);

  final ErrorState _self;
  final $Res Function(ErrorState) _then;

/// Create a copy of WithdrawAddNewBankProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(ErrorState(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
