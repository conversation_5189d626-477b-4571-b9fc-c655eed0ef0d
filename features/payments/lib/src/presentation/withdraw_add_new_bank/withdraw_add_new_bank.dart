import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/bloc/withdraw_add_new_bank_bloc.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/widgets/loaded_state_add_new_account.dart';

class WithdrawAddNewBank extends StatefulWidget {
  const WithdrawAddNewBank({super.key, required this.withdrawFlowParams});
  final WithdrawFlowParams withdrawFlowParams;

  @override
  State<WithdrawAddNewBank> createState() => _WithdrawAddNewBankState();
}

class _WithdrawAddNewBankState extends State<WithdrawAddNewBank> {
  final TextEditingController _nameOfAccountHolderController =
      TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _branchNameController = TextEditingController();
  final TextEditingController _swiftBicController = TextEditingController();
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _accountNicknameController =
      TextEditingController();

  @override
  void dispose() {
    _nameOfAccountHolderController.dispose();
    _bankNameController.dispose();
    _branchNameController.dispose();
    _swiftBicController.dispose();
    _accountNumberController.dispose();
    _accountNicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (ctx) => diContainer<WithdrawAddNewBankBloc>(),
      child: BlocBuilder<WithdrawAddNewBankBloc, WithdrawAddNewBankState>(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState,
        builder: (ctx, state) {
          return switch (state.processState) {
            LoadedState() => LoadedStateAddNewAccount(
              withdrawFlowParams: widget.withdrawFlowParams,
              nameOfAccountHolderController: _nameOfAccountHolderController,
              bankNameController: _bankNameController,
              branchNameController: _branchNameController,
              swiftBicController: _swiftBicController,
              accountNumberController: _accountNumberController,
              accountNicknameController: _accountNicknameController,
            ),
            ErrorState() => WithdrawStatusScreen.error(
              onButtonPressed: () => _onTap(ctx),
            ),
            // Nothing needs to be shown as navigation is handled in the bloc
            SuccessState() => const SizedBox.shrink(),
          };
        },
      ),
    );
  }

  void _onTap(BuildContext ctx) {
    final bloc = ctx.read<WithdrawAddNewBankBloc>();
    final state = bloc.state;
    if (state.transferType.isEmpty || state.countries.isEmpty) {
      Navigator.of(ctx).pop();
      return;
    }
    bloc.add(const WithdrawAddNewBankEvent.resetProcessState());
  }
}
