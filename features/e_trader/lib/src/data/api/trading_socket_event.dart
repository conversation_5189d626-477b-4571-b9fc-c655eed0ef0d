import 'package:socket_client/socket_client.dart';

enum TradingSocketEvent {
  marginRequirements(
    subscriptionEventName: 'SubscribeToMarginRequirements',
    unsubscriptionEventName: 'UnsubscribeFromMarginRequirements',
  ),
  quotes(
    subscriptionEventName: 'SubscribeToQuotes',
    unsubscriptionEventName: 'UnsubscribeFromQuotes',
  ),
  priceAlert(
    subscriptionEventName: 'Subscribe',
    unsubscriptionEventName: 'Unsubscribe',
  ),
  orders(
    subscriptionEventName: 'SubscribeToOrdersBySymbol',
    unsubscriptionEventName: 'Unsubscribe',
  ),
  positions(
    subscriptionEventName: 'SubscribeToPositions',
    unsubscriptionEventName: 'UnsubscribeFromPositions',
  ),
  accountBalance(
    subscriptionEventName: 'SubscribeToTradingAccountsBalance',
    unsubscriptionEventName: 'UnsubscribeFromTradingAccountsBalance',
  );

  final String subscriptionEventName;
  final String unsubscriptionEventName;

  const TradingSocketEvent({
    required this.subscriptionEventName,
    required this.unsubscriptionEventName,
  });
}

extension SocketEventTypeExtension on TradingSocketEvent {
  EventType get register => RegisterEvent(event: name);

  EventType get subscribe =>
      SubscribeEvent(event: name, methodName: subscriptionEventName);

  EventType get unsubscribe =>
      UnsubscribeEvent(event: name, methodName: unsubscriptionEventName);
}
