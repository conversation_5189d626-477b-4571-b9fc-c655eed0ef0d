import 'package:api_client/api_client.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent withdrawAddNewBank() {
  return DisplayableComponent(
    title: 'withdraw Add New Bank',
    children: [
      DisplayableComponent(
        title: 'withdraw Add New Bank',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/v1/getAllCountries': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/get_countries/success.json',
                  delayInMillis: 2000,
                ),
              ],
              'api/v1/country/ARE/info': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/get_transfer_type/success.json',
                  delayInMillis: 2000,
                ),
              ],
            });
          return WithdrawAddNewBank(
            withdrawFlowParams: WithdrawFlowParams(
              tradingAccountId: '',
              paymentType: WithdrawalMop.bank,
              accountCurrency: '',
              currency: '',
              conversionRateString: '',
              amount: 0,
              convertedAmount: 0,
              conversionRate: 0,
              accountBalance: 1000, // Add test account balance
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'withdraw Add New Bank (Cant load Countries or Transfer type)',
        onTap: () {
          return WithdrawAddNewBank(
            withdrawFlowParams: WithdrawFlowParams(
              tradingAccountId: '',
              paymentType: WithdrawalMop.bank,
              accountCurrency: '',
              currency: '',
              conversionRateString: '',
              amount: 0,
              convertedAmount: 0,
              conversionRate: 0,
              accountBalance: 1000, // Add test account balance
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'withdraw Add New Bank (submit error)',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/v1/getAllCountries': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/get_countries/success.json',
                  delayInMillis: 2000,
                ),
              ],
              'api/v1/country/ARE/info': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/get_transfer_type/success.json',
                  delayInMillis: 2000,
                ),
              ],
            });
          return WithdrawAddNewBank(
            withdrawFlowParams: WithdrawFlowParams(
              tradingAccountId: '',
              paymentType: WithdrawalMop.bank,
              accountCurrency: '',
              currency: '',
              conversionRateString: '',
              amount: 0,
              convertedAmount: 0,
              conversionRate: 0,
              accountBalance: 1000, // Add test account balance
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'withdraw New Bank Upload Doc',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({});
          return WithdrawNewBankUploadDoc(
            operationId: '',
            tradingAccountId: '',
          );
        },
      ),
    ],
  );
}
