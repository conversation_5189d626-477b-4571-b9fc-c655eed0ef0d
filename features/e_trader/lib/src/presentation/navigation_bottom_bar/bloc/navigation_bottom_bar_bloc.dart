import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/domain/usecase/fetch_all_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/watchlist_local_cache_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'navigation_bottom_bar_bloc.freezed.dart';
part 'navigation_bottom_bar_event.dart';
part 'navigation_bottom_bar_state.dart';

class NavigationBottomBarBloc
    extends Bloc<NavigationBottomBarEvent, NavigationBottomBarState>
    with DisposableMixin {
  final FetchAllWatchlistUseCase _fetchAllWatchlistUseCase;
  final WatchlistLocalCacheUseCase _watchlistLocalCacheUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;
  final LoggerBase _logger;

  NavigationBottomBarBloc(
    this._fetchAllWatchlistUseCase,
    this._watchlistLocalCacheUseCase,
    GetSelectedAccountUseCase _getSelectedAccountUseCase,
    this._logger,
    this._equitiTraderNavigation,
  ) : super(
        _NavigationBottomBarState(
          tradingAccountModel: _getSelectedAccountUseCase()!,
        ),
      ) {
    on<_OnFetchAllWatchlist>(_onFetchAllWatchlist);
    on<_NavigateToDepositPaymentOptions>(_navigateToPaymentOptions);
    on<_NavigateToWithdrawPaymentOptions>(_navigateToWithdrawPaymentOptions);
    on<_NavigateToTransferFunds>(_navigateToTransferFunds);
    add(_OnFetchAllWatchlist());
  }

  FutureOr<void> _onFetchAllWatchlist(
    _OnFetchAllWatchlist event,
    Emitter<NavigationBottomBarState> emit,
  ) async {
    final result = await _fetchAllWatchlistUseCase().run();
    result.fold((e) => _logger.logDebug(e.toString()), (watchlist) {
      _watchlistLocalCacheUseCase.saveWatchlist(watchlist);
    });
  }

  @override
  addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }

  FutureOr<void> _navigateToPaymentOptions(
    _NavigateToDepositPaymentOptions event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    log("Navigate to deposit payment options", name: "NavigationBottomBarBloc");
    _equitiTraderNavigation.navigateToDepositOptions(
      depositFlowConfig: DepositFlowConfig(
        origin: EquitiTraderRouteSchema.navBarRoute.url,
        depositType: DepositType.additional,
      ),
    );
  }

  FutureOr<void> _navigateToWithdrawPaymentOptions(
    _NavigateToWithdrawPaymentOptions event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    _equitiTraderNavigation.navigateToWithdrawOptions();
  }

  FutureOr<void> _navigateToTransferFunds(
    _NavigateToTransferFunds event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    _equitiTraderNavigation.goToTransferFundsScreen();
  }
}
