import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/bloc/withdraw_add_new_bank_bloc.dart';

class WithdrawAddNewBankBottomActions extends StatelessWidget {
  const WithdrawAddNewBankBottomActions({
    super.key,
    required this.onRequestWithdrawButtonPressed,
    required this.onWithdrawalDetailsPressed,
  });
  final void Function() onRequestWithdrawButtonPressed;
  final void Function() onWithdrawalDetailsPressed;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<WithdrawAddNewBankBloc, WithdrawAddNewBankState>(
      buildWhen:
          (previous, current) =>
              previous.isButtonDisabled != current.isButtonDisabled ||
              previous.isRequestWithdrawButtonLoading !=
                  current.isRequestWithdrawButtonLoading ||
              previous.hasInsufficientFunds != current.hasInsufficientFunds,
      builder: (ctx, state) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: DuploSpacing.spacing_xl_16,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DuploButton.defaultPrimary(
                  useFullWidth: true,
                  title: localization.payments_request_withdrawal,
                  isDisabled:
                      state.isButtonDisabled || state.hasInsufficientFunds,
                  isLoading: state.isRequestWithdrawButtonLoading,
                  trailingIcon:
                      Assets.images.chevronRightDirectional(context).keyName,
                  onTap: onRequestWithdrawButtonPressed,
                ),
                SizedBox(height: DuploSpacing.spacing_xl_16),
                DuploButton.link(
                  useFullWidth: true,
                  title: localization.payments_withdrawal_details,
                  isDisabled: state.isButtonDisabled,
                  onTap: onWithdrawalDetailsPressed,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
