part of 'withdraw_add_new_bank_bloc.dart';

@freezed
sealed class WithdrawAddNewBankState with _$WithdrawAddNewBankState {
  const factory WithdrawAddNewBankState({
    @Default(WithdrawAddNewBankProcessState.loaded())
    WithdrawAddNewBankProcessState processState,
    @Default(true) bool isButtonDisabled,
    @Default(false) bool isRequestWithdrawButtonLoading,
    @Default(false) bool isBankLocationLoading,
    @Default(false) bool isTransferTypeLoading,
    @Default(-1) int selectedBankLocationIndex,
    @Default(-1) int selectedTransferTypeIndex,
    @Default([]) List<CountryData> countries,
    @Default([]) List<String> transferType,
    WithdrawAddNewAccountParam? formData,
    @Default(false) bool isAccountNameManuallyEntered,
    @Default("") String autoCreatedAccountNickname,
    WithdrawStatusType? withdrawStatus,
    @Default(false) bool hasInsufficientFunds, // Add validation fields
    @Default('') String insufficientFundsMessage,
    num? withdrawalFees, // Store current fees for validation
    // Store validation data for localized error message generation
    num? validationWithdrawalAmount,
    num? validationAccountBalance,
    num? validationWithdrawalFees,
    String? validationCurrency,
  }) = _WithdrawAddNewBankState;
}

@freezed
sealed class WithdrawAddNewBankProcessState
    with _$WithdrawAddNewBankProcessState {
  const factory WithdrawAddNewBankProcessState.loaded() = LoadedState;
  const factory WithdrawAddNewBankProcessState.success() = SuccessState;
  const factory WithdrawAddNewBankProcessState.error({
    required String message,
  }) = ErrorState;
}
