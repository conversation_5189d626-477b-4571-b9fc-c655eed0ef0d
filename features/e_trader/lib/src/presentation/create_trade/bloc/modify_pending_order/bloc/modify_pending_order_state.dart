// ignore_for_file: prefer-immutable-bloc-state

part of 'modify_pending_order_bloc.dart';

@unfreezed
sealed class ModifyPendingOrderState with _$ModifyPendingOrderState {
  ModifyPendingOrderState._();
  factory ModifyPendingOrderState({
    required OrderModel orderModel,
    MarginInformationModel? marginInformation,
    String? accountNumber,
    @Default(TradeComponentState<double, OrderPriceErrorCode>.loading())
    TradeComponentState<double, OrderPriceErrorCode> orderPriceState,
    @Default(
      TradeComponentState<
        OrderLimitCalculation,
        OrderLimitErrorCode
      >.inactive(),
    )
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    takeProfitState,
    @Default(
      TradeComponentState<
        OrderLimitCalculation,
        OrderLimitErrorCode
      >.inactive(),
    )
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>
    stopLossState,
    @Default(ModifyPendingOrderProcessState.loading())
    ModifyPendingOrderProcessState currentState,
  }) = _ModifyPendingOrderState;

  double get orderPrice => orderPriceState.value;
  double get takeProfit =>
      takeProfitState.isActive() ? takeProfitState.value.price.toDouble() : 0.0;
  double get stopLoss =>
      stopLossState.isActive() ? stopLossState.value.price.toDouble() : 0.0;

  bool isValid() {
    final requiredComponentsValid = [
      orderPriceState,
    ].every((state) => state.isValid());

    final optionalComponentsValid = [
      takeProfitState,
      stopLossState,
    ].where((state) => state.isActive()).every((state) => state.isValid());
    return requiredComponentsValid && optionalComponentsValid;
  }
}

@freezed
sealed class ModifyPendingOrderProcessState
    with _$ModifyPendingOrderProcessState {
  const factory ModifyPendingOrderProcessState.loading() =
      ModifyPendingOrderLoadingProcessState;
  const factory ModifyPendingOrderProcessState.connected() =
      ModifyPendingOrderConnectedProcessState;
  const factory ModifyPendingOrderProcessState.disconnected() =
      ModifyPendingOrderDisconnectedProcessState;
  const factory ModifyPendingOrderProcessState.placingOrder() =
      ModifyPlacingPendingOrderProcessState;
  const factory ModifyPendingOrderProcessState.orderSuccess() =
      ModifyPendingOrderSuccessProcessState;
  const factory ModifyPendingOrderProcessState.orderError() =
      ModifyPendingOrderErrorProcessState;
  const factory ModifyPendingOrderProcessState.deletingOrder() =
      DeletingOrderProcessState;
  const factory ModifyPendingOrderProcessState.orderDeletionSuccess() =
      OrderDeletionSuccessProcessState;
  const factory ModifyPendingOrderProcessState.orderDeletionFailure() =
      OrderDeletionFailureProcessState;
  const factory ModifyPendingOrderProcessState.marketClosed() =
      OrderDeletionMarketClosedProcessState;
}
