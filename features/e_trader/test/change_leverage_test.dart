// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'get_change_leverage_test_data.dart';
import 'scenarios/change_leverage_success_scenario.dart';
import 'scenarios/change_leverage_failure_scenario.dart';
import 'package:equiti_test/equiti_test.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_runs_with_configuration.dart';
import 'package:bdd_widget_test/step/i_see_text.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_widget_test/step/i_tap_text.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Change Leverage''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testWidgets('''User sees a list of available leverages''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User sees a list of available leverages''');
        await theAppRunsWithConfiguration(
          tester,
          GetChangeLeverageTestData(),
          scenarios: [changeLeverageSuccessScenario],
        );
        await iSeeText(tester, "1:1");
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User sees a list of available leverages''', success);
      }
    });
    testGoldens('''Screenshot changeLeverage screen success''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Screenshot changeLeverage screen success''');
        await theAppIsRendered(
          tester,
          GetChangeLeverageTestData(),
          scenarios: [changeLeverageSuccessScenario],
        );
        await screenshotVerified(tester, 'change_leverage_success');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Screenshot changeLeverage screen success''',
          success,
        );
      }
    });
    testGoldens('''Screenshot changeLeverage screen failure''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Screenshot changeLeverage screen failure''');
        await theAppIsRendered(
          tester,
          GetChangeLeverageTestData(),
          scenarios: [changeLeverageFailureScenario],
        );
        await screenshotVerified(tester, 'change_leverage_failure');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''Screenshot changeLeverage screen failure''',
          success,
        );
      }
    });
    testGoldens('''User select on of the leverages''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select on of the leverages''');
        await theAppRunsWithConfiguration(
          tester,
          GetChangeLeverageTestData(),
          scenarios: [changeLeverageSuccessScenario],
        );
        await iTapText(tester, "1:1");
        await screenshotVerified(tester, 'leverage_selected');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select on of the leverages''', success);
      }
    });
    testGoldens(
      '''User select on of the leverages and press on Save to pop from screen''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''User select on of the leverages and press on Save to pop from screen''',
          );
          await theAppRunsWithConfiguration(
            tester,
            GetChangeLeverageTestData(),
            scenarios: [changeLeverageSuccessScenario],
          );
          await iTapText(tester, "1:1");
          await iTapIdentifier(tester, "text_selection_component_button");
          await screenshotVerified(tester, 'leverage_saved');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''User select on of the leverages and press on Save to pop from screen''',
            success,
          );
        }
      },
    );
  });
}
