import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

class CreateAccountBannerScreen extends StatelessWidget {
  const CreateAccountBannerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            flex: 1,
            child: DuploLottieView.asset(
              lightAnimation: onboarding.Assets.lotties.accountCreationBanner,
              darkAnimation: onboarding.Assets.lotties.accountCreationBanner,
              alignment: Alignment.topCenter,
              fit: BoxFit.contain,
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Spacer(flex: 1),
                DuploText(
                  text: localization.onboarding_createTradingAccount,
                  style: DuploTextStyles.of(context).textXl,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                SizedBox(height: 15),
                DuploText(
                  text: localization.onboarding_createTradingAccountDescription,
                  style: DuploTextStyles.of(context).textSm,
                  textAlign: TextAlign.center,
                  color: theme.text.textSecondary,
                ),
                Spacer(flex: 5),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: DuploButton.defaultPrimary(
                    semanticsIdentifier: "create_account_button",
                    title: localization.onboarding_continueButton,
                    isLoading: false,
                    trailingIcon: onboarding.Assets.images.continueIc.keyName,
                    onTap: () {
                      diContainer<OnboardingNavigation>()
                          .navigateToCreateAccountMain(
                            createAccountFlow:
                                CreateAccountFlow.firstLiveAccount,
                          );
                    },
                    useFullWidth: true,
                  ),
                ),
                Spacer(flex: 1),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
