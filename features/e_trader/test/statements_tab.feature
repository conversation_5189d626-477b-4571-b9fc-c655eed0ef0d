import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/statements_tab.dart';
import 'scenarios/statements_empty_scenario.dart';
import 'scenarios/statements_success_scenario.dart';

Feature: Statements Tab 

@testMethodName: testGoldens
Scenario Outline: User views Statements Tab
  Given The {StatementsTab()} app is rendered <scenario>
  And I wait
  And screenshot verified <golden_file_name>
  Examples:
  | scenario                                  | golden_file_name         |
  | scenarios: [statementsSuccessScenario]    | 'statements_success'     |
  | scenarios: [statementsEmptyScenario]      | 'statements_empty'       |

@testMethodName: testGoldens
Scenario: User select sort Statements
    Given The {StatementsTab()} app runs with configuration {scenarios:[statementsSuccessScenario]}
    And I wait
    Then i tap {"grouped_buttons_sort_statements"} identifier
    And I wait
    Then screenshot verified {'statements_sort'}

@testMethodName: testGoldens
Scenario: User select filter Statements
    Given The {StatementsTab()} app runs with configuration {scenarios:[statementsSuccessScenario]}
    And I wait
    Then i tap {"grouped_buttons_filter_statements"} identifier
    And I wait
    Then screenshot verified {'statements_filter'}

