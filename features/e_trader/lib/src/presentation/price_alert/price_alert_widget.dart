import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/widget/active_price_alerts_widget.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/widget/set_price_alert_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class PriceAlertWidget extends StatefulWidget {
  final String symbol;
  final String symbolImageUrl;

  PriceAlertWidget({
    Key? key,
    required this.symbol,
    required this.symbolImageUrl,
  }) : super(key: key);

  @override
  _PriceAlertWidgetState createState() => _PriceAlertWidgetState();
}

class _PriceAlertWidgetState extends State<PriceAlertWidget>
    with SingleTickerProviderStateMixin {
  bool _hideTabbar = false;
  late final TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging &&
          _currentIndex != _tabController.index) {
        setState(() {
          _currentIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);

    return DuploTabBar(
      hideTabbar: _hideTabbar,
      tabController: _tabController,
      tabTitles: [
        DuploTabBarTitle(
          text: l10n.trader_setAlert,
          semanticsIdentifier: 'set_alert_tab',
        ),
        DuploTabBarTitle(
          text: l10n.trader_activeAlerts,
          semanticsIdentifier: 'active_alerts_tab',
        ),
      ],
      isScrollable: false,
      tabViews: [
        SetPriceAlertWidget(
          symbol: widget.symbol,
          symbolImageUrl: widget.symbolImageUrl,
          onHideTabbar: (hide) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _hideTabbar = hide;
              });
            });
          },
        ),
        ActivePriceAlertsWidget(symbol: widget.symbol),
      ],
      isFlex: false,
    );
  }
}
