// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/statements_tab.dart';
import 'scenarios/statements_empty_scenario.dart';
import 'scenarios/statements_success_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import './step/the_app_runs_with_configuration.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Statements Tab''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: User views Statements Tab (scenarios: [statementsSuccessScenario], 'statements_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Statements Tab (scenarios: [statementsSuccessScenario], 'statements_success')''',
          );
          await theAppIsRendered(
            tester,
            StatementsTab(),
            scenarios: [statementsSuccessScenario],
          );
          await iWait(tester);
          await screenshotVerified(tester, 'statements_success');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Statements Tab (scenarios: [statementsSuccessScenario], 'statements_success')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Statements Tab (scenarios: [statementsEmptyScenario], 'statements_empty')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Statements Tab (scenarios: [statementsEmptyScenario], 'statements_empty')''',
          );
          await theAppIsRendered(
            tester,
            StatementsTab(),
            scenarios: [statementsEmptyScenario],
          );
          await iWait(tester);
          await screenshotVerified(tester, 'statements_empty');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Statements Tab (scenarios: [statementsEmptyScenario], 'statements_empty')''',
            success,
          );
        }
      },
    );
    testGoldens('''User select sort Statements''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select sort Statements''');
        await theAppRunsWithConfiguration(
          tester,
          StatementsTab(),
          scenarios: [statementsSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_sort_statements");
        await iWait(tester);
        await screenshotVerified(tester, 'statements_sort');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select sort Statements''', success);
      }
    });
    testGoldens('''User select filter Statements''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select filter Statements''');
        await theAppRunsWithConfiguration(
          tester,
          StatementsTab(),
          scenarios: [statementsSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_filter_statements");
        await iWait(tester);
        await screenshotVerified(tester, 'statements_filter');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select filter Statements''', success);
      }
    });
  });
}
