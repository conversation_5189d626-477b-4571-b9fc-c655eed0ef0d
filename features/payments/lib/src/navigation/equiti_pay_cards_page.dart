import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/deposit_payment_methods/equiti_pay_cards_screen.dart';
import 'package:payment/src/navigation/arguments/equiti_pay_cards_args.dart';

class EquitiPayCardsPage extends EquitiPage {
  const EquitiPayCardsPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> data,
  ) {
    final arguments = routeData.arguments as EquitiPayCardsArgs;
    return EquitiPayCardsScreen(
      url: arguments.url,
      title: arguments.title,
      transactionId: arguments.transactionId,
      accountNumber: arguments.accountNumber,
      maxPollingAttempts: arguments.maxPollingAttempts,
      depositFlowConfig: arguments.depositFlowConfig,
      pollingFrequencySeconds: arguments.pollingFrequencySeconds,
    );
  }

  @override
  String get label => PaymentRouteSchema.equitiPayCardsRoute.label;

  @override
  String get url => PaymentRouteSchema.equitiPayCardsRoute.url;
}
