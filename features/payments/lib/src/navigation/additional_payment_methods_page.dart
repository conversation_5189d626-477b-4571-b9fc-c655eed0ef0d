import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/navigation/arguments/additional_payment_methods_args.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/deposit_payment_methods/additional_payment_methods_screen.dart';

class AdditionalPaymentMethodsPage extends EquitiPage {
  const AdditionalPaymentMethodsPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    final arguments = routeData.arguments as AdditionalPaymentMethodsArgs;
    return AdditionalPaymentMethodsScreen(
      url: arguments.url,
      title: arguments.title,
      transactionId: arguments.transactionId,
      accountNumber: arguments.accountNumber,
      depositFlowConfig: arguments.depositFlowConfig,
      maxPollingAttempts: arguments.maxPollingAttempts,
      pollingFrequencySeconds: arguments.pollingFrequencySeconds,
    );
  }

  @override
  String get label => PaymentRouteSchema.additionalPaymentMethodsRoute.label;

  @override
  String get url => PaymentRouteSchema.additionalPaymentMethodsRoute.url;
}
