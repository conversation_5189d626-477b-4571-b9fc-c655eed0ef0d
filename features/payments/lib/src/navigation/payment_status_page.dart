import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/data/payment_status_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/payment_status/deposit_status_screen.dart';

class PaymentStatusPage extends EquitiPage {
  const PaymentStatusPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    return DepositStatusScreen(
      transactionId: globalData['transactionId'] as String,
      accountNumber: globalData['accountNumber'] as String,
      depositFlowConfig: globalData['depositFlowConfig'] as DepositFlowConfig,
      paymentStatus: globalData['paymentStatus'] as PaymentStatus?,
      maxPollingAttempts: globalData['maxPollingAttempts'] as int?,
      pollingFrequencySeconds: globalData['pollingFrequencySeconds'] as int?,
    );
  }

  @override
  String get label => PaymentRouteSchema.paymentStatusRoute.label;

  @override
  String get url => PaymentRouteSchema.paymentStatusRoute.url;
}
