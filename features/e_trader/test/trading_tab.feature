import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/trading_tab.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/closed_trade_details.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_filter_bottom_sheet.dart';
import 'scenarios/trading_empty_scenario.dart';
import 'scenarios/trading_success_scenario.dart';
import 'scenarios/trade_details_success_scenario.dart';
import 'package:e_trader/src/data/api/trading_response_model.dart';

Feature: Trading Tab 

@testMethodName: testGoldens
Scenario Outline: User views Trading Tab
  Given The {TradingTab()} app is rendered <scenario>
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
  | scenario                                  | golden_file_name                |
  | scenarios: [tradingSuccessScenario]       | 'trading_success_scenario'      |
  | scenarios: [tradingEmptyScenario]         | 'trading_empty_scenario'        |

@testMethodName: testGoldens
Scenario: User select sort Trading
  Given The {TradingTab()} app runs with configuration {scenarios:[tradingSuccessScenario]}
  And I wait 
  Then i tap {"grouped_buttons_sort"} identifier
  Then screenshot verified {'trading_sort_scenario'} with custom pump

@testMethodName: testGoldens
Scenario: User select filter Trading
  Given The {TradingTab()} app is rendered {scenarios:[tradingSuccessScenario]}
  And I wait 
  Then i tap {"grouped_buttons_filter"} identifier
  Then screenshot verified {'trading_filter_scenario'} with custom pump

@testMethodName: testGoldens
Scenario Outline: User views Trade Details
  Given The {ClosedTradeDetails(tradingItem: TradingListModel(activityType: "CloseTrade",activityTypeName: "Close profit",dateTime: DateTime.tryParse(""),id: 284979,tradeDetail: TradeDetailModel(tradeType: "Buy",symbol: "EURUSD",profit: 0,entry: "In",volume: 1000,openPrice: 4125.87,positionId: 465304,productName: "Euro vs US Dollar",friendlyName: "EURUSD",logoUrl: "https://eqdulcimer03z.blob.core.windows.net/public/logos%2Fa52a70e2-2917-442d-96c6-b7ffcfff7722_EURUSD.png",positionOpenPrice: 0,commission: 0,),),)} app is rendered <scenario>
  And I wait 
  Then screenshot verified <golden_file_name>
  Examples:
  | scenario                                  | golden_file_name                    |
  | scenarios: [tradeDetailsSuccessScenario]  | 'trade_details_success_scenario'    |