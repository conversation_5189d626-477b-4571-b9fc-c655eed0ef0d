part of 'events_bloc.dart';

@freezed
sealed class EventsState with _$EventsState {
  const factory EventsState({
    required EventsProcessState processState,
    @Default([]) List<EventItem> allEventItems,
    @Default([]) List<Object?> groupedEvents,
    @Default(false) bool hasReachedMax,
    @Default(10) int pageSize,
    @Default(0) int currentPage,
    @Default(0) int count,
    @Default('') String dateLabel,
    required NewsEventsSortOrder sortOrder,
  }) = _EventsState;
}

@freezed
sealed class EventsProcessState with _$EventsProcessState {
  const factory EventsProcessState.initial() = EventsInitial;
  const factory EventsProcessState.loading() = EventsLoading;
  const factory EventsProcessState.success() = EventsSuccess;
  const factory EventsProcessState.loadingMore() = EventsLoadingMore;
  const factory EventsProcessState.error(String message) = EventsError;
  const factory EventsProcessState.calendarError({
    required String message,
    required String title,
    required String eventId,
  }) = CalendarError;

  const factory EventsProcessState.calendarMessage({
    required String message,
    required String eventId,
  }) = EventsCalendarMessage;
}
