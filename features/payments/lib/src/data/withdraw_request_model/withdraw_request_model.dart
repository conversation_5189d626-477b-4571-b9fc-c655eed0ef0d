// To parse this JSON data, do
//
//     final withdrawRequestModel = withdrawRequestModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';

part 'withdraw_request_model.freezed.dart';
part 'withdraw_request_model.g.dart';

@freezed
abstract class WithdrawRequestModel with _$WithdrawRequestModel {
  const factory WithdrawRequestModel({
    @JsonKey(name: "paymentType") required String paymentType,
    @JsonKey(name: "accountInfo") required AccountInfo accountInfo,
    @Json<PERSON>ey(name: "amount") required Amount amount,
    @JsonKey(name: "metadata") required Metadata metadata,
  }) = _WithdrawRequestModel;

  factory WithdrawRequestModel.fromJson(Map<String, dynamic> json) =>
      _$WithdrawRequestModelFromJson(json);
}

@freezed
abstract class AccountInfo with _$AccountInfo {
  const factory AccountInfo({
    @JsonKey(name: "tradingAccountId") required String tradingAccountId,
    @Json<PERSON>ey(name: "accountCurrency") required String accountCurrency,
  }) = _AccountInfo;

  factory AccountInfo.fromJson(Map<String, dynamic> json) =>
      _$AccountInfoFromJson(json);
}

@freezed
abstract class Amount with _$Amount {
  const factory Amount({
    @JsonKey(name: "currency") required String currency,
    @JsonKey(name: "conversionRateString") required String conversionRateString,
    @JsonKey(name: "amount") required num amount,
    @JsonKey(name: "convertedAmount") required num convertedAmount,
    @JsonKey(name: "conversionRate") required num conversionRate,
  }) = _Amount;

  factory Amount.fromJson(Map<String, dynamic> json) => _$AmountFromJson(json);
}

// making sealed class Metadata to allow for different types of metadata
@freezed
sealed class Metadata with _$Metadata {
  const factory Metadata.card({@JsonKey(name: "card") required Card card}) =
      CardMetadata;
  const factory Metadata.skrill({
    @JsonKey(name: "skrill") required Skrill skrill,
  }) = SkrillMetadata;
  const factory Metadata.neteller({
    @JsonKey(name: "neteller") required Neteller neteller,
  }) = NetellerMetadata;
  const factory Metadata.bank({
    @JsonKey(name: "transferType") required String transferType,
    @JsonKey(name: "bankAccount") required BankAccount bankAccount,
  }) = BankMetadata;
  factory Metadata.fromJson(Map<String, dynamic> json) =>
      _$MetadataFromJson(json);
}

@freezed
sealed class BankAccount with _$BankAccount {
  const factory BankAccount({
    @JsonKey(name: "id", includeIfNull: false) String? id,
    @JsonKey(name: "country") required String country,
    @JsonKey(name: "accountHolder") required String accountHolder,
    @JsonKey(name: "bankName") required String bankName,
    @JsonKey(name: "accountNickname") required String accountNickname,
    @JsonKey(name: "branchName") required String branchName,
    @JsonKey(name: "swiftBic") required String swiftBic,
    @JsonKey(name: "iban") required String iban,
  }) = _BankAccount;

  factory BankAccount.fromJson(Map<String, dynamic> json) =>
      _$BankAccountFromJson(json);
}

@freezed
abstract class Card with _$Card {
  const factory Card({@JsonKey(name: "id") required String id}) = _Card;

  factory Card.fromJson(Map<String, dynamic> json) => _$CardFromJson(json);
}

@freezed
abstract class Skrill with _$Skrill {
  const factory Skrill({@JsonKey(name: "email") required String email}) =
      _Skrill;

  factory Skrill.fromJson(Map<String, dynamic> json) => _$SkrillFromJson(json);
}

@freezed
abstract class Neteller with _$Neteller {
  const factory Neteller({@JsonKey(name: "email") required String email}) =
      _Neteller;

  factory Neteller.fromJson(Map<String, dynamic> json) =>
      _$NetellerFromJson(json);
}
