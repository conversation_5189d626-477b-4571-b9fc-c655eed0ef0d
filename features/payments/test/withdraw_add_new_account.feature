import 'scenarios/withdraw_add_new_account_success.dart';
import 'scenarios/withdraw_add_new_account_failure.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/withdraw_add_new_bank.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';


Feature: Withdraw Add New Account Screen
  @testMethodName: testGoldens
  Scenario: Add new account
    Given The {  WithdrawAddNewBank(withdrawFlowParams: WithdrawFlowParams(tradingAccountId: '',paymentType: WithdrawalMop.cards,accountCurrency: '',currency: '',conversionRateString: '',amount: 0,convertedAmount: 0,conversionRate: 0,),)} app is rendered {scenarios:[withdrawAddNewAccountSuccess]}
    Then screenshot verified {'withdraw_add_new_account/step_1_loaded'} with custom pump

    