part of 'withdraw_bank_transfer_bloc.dart';

@freezed
sealed class WithdrawBankTransferEvent with _$WithdrawBankTransferEvent {
  const factory WithdrawBankTransferEvent.fetchBankAccounts(
    String tradingAccountId,
  ) = _FetchBankAccounts;
  const factory WithdrawBankTransferEvent.changeSelectedIndex(int index) =
      _ChangeSelectedIndex;
  const factory WithdrawBankTransferEvent.onPressContinue(
    WithdrawFlowParams bankTransferAmountModel,
  ) = _OnPressContinue;

  const factory WithdrawBankTransferEvent.onChangeMode(ScreenMode mode) =
      _OnChangeMode;

  const factory WithdrawBankTransferEvent.onDeleteBankAccount(
    BankAccountData bankAccountModel,
  ) = _OnDeleteBankAccount;
  const factory WithdrawBankTransferEvent.onAddNewAccountPressed(
    WithdrawFlowParams bankTransferAmountModel,
  ) = _OnAddNewAccountPressed;
}
