//@GeneratedMicroModule;ETraderPackageModule;package:e_trader/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:clock/clock.dart' as _i454;
import 'package:device_calendar/device_calendar.dart' as _i439;
import 'package:e_trader/src/data/api/document_contents_firestore.dart'
    as _i417;
import 'package:e_trader/src/data/api/legal_documents_api.dart' as _i1004;
import 'package:e_trader/src/data/socket/order_model.dart' as _i516;
import 'package:e_trader/src/data/socket/position_model.dart' as _i339;
import 'package:e_trader/src/di/equiti_trader_module.dart' as _i1049;
import 'package:e_trader/src/domain/repository/account_repository.dart'
    as _i863;
import 'package:e_trader/src/domain/repository/activity_repository.dart'
    as _i970;
import 'package:e_trader/src/domain/repository/broker_settings_repository.dart'
    as _i686;
import 'package:e_trader/src/domain/repository/calendar_repository.dart'
    as _i337;
import 'package:e_trader/src/domain/repository/change_leverage_repository.dart'
    as _i742;
import 'package:e_trader/src/domain/repository/create_wallet_repository.dart'
    as _i17;
import 'package:e_trader/src/domain/repository/events_news_repository.dart'
    as _i413;
import 'package:e_trader/src/domain/repository/funding_repository.dart'
    as _i213;
import 'package:e_trader/src/domain/repository/get_active_alert_repository.dart'
    as _i810;
import 'package:e_trader/src/domain/repository/historical_repository.dart'
    as _i285;
import 'package:e_trader/src/domain/repository/legal_document_repository.dart'
    as _i177;
import 'package:e_trader/src/domain/repository/margin_repository.dart' as _i140;
import 'package:e_trader/src/domain/repository/margin_requirment_hub_repository.dart'
    as _i198;
import 'package:e_trader/src/domain/repository/market_hours_repository.dart'
    as _i804;
import 'package:e_trader/src/domain/repository/order_repository.dart' as _i434;
import 'package:e_trader/src/domain/repository/position_repository.dart'
    as _i1005;
import 'package:e_trader/src/domain/repository/price_alert_repository.dart'
    as _i22;
import 'package:e_trader/src/domain/repository/product_detail_info_repository.dart'
    as _i211;
import 'package:e_trader/src/domain/repository/register_push_notification_token_repository.dart'
    as _i400;
import 'package:e_trader/src/domain/repository/reset_balance_repository.dart'
    as _i890;
import 'package:e_trader/src/domain/repository/statements_repository.dart'
    as _i1024;
import 'package:e_trader/src/domain/repository/switch_account_repository.dart'
    as _i863;
import 'package:e_trader/src/domain/repository/symbol_quote_repository.dart'
    as _i598;
import 'package:e_trader/src/domain/repository/symbol_repository.dart' as _i625;
import 'package:e_trader/src/domain/repository/trader_account_preferences_repository.dart'
    as _i339;
import 'package:e_trader/src/domain/repository/trading_chart_repository.dart'
    as _i970;
import 'package:e_trader/src/domain/repository/watchlist_repository.dart'
    as _i856;
import 'package:e_trader/src/domain/repository/withdraw_repository.dart'
    as _i994;
import 'package:e_trader/src/domain/usecase/add_watchlist_use_case.dart'
    as _i37;
import 'package:e_trader/src/domain/usecase/broker_setting_currencies_use_case.dart'
    as _i660;
import 'package:e_trader/src/domain/usecase/calculate_partial_close_profit_use_case.dart'
    as _i419;
import 'package:e_trader/src/domain/usecase/calculate_volume_use_case.dart'
    as _i308;
import 'package:e_trader/src/domain/usecase/calendar_use_case.dart' as _i938;
import 'package:e_trader/src/domain/usecase/change_account_password_use_case.dart'
    as _i41;
import 'package:e_trader/src/domain/usecase/change_leverage_use_case.dart'
    as _i105;
import 'package:e_trader/src/domain/usecase/check_symbol_watchlist_use_case.dart'
    as _i906;
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart' as _i764;
import 'package:e_trader/src/domain/usecase/create_order_use_case.dart'
    as _i938;
import 'package:e_trader/src/domain/usecase/create_trade_use_case.dart'
    as _i408;
import 'package:e_trader/src/domain/usecase/create_wallet_use_case.dart'
    as _i403;
import 'package:e_trader/src/domain/usecase/deal_by_id_use_case.dart' as _i136;
import 'package:e_trader/src/domain/usecase/delete_order_use_case.dart'
    as _i357;
import 'package:e_trader/src/domain/usecase/delete_price_alert_use_case.dart'
    as _i662;
import 'package:e_trader/src/domain/usecase/events_news_local_data_use_case.dart'
    as _i334;
import 'package:e_trader/src/domain/usecase/events_use_case.dart' as _i641;
import 'package:e_trader/src/domain/usecase/fetch_all_watchlist_use_case.dart'
    as _i30;
import 'package:e_trader/src/domain/usecase/filter_transfer_destination_accounts_use_case.dart'
    as _i3;
import 'package:e_trader/src/domain/usecase/filter_transfer_source_accounts_use_case.dart'
    as _i181;
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart'
    as _i593;
import 'package:e_trader/src/domain/usecase/get_account_type_use_case.dart'
    as _i113;
import 'package:e_trader/src/domain/usecase/get_active_alert_use_case.dart'
    as _i447;
import 'package:e_trader/src/domain/usecase/get_broker_settings_use_case.dart'
    as _i618;
import 'package:e_trader/src/domain/usecase/get_category_use_case.dart'
    as _i147;
import 'package:e_trader/src/domain/usecase/get_demo_trading_accounts_use_case.dart'
    as _i999;
import 'package:e_trader/src/domain/usecase/get_funding_use_case.dart' as _i665;
import 'package:e_trader/src/domain/usecase/get_historical_profit_use_case.dart'
    as _i645;
import 'package:e_trader/src/domain/usecase/get_historical_volume_use_case.dart'
    as _i975;
import 'package:e_trader/src/domain/usecase/get_language_model_from_language_code_usecase.dart'
    as _i294;
import 'package:e_trader/src/domain/usecase/get_legal_documents_use_case.dart'
    as _i1063;
import 'package:e_trader/src/domain/usecase/get_leverage_use_case.dart'
    as _i345;
import 'package:e_trader/src/domain/usecase/get_market_timing_calculator_use_case.dart'
    as _i303;
import 'package:e_trader/src/domain/usecase/get_news_use_case.dart' as _i458;
import 'package:e_trader/src/domain/usecase/get_office_code_use_case.dart'
    as _i956;
import 'package:e_trader/src/domain/usecase/get_platform_password_use_case.dart'
    as _i351;
import 'package:e_trader/src/domain/usecase/get_product_holidays_use_case.dart'
    as _i935;
import 'package:e_trader/src/domain/usecase/get_product_sessions_use_case.dart'
    as _i429;
import 'package:e_trader/src/domain/usecase/get_risk_percentage_use_case.dart'
    as _i755;
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart'
    as _i450;
import 'package:e_trader/src/domain/usecase/get_selected_language_code_use_case.dart'
    as _i762;
import 'package:e_trader/src/domain/usecase/get_statements_use_case.dart'
    as _i89;
import 'package:e_trader/src/domain/usecase/get_symbols_use_case.dart' as _i635;
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart'
    as _i633;
import 'package:e_trader/src/domain/usecase/get_trading_accounts_use_case.dart'
    as _i194;
import 'package:e_trader/src/domain/usecase/get_trading_chart_use_case.dart'
    as _i270;
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart'
    as _i40;
import 'package:e_trader/src/domain/usecase/get_watchlist_data_use_case.dart'
    as _i465;
import 'package:e_trader/src/domain/usecase/historical_performance_preferences_use_case.dart'
    as _i600;
import 'package:e_trader/src/domain/usecase/modify_order_use_case.dart'
    as _i1051;
import 'package:e_trader/src/domain/usecase/modify_price_alert_use_case.dart'
    as _i1047;
import 'package:e_trader/src/domain/usecase/modify_trade_use_case.dart'
    as _i586;
import 'package:e_trader/src/domain/usecase/product_detail_info_usecase.dart'
    as _i1035;
import 'package:e_trader/src/domain/usecase/push_notification_token_use_case.dart'
    as _i933;
import 'package:e_trader/src/domain/usecase/remove_watchlist_use_case.dart'
    as _i865;
import 'package:e_trader/src/domain/usecase/reset_balance_use_case.dart'
    as _i725;
import 'package:e_trader/src/domain/usecase/save_account_number_and_is_demo_use_case.dart'
    as _i622;
import 'package:e_trader/src/domain/usecase/save_close_positions_dialog_use_case.dart'
    as _i856;
import 'package:e_trader/src/domain/usecase/save_price_alert_use_case.dart'
    as _i163;
import 'package:e_trader/src/domain/usecase/save_selected_account_use_case.dart'
    as _i373;
import 'package:e_trader/src/domain/usecase/save_trading_preferences_use_case.dart'
    as _i835;
import 'package:e_trader/src/domain/usecase/should_show_close_positions_dialog_use_case.dart'
    as _i261;
import 'package:e_trader/src/domain/usecase/subscribe_to_grouped_positions_use_case.dart'
    as _i900;
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart'
    as _i454;
import 'package:e_trader/src/domain/usecase/subscribe_to_margin_requirment_use_case.dart'
    as _i943;
import 'package:e_trader/src/domain/usecase/subscribe_to_orders_use_case.dart'
    as _i966;
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart'
    as _i579;
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart'
    as _i998;
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart'
    as _i98;
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart'
    as _i291;
import 'package:e_trader/src/domain/usecase/trading_activity_use_case.dart'
    as _i938;
import 'package:e_trader/src/domain/usecase/update_account_details_use_case.dart'
    as _i252;
import 'package:e_trader/src/domain/usecase/update_active_alerts_hub_use_case.dart'
    as _i246;
import 'package:e_trader/src/domain/usecase/update_margin_requirment_use_case.dart'
    as _i733;
import 'package:e_trader/src/domain/usecase/update_order_hub_use_case.dart'
    as _i635;
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart'
    as _i149;
import 'package:e_trader/src/domain/usecase/update_quotes_by_symbols_use_case.dart'
    as _i197;
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart'
    as _i500;
import 'package:e_trader/src/domain/usecase/validate_margin_allocation_use_case.dart'
    as _i308;
import 'package:e_trader/src/domain/usecase/watchlist_local_cache_use_case.dart'
    as _i661;
import 'package:e_trader/src/domain/usecase/withdraw_use_case.dart' as _i410;
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart'
    as _i1056;
import 'package:e_trader/src/presentation/account/bloc/account_details_bloc.dart'
    as _i1038;
import 'package:e_trader/src/presentation/change_account_password/bloc/change_account_password_bloc.dart'
    as _i246;
import 'package:e_trader/src/presentation/change_leverage/bloc/change_leverage_bloc.dart'
    as _i325;
import 'package:e_trader/src/presentation/create_new_wallet/bloc/create_new_wallet_bloc.dart'
    as _i331;
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart'
    as _i288;
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_bloc.dart'
    as _i849;
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/input_order_size_bloc.dart'
    as _i328;
import 'package:e_trader/src/presentation/create_trade/bloc/market_order/market_order_bloc.dart'
    as _i248;
import 'package:e_trader/src/presentation/create_trade/bloc/modify_pending_order/bloc/modify_pending_order_bloc.dart'
    as _i426;
import 'package:e_trader/src/presentation/create_trade/bloc/modify_trade/bloc/modify_trade_bloc.dart'
    as _i161;
import 'package:e_trader/src/presentation/create_trade/bloc/order_limit/order_limit_bloc.dart'
    as _i15;
import 'package:e_trader/src/presentation/create_trade/bloc/pending_order/pending_order_bloc.dart'
    as _i945;
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart'
    as _i371;
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart'
    as _i712;
import 'package:e_trader/src/presentation/create_trade/widgets/market_order_widget.dart'
    as _i961;
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart'
    as _i516;
import 'package:e_trader/src/presentation/create_trade/widgets/pending_order_widget.dart'
    as _i2;
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart'
    as _i716;
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart'
    as _i272;
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart'
    as _i256;
import 'package:e_trader/src/presentation/legal_documents/bloc/legal_documents_bloc.dart'
    as _i772;
import 'package:e_trader/src/presentation/market_hours/bloc/market_hours_bloc.dart'
    as _i963;
import 'package:e_trader/src/presentation/model/order_limit_config.dart'
    as _i364;
import 'package:e_trader/src/presentation/more/trading_settings/bloc/trading_settings_bloc.dart'
    as _i964;
import 'package:e_trader/src/presentation/navigation_bottom_bar/account_balance/bloc/account_balance_bloc.dart'
    as _i829;
import 'package:e_trader/src/presentation/navigation_bottom_bar/bloc/navigation_bottom_bar_bloc.dart'
    as _i108;
import 'package:e_trader/src/presentation/partial_close/bloc/partial_close_bloc.dart'
    as _i803;
import 'package:e_trader/src/presentation/performance_screen/bloc/performance_bloc.dart'
    as _i726;
import 'package:e_trader/src/presentation/performance_screen/funding_tab/bloc/funding_bloc.dart'
    as _i64;
import 'package:e_trader/src/presentation/performance_screen/statements_tab/bloc/statements_bloc.dart'
    as _i779;
import 'package:e_trader/src/presentation/performance_screen/trading_tab/bloc/trading_bloc.dart'
    as _i48;
import 'package:e_trader/src/presentation/portfolio/insights/bloc/insights_bloc.dart'
    as _i867;
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart'
    as _i869;
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart'
    as _i122;
import 'package:e_trader/src/presentation/position_option/bloc/position_option_bloc.dart'
    as _i126;
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/bloc/active_price_alerts_bloc.dart'
    as _i116;
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart'
    as _i1014;
import 'package:e_trader/src/presentation/price_alert/set_price_alert/bloc/set_price_alert_bloc.dart'
    as _i875;
import 'package:e_trader/src/presentation/product_detail_overview/bloc/product_detail_overview_bloc.dart'
    as _i747;
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart'
    as _i870;
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart'
    as _i88;
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart'
    as _i115;
import 'package:e_trader/src/presentation/switch_account/rename_account/bloc/rename_account_bloc.dart'
    as _i53;
import 'package:e_trader/src/presentation/switch_account/wallet_details/bloc/wallet_details_bloc.dart'
    as _i369;
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart'
    as _i807;
import 'package:e_trader/src/presentation/symbols/bloc/symbols/symbols_bloc.dart'
    as _i921;
import 'package:e_trader/src/presentation/trade_options_view/bloc/trade_options_bloc.dart'
    as _i321;
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart'
    as _i820;
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/bloc/watchlisted_symbol_indicator_bloc.dart'
    as _i508;
import 'package:injectable/injectable.dart' as _i526;
import 'package:locale_manager/locale_manager.dart' as _i385;
import 'package:monitoring/monitoring.dart' as _i472;
import 'package:preferences/preferences.dart' as _i695;
import 'package:socket_client/socket_client.dart' as _i688;
import 'package:user_account/user_account.dart' as _i43;
import 'package:validator/validator.dart' as _i268;

class ETraderPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final equitiTraderModule = _$EquitiTraderModule();
    gh.factory<_i762.GetSelectedLanguageCodeUseCase>(
      () => equitiTraderModule.getSelectedLanguageCodeUseCase(),
    );
    gh.factory<_i308.CalculateVolumeUseCase>(
      () => equitiTraderModule.calculateVolumeUseCase(),
    );
    gh.factory<_i181.FilterTransferSourceAccountsUseCase>(
      () => equitiTraderModule.getTransferSourceAccountUseCase(),
    );
    gh.factory<_i3.FilterTransferDestinationAccountsUseCase>(
      () => equitiTraderModule.getTransferDestinationAccountUseCase(),
    );
    gh.factory<_i755.GetRiskPercentageUseCase>(
      () => equitiTraderModule.getRiskPercentageUseCase(),
    );
    gh.factory<_i1014.EditPriceAlertBloc>(
      () => equitiTraderModule.editPriceAlertBloc(),
    );
    gh.factory<_i633.GetTradeSizeFromVolumeUseCase>(
      () => equitiTraderModule.getTradeSizeFromVolumeUseCase(),
    );
    gh.factory<_i419.CalculatePartialCloseProfitUseCase>(
      () => equitiTraderModule.calculatePartialCloseProfitUseCase(),
    );
    gh.factory<_i294.GetLanguageModelFromLanguageCodeUsecase>(
      () => equitiTraderModule.getLanguageModelFromLanguageCodeUsecase(),
    );
    gh.factory<_i439.DeviceCalendarPlugin>(
      () => equitiTraderModule.deviceCalendarPlugin(),
    );
    gh.factory<_i351.GetPlatformPasswordUseCase>(
      () => equitiTraderModule.getPlatformPasswordUseCase(),
    );
    gh.factory<_i660.BrokerSettingCurrenciesUseCase>(
      () => equitiTraderModule.brokerSettingCurrenciesUseCase(),
    );
    gh.singleton<_i268.InputValidator>(() => equitiTraderModule.inputValidator);
    gh.factory<_i1004.LegalDocumentsApi>(
      () => equitiTraderModule.legalDocumentsApi(
        gh<_i633.ApiClientBase>(),
        gh<_i417.DocumentContentsFirestore>(),
      ),
    );
    gh.factory<_i434.OrderRepository>(
      () => equitiTraderModule.orderRepository(
        gh<_i633.ApiClientBase>(),
        gh<_i688.SocketClient>(),
      ),
    );
    gh.factory<_i1005.PositionRepository>(
      () => equitiTraderModule.positionRepository(
        gh<_i633.ApiClientBase>(),
        gh<_i688.SocketClient>(),
      ),
    );
    gh.factory<_i863.SwitchAccountRepository>(
      () => equitiTraderModule.switchAccountRepository(
        gh<_i633.ApiClientBase>(),
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i213.FundingRepository>(
      () => equitiTraderModule.fundingRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i194.GetTradingAccountsUseCase>(
      () => equitiTraderModule.getTradingAccountsUseCase(
        gh<_i863.SwitchAccountRepository>(),
      ),
    );
    gh.factory<_i252.UpdateAccountDetailsUseCase>(
      () => equitiTraderModule.updateAccountDetailsUseCase(
        gh<_i863.SwitchAccountRepository>(),
      ),
    );
    gh.factory<_i938.CreateOrderUseCase>(
      () => equitiTraderModule.createOrderUseCase(gh<_i434.OrderRepository>()),
    );
    gh.factory<_i966.SubscribeToOrdersUseCase>(
      () => equitiTraderModule.subscribeToOrdersUseCase(
        gh<_i434.OrderRepository>(),
      ),
    );
    gh.factory<_i1051.ModifyOrderUseCase>(
      () => equitiTraderModule.modifyOrderUseCase(gh<_i434.OrderRepository>()),
    );
    gh.factory<_i357.DeleteOrderUseCase>(
      () => equitiTraderModule.deleteOrderUseCase(gh<_i434.OrderRepository>()),
    );
    gh.factory<_i863.AccountRepository>(
      () => equitiTraderModule.getAccountRepository(
        gh<_i633.ApiClientBase>(),
        gh<_i688.SocketClient>(),
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i665.GetFundingUseCase>(
      () => equitiTraderModule.getFundingUseCase(gh<_i213.FundingRepository>()),
    );
    gh.factory<_i999.GetDemoTradingAccountsUseCase>(
      () => equitiTraderModule.getLiveTradingAccountsUseCase(
        gh<_i194.GetTradingAccountsUseCase>(),
      ),
    );
    gh.factory<_i53.RenameAccountBloc>(
      () => equitiTraderModule.renameAccountBloc(
        gh<_i252.UpdateAccountDetailsUseCase>(),
      ),
    );
    gh.factory<_i890.ResetBalanceRepository>(
      () =>
          equitiTraderModule.resetBalanceRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i994.WithdrawRepository>(
      () => equitiTraderModule.withdrawRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i686.BrokerSettingsRepository>(
      () => equitiTraderModule.getBrokerSettingRepository(
        gh<_i633.ApiClientBase>(),
      ),
    );
    gh.factory<_i625.SymbolRepository>(
      () => equitiTraderModule.getSymbolRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i970.TradingChartRepository>(
      () =>
          equitiTraderModule.tradingChartRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i804.MarketHoursRepository>(
      () => equitiTraderModule.marketHoursRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i211.ProductDetailInfoRepository>(
      () => equitiTraderModule.productInfoRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i856.WatchlistRepository>(
      () => equitiTraderModule.watchlistRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i22.PriceAlertRepository>(
      () => equitiTraderModule.priceAlertRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i742.ChangeLeverageRepository>(
      () => equitiTraderModule.changeLeverageRepository(
        gh<_i633.ApiClientBase>(),
      ),
    );
    gh.factory<_i1024.StatementsRepository>(
      () => equitiTraderModule.statementsRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i413.EventsNewsRepository>(
      () => equitiTraderModule.eventsRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i970.ActivityRepository>(
      () => equitiTraderModule.activityRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i285.HistoricalRepository>(
      () => equitiTraderModule.historicalRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i308.ValidateMarginAllocationUseCase>(
      () => equitiTraderModule.validateMarginAllocationUseCase(
        gh<_i268.InputValidator>(),
      ),
    );
    gh.factory<_i618.GetBrokerSettingsUseCase>(
      () => equitiTraderModule.getBrokerSettingsUseCase(
        gh<_i686.BrokerSettingsRepository>(),
      ),
    );
    gh.factory<_i408.CreateTradeUseCase>(
      () => equitiTraderModule.createTradeUseCase(
        gh<_i1005.PositionRepository>(),
      ),
    );
    gh.factory<_i900.SubscribeToGroupedPositionsUseCase>(
      () => equitiTraderModule.subscribeToGroupedPositionsUseCase(
        gh<_i1005.PositionRepository>(),
      ),
    );
    gh.factory<_i764.CloseTradeUseCase>(
      () =>
          equitiTraderModule.closeTradeUseCase(gh<_i1005.PositionRepository>()),
    );
    gh.factory<_i586.ModifyTradeUseCase>(
      () => equitiTraderModule.modifyTradeUseCase(
        gh<_i1005.PositionRepository>(),
      ),
    );
    gh.factory<_i373.SaveSelectedAccountUseCase>(
      () => equitiTraderModule.saveSelectedAccountUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factoryParam<_i15.OrderLimitBloc, _i364.OrderLimitConfig, bool>(
      (config, isOrderLimitEnabled) =>
          equitiTraderModule.orderLimitBloc(config, isOrderLimitEnabled),
    );
    gh.factoryParam<_i328.InputOrderSizeBloc, _i712.OrderSizeArgs, dynamic>(
      (args, _) => equitiTraderModule.inputOrderSizeBloc(args),
    );
    gh.factory<_i147.GetCategoryUseCase>(
      () => equitiTraderModule.getCategoryUseCase(
        gh<_i625.SymbolRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i598.SymbolQuoteRepository>(
      () => equitiTraderModule.symbolQuoteRepository(gh<_i688.SocketClient>()),
    );
    gh.factory<_i140.MarginRepository>(
      () => equitiTraderModule.marginRepository(gh<_i688.SocketClient>()),
    );
    gh.factory<_i334.EventsNewsLocalDataUseCase>(
      () => equitiTraderModule.eventsNewsLocalDataUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i400.RegisterPushNotificationTokenRepository>(
      () => equitiTraderModule.registerPushNotificationTokenRepository(
        gh<_i633.ApiClientBase>(),
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factoryParam<_i248.MarketOrderBloc, _i961.MarketOrderArgs, dynamic>(
      (args, _) => equitiTraderModule.marketOrderBloc(
        args,
        gh<_i408.CreateTradeUseCase>(),
        gh<_i308.CalculateVolumeUseCase>(),
        gh<_i308.ValidateMarginAllocationUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
      ),
    );
    gh.factory<_i1035.ProductDetailInfoUseCase>(
      () => equitiTraderModule.productInfoUseCase(
        gh<_i211.ProductDetailInfoRepository>(),
      ),
    );
    gh.factory<_i291.SymbolLocalDataUseCase>(
      () => equitiTraderModule.symbolLocalDataUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i661.WatchlistLocalCacheUseCase>(
      () => equitiTraderModule.watchlistLocalCacheUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i600.HistoricalPerformancePreferencesUseCase>(
      () => equitiTraderModule.historicalPerformanceUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i369.WalletDetailsBloc>(
      () => equitiTraderModule.walletDetailsBloc(
        gh<_i665.GetFundingUseCase>(),
        gh<_i454.Clock>(),
        gh<_i1056.EquitiTraderNavigation>(),
      ),
    );
    gh.factory<_i17.CreateWalletRepository>(
      () => equitiTraderModule.createWalletRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i938.TradingActivityUseCase>(
      () => equitiTraderModule.tradingActivityUseCase(
        gh<_i970.ActivityRepository>(),
      ),
    );
    gh.factory<_i136.DealByIdUseCase>(
      () => equitiTraderModule.dealByIdUseCase(gh<_i970.ActivityRepository>()),
    );
    gh.factory<_i177.LegalDocumentRepository>(
      () => equitiTraderModule.legalDocumentRepository(
        gh<_i1004.LegalDocumentsApi>(),
      ),
    );
    gh.factory<_i450.GetSelectedAccountUseCase>(
      () => equitiTraderModule.getSelectedAccountUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i98.SubscribeToTradingAccountBalanceUseCase>(
      () => equitiTraderModule.getTradingAccountUseCase(
        gh<_i140.MarginRepository>(),
      ),
    );
    gh.factory<_i198.MarginRequirmentHubRepository>(
      () => equitiTraderModule.marginRequirmentRepository(
        gh<_i688.SocketClient>(),
      ),
    );
    gh.factory<_i810.GetActiveAlertRepository>(
      () =>
          equitiTraderModule.getActiveAlertRepository(gh<_i688.SocketClient>()),
    );
    gh.factory<_i725.ResetBalanceUseCase>(
      () => equitiTraderModule.resetBalanceUseCase(
        gh<_i890.ResetBalanceRepository>(),
      ),
    );
    gh.factoryParam<_i945.PendingOrderBloc, _i2.PendingOrderArgs, dynamic>(
      (args, _) => equitiTraderModule.pendingOrderBloc(
        args,
        gh<_i938.CreateOrderUseCase>(),
        gh<_i308.CalculateVolumeUseCase>(),
        gh<_i308.ValidateMarginAllocationUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
      ),
    );
    gh.factory<_i454.SubscribeToListOfSymbolQuotesUseCase>(
      () => equitiTraderModule.getProductsBySymbolUseCase(
        gh<_i598.SymbolQuoteRepository>(),
      ),
    );
    gh.factory<_i89.GetStatementsUseCase>(
      () => equitiTraderModule.getStatementsUseCase(
        gh<_i1024.StatementsRepository>(),
      ),
    );
    gh.factory<_i807.CategoriesBloc>(
      () => equitiTraderModule.categoriesBloc(
        gh<_i147.GetCategoryUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factory<_i645.GetHistoricalProfitUseCase>(
      () => equitiTraderModule.getHistoricalProfitUseCase(
        gh<_i285.HistoricalRepository>(),
      ),
    );
    gh.factory<_i975.GetHistoricalVolumeUseCase>(
      () => equitiTraderModule.getHistoricalVolumeUseCase(
        gh<_i285.HistoricalRepository>(),
      ),
    );
    gh.factory<_i933.PushNotificationTokenUseCase>(
      () => equitiTraderModule.pushNotificationTokenUseCase(
        gh<_i400.RegisterPushNotificationTokenRepository>(),
      ),
    );
    gh.factory<_i622.SaveAccountNumberAndIsDemoUseCase>(
      () => equitiTraderModule.saveAccountNumberAndIsDemoUseCase(
        gh<_i863.AccountRepository>(),
      ),
    );
    gh.factory<_i41.ChangeAccountPasswordUseCase>(
      () => equitiTraderModule.changePasswordUseCase(
        gh<_i863.AccountRepository>(),
      ),
    );
    gh.factory<_i641.EventsUseCase>(
      () => equitiTraderModule.eventsUseCase(gh<_i413.EventsNewsRepository>()),
    );
    gh.factory<_i458.GetNewsUseCase>(
      () => equitiTraderModule.getNewsUseCase(gh<_i413.EventsNewsRepository>()),
    );
    gh.factory<_i593.GetAccountNumberUseCase>(
      () => equitiTraderModule.getAccountNumberUseCase(
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i726.PerformanceBloc>(
      () => equitiTraderModule.performanceBloc(
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i747.ProductDetailOverviewBloc>(
      () => equitiTraderModule.productOverviewBloc(
        gh<_i1035.ProductDetailInfoUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i779.StatementsBloc>(
      () => equitiTraderModule.statementsBloc(
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i89.GetStatementsUseCase>(),
        gh<_i454.Clock>(),
      ),
    );
    gh.factory<_i64.FundingBloc>(
      () => equitiTraderModule.fundingBloc(
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i665.GetFundingUseCase>(),
        gh<_i454.Clock>(),
      ),
    );
    gh.factory<_i943.SubscribeToMarginRequirmentUseCase>(
      () => equitiTraderModule.subscribeToMarginRequirmentUseCase(
        gh<_i198.MarginRequirmentHubRepository>(),
      ),
    );
    gh.factory<_i733.UpdateMarginRequirmentUseCase>(
      () => equitiTraderModule.updateMarginRequirmentUseCase(
        gh<_i198.MarginRequirmentHubRepository>(),
      ),
    );
    gh.factory<_i429.GetProductSessionsUseCase>(
      () => equitiTraderModule.getProductSessionsUseCase(
        gh<_i804.MarketHoursRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i935.GetProductHolidaysUseCase>(
      () => equitiTraderModule.getMarketHoursUseCase(
        gh<_i804.MarketHoursRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factoryParam<_i272.NewsBloc, String?, dynamic>(
      (_tickerName, _) => equitiTraderModule.newsBloc(
        gh<_i458.GetNewsUseCase>(),
        gh<_i334.EventsNewsLocalDataUseCase>(),
        gh<_i385.LocaleManager>(),
        _tickerName,
      ),
    );
    gh.factory<_i88.ResetBalanceBloc>(
      () => equitiTraderModule.resetBalanceBloc(
        gh<_i725.ResetBalanceUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factory<_i337.CalendarRepository>(
      () => equitiTraderModule.calendarService(
        gh<_i334.EventsNewsLocalDataUseCase>(),
        gh<_i439.DeviceCalendarPlugin>(),
      ),
    );
    gh.factory<_i579.SubscribeToPositionsUseCase>(
      () => equitiTraderModule.subscribeToPositionsUseCase(
        gh<_i1005.PositionRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i149.UpdatePositionsUseCase>(
      () => equitiTraderModule.unsubscribeFromPositionsUseCase(
        gh<_i1005.PositionRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i270.GetTradingChartUseCase>(
      () => equitiTraderModule.getTradingChartUseCase(
        gh<_i970.TradingChartRepository>(),
      ),
    );
    gh.factory<_i1063.GetLegalDocumentsUseCase>(
      () => equitiTraderModule.getLegalDocumentUseCase(
        gh<_i177.LegalDocumentRepository>(),
        gh<_i762.GetSelectedLanguageCodeUseCase>(),
      ),
    );
    gh.factory<_i803.PartialCloseBloc>(
      () => equitiTraderModule.partialCloseBloc(
        gh<_i579.SubscribeToPositionsUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i764.CloseTradeUseCase>(),
        gh<_i419.CalculatePartialCloseProfitUseCase>(),
        gh<_i633.GetTradeSizeFromVolumeUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i998.SubscribeToSymbolQuotesUseCase>(
      () => equitiTraderModule.subscribeToSymbolQuotesUseCase(
        gh<_i598.SymbolQuoteRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i197.UpdateQuotesBySymbolsUseCase>(
      () => equitiTraderModule.updateQuotesBySymbolsUseCase(
        gh<_i598.SymbolQuoteRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factoryParam<_i426.ModifyPendingOrderBloc, _i516.OrderModel, dynamic>(
      (orderModel, _) => equitiTraderModule.modifyPendingOrderBloc(
        orderModel,
        gh<_i966.SubscribeToOrdersUseCase>(),
        gh<_i1051.ModifyOrderUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i943.SubscribeToMarginRequirmentUseCase>(),
        gh<_i357.DeleteOrderUseCase>(),
      ),
    );
    gh.factory<_i163.SavePriceAlertUseCase>(
      () => equitiTraderModule.saveProceAlertUseCase(
        gh<_i22.PriceAlertRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i339.TraderAccountPreferencesRepository>(
      () => equitiTraderModule.traderAccountPreferencesRepository(
        gh<_i695.EquitiPreferences>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i1047.ModifyPriceAlertUseCase>(
      () => equitiTraderModule.getModifyPriceAlertUseCase(
        gh<_i22.PriceAlertRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i662.DeletePriceAlertUseCase>(
      () => equitiTraderModule.deletePriceAlertUseCase(
        gh<_i22.PriceAlertRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i30.FetchAllWatchlistUseCase>(
      () => equitiTraderModule.fetchAllWatchlistUseCase(
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i856.WatchlistRepository>(),
      ),
    );
    gh.factoryParam<_i288.CreateTradeBloc, _i371.CreateTradeArgs, dynamic>(
      (args, _) => equitiTraderModule.createTradeBloc(
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i943.SubscribeToMarginRequirmentUseCase>(),
        gh<_i998.SubscribeToSymbolQuotesUseCase>(),
        gh<_i308.CalculateVolumeUseCase>(),
        args,
        gh<_i472.LoggerBase>(),
        gh<_i733.UpdateMarginRequirmentUseCase>(),
      ),
    );
    gh.factory<_i48.TradingBloc>(
      () => equitiTraderModule.tradingBloc(
        gh<_i454.Clock>(),
        gh<_i136.DealByIdUseCase>(),
        gh<_i938.TradingActivityUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i403.CreateWalletUseCase>(
      () => equitiTraderModule.createWalletUseCase(
        gh<_i17.CreateWalletRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factoryParam<_i161.ModifyTradeBloc, _i339.PositionModel, dynamic>(
      (positionModel, _) => equitiTraderModule.modifyTradeBloc(
        gh<_i579.SubscribeToPositionsUseCase>(),
        gh<_i586.ModifyTradeUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i943.SubscribeToMarginRequirmentUseCase>(),
        positionModel,
      ),
    );
    gh.factory<_i938.CalendarUseCase>(
      () => equitiTraderModule.calendarUseCase(gh<_i337.CalendarRepository>()),
    );
    gh.factory<_i303.GetMarketTimingCalculatorUseCase>(
      () => equitiTraderModule.getMarketTimingCalculatorUseCase(
        gh<_i429.GetProductSessionsUseCase>(),
        gh<_i935.GetProductHolidaysUseCase>(),
        gh<_i454.Clock>(),
      ),
    );
    gh.factory<_i108.NavigationBottomBarBloc>(
      () => equitiTraderModule.navigationBottomBarBloc(
        gh<_i30.FetchAllWatchlistUseCase>(),
        gh<_i661.WatchlistLocalCacheUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i472.LoggerBase>(),
        gh<_i1056.EquitiTraderNavigation>(),
      ),
    );
    gh.factoryParam<_i321.TradeOptionsBloc, String, dynamic>(
      (positionId, _) => equitiTraderModule.tradeOptionsBloc(
        positionId,
        gh<_i579.SubscribeToPositionsUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i633.GetTradeSizeFromVolumeUseCase>(),
        gh<_i764.CloseTradeUseCase>(),
      ),
    );
    gh.factory<_i246.ChangeAccountPasswordBloc>(
      () => equitiTraderModule.changeAccountPasswordBloc(
        gh<_i41.ChangeAccountPasswordUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factory<_i410.WithdrawUseCase>(
      () => equitiTraderModule.withdrawUseCase(
        gh<_i994.WithdrawRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i113.GetAccountTypeUseCase>(),
      ),
    );
    gh.factory<_i40.GetTradingPreferencesUseCase>(
      () => equitiTraderModule.getTradingPreferencesUseCase(
        gh<_i339.TraderAccountPreferencesRepository>(),
      ),
    );
    gh.factory<_i835.SaveTradingPreferencesUseCase>(
      () => equitiTraderModule.saveTradingPreferencesUseCase(
        gh<_i339.TraderAccountPreferencesRepository>(),
      ),
    );
    gh.factory<_i122.PositionBloc>(
      () => equitiTraderModule.positionBloc(
        gh<_i579.SubscribeToPositionsUseCase>(),
        gh<_i149.UpdatePositionsUseCase>(),
        gh<_i472.LoggerBase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i870.ProductDetailsBloc>(
      () => equitiTraderModule.productDetailswBloc(
        gh<_i998.SubscribeToSymbolQuotesUseCase>(),
      ),
    );
    gh.factory<_i875.SetPriceAlertBloc>(
      () => equitiTraderModule.setPriceAlertBloc(
        gh<_i163.SavePriceAlertUseCase>(),
        gh<_i1047.ModifyPriceAlertUseCase>(),
        gh<_i998.SubscribeToSymbolQuotesUseCase>(),
        gh<_i662.DeletePriceAlertUseCase>(),
      ),
    );
    gh.factory<_i500.UpdateTradingAccountBalanceHubUseCase>(
      () => equitiTraderModule.updateTradingAccountBalanceHubUseCase(
        gh<_i140.MarginRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i635.UpdateOrderHubUseCase>(
      () => equitiTraderModule.updateOrderHubUseCase(
        gh<_i434.OrderRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i906.CheckSymbolWatchlistUseCase>(
      () => equitiTraderModule.checkSymbolWatchlistUseCase(
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i625.SymbolRepository>(),
      ),
    );
    gh.factory<_i256.HistoricalPerformanceBloc>(
      () => equitiTraderModule.historicalPerformanceBloc(
        gh<_i645.GetHistoricalProfitUseCase>(),
        gh<_i975.GetHistoricalVolumeUseCase>(),
        gh<_i600.HistoricalPerformancePreferencesUseCase>(),
      ),
    );
    gh.factory<_i820.TradingChartViewBloc>(
      () => equitiTraderModule.tradingChartViewBloc(
        gh<_i472.LoggerBase>(),
        gh<_i695.EquitiPreferences>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i270.GetTradingChartUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
        gh<_i998.SubscribeToSymbolQuotesUseCase>(),
      ),
    );
    gh.factory<_i105.ChangeLeverageUseCase>(
      () => equitiTraderModule.changeLeverageUseCase(
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i742.ChangeLeverageRepository>(),
      ),
    );
    gh.factory<_i447.GetActiveAlertUseCase>(
      () => equitiTraderModule.getActiveAlertUseCase(
        gh<_i810.GetActiveAlertRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i246.UpdateActiveAlertsHubUseCase>(
      () => equitiTraderModule.updateActiveAlertsHubUseCase(
        gh<_i810.GetActiveAlertRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i635.GetSymbolsUseCase>(
      () => equitiTraderModule.getSymbolsUseCase(
        gh<_i863.AccountRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i37.AddWatchlistUseCase>(
      () => equitiTraderModule.addWatchlistUseCase(
        gh<_i856.WatchlistRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i865.RemoveWatchlistUseCase>(
      () => equitiTraderModule.removeWatchlistUseCase(
        gh<_i856.WatchlistRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i465.GetWatchlistDataUseCase>(
      () => equitiTraderModule.getWatchlistData(
        gh<_i856.WatchlistRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
      ),
    );
    gh.factory<_i345.GetLeverageUseCase>(
      () => equitiTraderModule.getLeverageUseCase(
        gh<_i742.ChangeLeverageRepository>(),
        gh<_i686.BrokerSettingsRepository>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i956.GetOfficeCodeUseCase>(),
      ),
    );
    gh.factory<_i964.TradingSettingsBloc>(
      () => equitiTraderModule.tradingSettingsBloc(
        gh<_i40.GetTradingPreferencesUseCase>(),
        gh<_i835.SaveTradingPreferencesUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i331.CreateNewWalletBloc>(
      () => equitiTraderModule.createNewWalletBloc(
        gh<_i403.CreateWalletUseCase>(),
        gh<_i660.BrokerSettingCurrenciesUseCase>(),
      ),
    );
    gh.factory<_i829.AccountBalanceBloc>(
      () => equitiTraderModule.accountBalanceBloc(
        gh<_i98.SubscribeToTradingAccountBalanceUseCase>(),
        gh<_i500.UpdateTradingAccountBalanceHubUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i261.ShouldShowClosePositionsDialogUseCase>(
      () => equitiTraderModule.shouldShowClosePositionsDialogUseCase(
        gh<_i339.TraderAccountPreferencesRepository>(),
      ),
    );
    gh.factory<_i856.SaveClosePositionsDialogUseCase>(
      () => equitiTraderModule.saveClosePositionsDialogUseCase(
        gh<_i339.TraderAccountPreferencesRepository>(),
      ),
    );
    gh.factory<_i772.LegalDocumentsBloc>(
      () => equitiTraderModule.legalDocumentsBloc(
        gh<_i1063.GetLegalDocumentsUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factoryParam<_i849.OrderPriceBloc, _i516.OrderPriceArgs, dynamic>(
      (args, _) => equitiTraderModule.inputOrderPriceBloc(
        gh<_i998.SubscribeToSymbolQuotesUseCase>(),
        args,
      ),
    );
    gh.factory<_i115.AccountsBloc>(
      () => equitiTraderModule.accountsBloc(
        gh<_i194.GetTradingAccountsUseCase>(),
        gh<_i373.SaveSelectedAccountUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
        gh<_i98.SubscribeToTradingAccountBalanceUseCase>(),
        gh<_i500.UpdateTradingAccountBalanceHubUseCase>(),
        gh<_i835.SaveTradingPreferencesUseCase>(),
      ),
    );
    gh.factory<_i867.InsightsBloc>(
      () => equitiTraderModule.insightsBloc(
        gh<_i98.SubscribeToTradingAccountBalanceUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i500.UpdateTradingAccountBalanceHubUseCase>(),
      ),
    );
    gh.factory<_i869.OrdersBloc>(
      () => equitiTraderModule.ordersBloc(
        gh<_i966.SubscribeToOrdersUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i635.UpdateOrderHubUseCase>(),
      ),
    );
    gh.factoryParam<_i716.EventsBloc, String?, dynamic>(
      (_tickerName, _) => equitiTraderModule.eventsBloc(
        gh<_i641.EventsUseCase>(),
        gh<_i334.EventsNewsLocalDataUseCase>(),
        gh<_i938.CalendarUseCase>(),
        _tickerName,
      ),
    );
    gh.factory<_i963.MarketHoursBloc>(
      () => equitiTraderModule.marketHours(
        gh<_i303.GetMarketTimingCalculatorUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factory<_i1038.AccountDetailsBloc>(
      () => equitiTraderModule.accountDetailsBloc(
        gh<_i450.GetSelectedAccountUseCase>(),
        gh<_i98.SubscribeToTradingAccountBalanceUseCase>(),
        gh<_i500.UpdateTradingAccountBalanceHubUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factory<_i116.ActivePriceAlertsBloc>(
      () => equitiTraderModule.activePriceAlerts(
        gh<_i447.GetActiveAlertUseCase>(),
        gh<_i246.UpdateActiveAlertsHubUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
    gh.factoryParam<_i325.ChangeLeverageBloc, int?, dynamic>(
      (leverage, _) => equitiTraderModule.changeLeverageBloc(
        gh<_i345.GetLeverageUseCase>(),
        gh<_i105.ChangeLeverageUseCase>(),
        gh<_i472.LoggerBase>(),
        gh<_i40.GetTradingPreferencesUseCase>(),
        gh<_i835.SaveTradingPreferencesUseCase>(),
        leverage,
      ),
    );
    gh.factory<_i126.PositionOptionBloc>(
      () => equitiTraderModule.positionOptionBloc(
        gh<_i900.SubscribeToGroupedPositionsUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
        gh<_i261.ShouldShowClosePositionsDialogUseCase>(),
        gh<_i856.SaveClosePositionsDialogUseCase>(),
        gh<_i764.CloseTradeUseCase>(),
        gh<_i450.GetSelectedAccountUseCase>(),
      ),
    );
    gh.factory<_i921.SymbolsBloc>(
      () => equitiTraderModule.symbolsBloc(
        gh<_i635.GetSymbolsUseCase>(),
        gh<_i472.LoggerBase>(),
        gh<_i454.SubscribeToListOfSymbolQuotesUseCase>(),
        gh<_i197.UpdateQuotesBySymbolsUseCase>(),
        gh<_i1056.EquitiTraderNavigation>(),
        gh<_i291.SymbolLocalDataUseCase>(),
        gh<_i593.GetAccountNumberUseCase>(),
        gh<_i465.GetWatchlistDataUseCase>(),
      ),
    );
    gh.factory<_i508.WatchlistedSymbolIndicatorBloc>(
      () => equitiTraderModule.watchlistedSymbolIndicatorBloc(
        gh<_i37.AddWatchlistUseCase>(),
        gh<_i865.RemoveWatchlistUseCase>(),
        gh<_i906.CheckSymbolWatchlistUseCase>(),
        gh<_i661.WatchlistLocalCacheUseCase>(),
        gh<_i472.LoggerBase>(),
      ),
    );
  }
}

class _$EquitiTraderModule extends _i1049.EquitiTraderModule {}
