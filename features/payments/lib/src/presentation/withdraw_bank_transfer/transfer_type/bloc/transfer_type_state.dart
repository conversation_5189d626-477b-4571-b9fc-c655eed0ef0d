part of 'transfer_type_bloc.dart';

@freezed
sealed class TransferTypeState with _$TransferTypeState {
  const factory TransferTypeState({
    String? transferType,
    String? clientAccountId,
    @Default(TransferTypeProcessState.loaded())
    TransferTypeProcessState processState,
    @Default(false) bool isRequestWithdrawButtonLoading,
    @Default(false) bool hasInsufficientFunds, // Add validation fields
    @Default('') String insufficientFundsMessage,
    num? withdrawalFees, // Store current fees for validation
    // Store validation data for localized error message generation
    num? validationWithdrawalAmount,
    num? validationAccountBalance,
    num? validationWithdrawalFees,
    String? validationCurrency,
  }) = _TransferTypeState;
}

@freezed
sealed class TransferTypeProcessState with _$TransferTypeProcessState {
  const factory TransferTypeProcessState.loaded() = TransferTypeLoadedState;
  const factory TransferTypeProcessState.success({
    required WithdrawStatusType status,
    String? operationId,
  }) = TransferTypeSuccessState;
  const factory TransferTypeProcessState.error({required String message}) =
      TransferTypeErrorState;
}
