// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_flow_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WithdrawFlowParams _$WithdrawFlowParamsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_WithdrawFlowParams', json, ($checkedConvert) {
      final val = _WithdrawFlowParams(
        tradingAccountId: $checkedConvert(
          'tradingAccountId',
          (v) => v as String,
        ),
        paymentType: $checkedConvert(
          'paymentType',
          (v) => $enumDecode(_$WithdrawalMopEnumMap, v),
        ),
        accountCurrency: $checkedConvert('accountCurrency', (v) => v as String),
        currency: $checkedConvert('currency', (v) => v as String),
        conversionRateString: $checkedConvert(
          'conversionRateString',
          (v) => v as String,
        ),
        amount: $checkedConvert('amount', (v) => v as num),
        convertedAmount: $checkedConvert('convertedAmount', (v) => v as num),
        conversionRate: $checkedConvert('conversionRate', (v) => v as num),
        accountBalance: $checkedConvert('accountBalance', (v) => v as num),
      );
      return val;
    });

Map<String, dynamic> _$WithdrawFlowParamsToJson(_WithdrawFlowParams instance) =>
    <String, dynamic>{
      'tradingAccountId': instance.tradingAccountId,
      'paymentType': _$WithdrawalMopEnumMap[instance.paymentType]!,
      'accountCurrency': instance.accountCurrency,
      'currency': instance.currency,
      'conversionRateString': instance.conversionRateString,
      'amount': instance.amount,
      'convertedAmount': instance.convertedAmount,
      'conversionRate': instance.conversionRate,
      'accountBalance': instance.accountBalance,
    };

const _$WithdrawalMopEnumMap = {
  WithdrawalMop.cards: 'cards',
  WithdrawalMop.skrill: 'skrill',
  WithdrawalMop.neteller: 'neteller',
  WithdrawalMop.bank: 'bank',
};
