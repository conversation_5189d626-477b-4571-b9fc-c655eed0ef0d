// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:api_client/api_client.dart' as _i633;
import 'package:broker_settings/broker_settings.dart' as _i389;
import 'package:clock/clock.dart' as _i454;
import 'package:domain/domain.dart' as _i494;
import 'package:duplo/duplo.dart' as _i323;
import 'package:e_trader/fusion.dart' as _i665;
import 'package:equiti_auth/equiti_auth.dart' as _i313;
import 'package:equiti_router/equiti_router.dart' as _i955;
import 'package:flutter/widgets.dart' as _i718;
import 'package:get_it/get_it.dart' as _i174;
import 'package:host/host.dart' as _i1035;
import 'package:injectable/injectable.dart' as _i526;
import 'package:login/login.dart' as _i944;
import 'package:monitoring/monitoring.dart' as _i472;
import 'package:onboarding/onboarding.dart' as _i706;
import 'package:payment/payments.dart' as _i702;
import 'package:socket_client/socket_client.dart' as _i688;
import 'package:trader/di/app_module.dart' as _i567;
import 'package:user_account/user_account.dart' as _i43;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    await _i472.MonitoringPackageModule().init(gh);
    final appModule = _$AppModule();
    gh.singleton<_i706.OnboardingRouteLocation>(
      () => appModule.onboardingRouteLocation(),
    );
    gh.lazySingleton<_i454.Clock>(() => appModule.clock);
    gh.lazySingleton<_i665.GetOfficeCodeUseCase>(
      () => appModule.getOfficeCodeUseCase,
    );
    gh.lazySingleton<_i718.GlobalKey<_i718.NavigatorState>>(
      () => appModule.navigatorKey,
    );
    gh.lazySingleton<_i944.LoginNavigation>(() => appModule.loginNavigation());
    gh.singleton<_i688.LoggerInterceptor>(
      () => appModule.loggerInterceptor(gh<_i472.LoggerBase>()),
    );
    gh.singleton<_i688.MockSocketInterceptor>(
      () => appModule.mockSocketInterceptor(gh<_i472.LoggerBase>()),
    );
    gh.factory<_i665.DocumentContentsFirestore>(
      () => appModule.documentContentsFirestore,
    );
    gh.lazySingleton<_i706.OnboardingNavigation>(
      () => appModule.onboardingNavigation(
        gh<_i718.GlobalKey<_i718.NavigatorState>>(),
      ),
    );
    gh.singleton<String>(
      () => appModule.socketBaseUrl,
      instanceName: 'SocketBaseUrl',
    );
    gh.singleton<String>(
      () => appModule.mobileBffUrl,
      instanceName: 'mobileBffUrl',
    );
    gh.lazySingleton<_i955.EquitiNavigatorBase>(
      () => appModule.equitiPlatformNavigator(
        gh<_i706.OnboardingRouteLocation>(),
      ),
    );
    gh.lazySingleton<_i665.GetAccountTypeUseCase>(
      () => appModule.getAccountType,
    );
    gh.lazySingleton<_i665.GetAccountCurrencyUseCase>(
      () => appModule.getAccountCurrencyUseCase,
    );
    gh.lazySingleton<_i702.PaymentNavigation>(
      () => appModule.paymentNavigation(
        gh<_i718.GlobalKey<_i718.NavigatorState>>(),
      ),
    );
    gh.lazySingleton<_i665.EquitiTraderNavigation>(
      () => appModule.withdrawNavigation(
        gh<_i718.GlobalKey<_i718.NavigatorState>>(),
      ),
    );
    gh.singleton<_i688.SocketClient>(
      () => appModule.socketClient(
        gh<_i472.LoggerBase>(),
        gh<_i688.MockSocketInterceptor>(),
        gh<String>(instanceName: 'SocketBaseUrl'),
      ),
    );
    gh.lazySingleton<_i633.DioBuilder>(
      () => appModule.mobileBffDioBuilder(
        gh<_i472.PrettyDioLogger>(),
        gh<String>(instanceName: 'mobileBffUrl'),
        gh<_i313.AuthInterceptor>(),
        gh<_i633.MockApiInterceptor>(),
      ),
      instanceName: 'mobileBffDioBuilder',
    );
    gh.lazySingleton<_i633.ApiClientBase>(
      () => appModule.mobileBffApiClient(
        gh<_i633.DioBuilder>(instanceName: 'mobileBffDioBuilder'),
      ),
      instanceName: 'mobileBffApiClient',
    );
    await _i43.UserAccountPackageModule().init(gh);
    await _i389.BrokerSettingsPackageModule().init(gh);
    await _i1035.HostPackageModule().init(gh);
    await _i665.ETraderPackageModule().init(gh);
    await _i944.LoginPackageModule().init(gh);
    await _i323.DuploPackageModule().init(gh);
    await _i706.OnboardingPackageModule().init(gh);
    await _i702.PaymentPackageModule().init(gh);
    await _i494.DomainPackageModule().init(gh);
    return this;
  }
}

class _$AppModule extends _i567.AppModule {}
