import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/portfolio/insights/bloc/insights_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/insights/insights_screen.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/orders/orders_tab.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/positions_tab.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/trades_tab.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/widget/active_price_alerts_widget.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PortfolioPositionScreen extends StatelessWidget {
  const PortfolioPositionScreen({super.key, this.tabIndex = 0});
  final int tabIndex;

  @override
  Widget build(BuildContext context) => MultiBlocProvider(
    providers: [
      BlocProvider(create: (_) => diContainer<PositionBloc>()),
      BlocProvider(create: (_) => diContainer<OrdersBloc>()),
      BlocProvider(create: (_) => diContainer<InsightsBloc>()),
      BlocProvider(
        create:
            (_) =>
                diContainer<CategoriesBloc>()
                  ..add(CategoriesEvent.onGetCategories()),
      ),
    ],
    child: _PortfolioPositionContent(tabIndex: tabIndex),
  );
}

class _PortfolioPositionContent extends StatefulWidget {
  const _PortfolioPositionContent({this.tabIndex = 0});

  final int tabIndex;

  @override
  State<_PortfolioPositionContent> createState() =>
      _PortfolioPositionContentState();
}

class _PortfolioPositionContentState extends State<_PortfolioPositionContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 5,
      vsync: this,
      initialIndex: widget.tabIndex,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    final duploTextStyles = context.duploTextStyles;

    return Scaffold(
      backgroundColor: theme.background.bgSecondary,
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            dividerColor: theme.border.borderSecondary,
            indicatorColor: theme.foreground.fgBrandPrimaryAlt,
            labelColor: theme.text.textBrandSecondary,
            labelStyle: TextStyle(
              fontSize: duploTextStyles.textSm.fontSize,
              fontWeight: DuploFontWeight.semiBold.value,
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            unselectedLabelColor: theme.text.textQuaternary,
            splashFactory: null,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            tabAlignment: TabAlignment.start,
            unselectedLabelStyle: TextStyle(
              fontSize: duploTextStyles.textSm.fontSize,
              fontWeight: DuploFontWeight.medium.value,
            ),
            isScrollable: true,
            tabs: [
              Semantics(
                identifier: 'margin_tab',
                child: Tab(text: localization.trader_margin),
              ),
              Semantics(
                identifier: 'positions_tab',
                child: Tab(text: localization.trader_positions),
              ),
              Semantics(
                identifier: 'portfolio_tab',
                child: Tab(text: localization.trader_portfolio_trades),
              ),
              Semantics(
                identifier: 'orders_tab',
                child: Tab(text: localization.trader_orders),
              ),
              Semantics(
                identifier: 'alerts_tab',
                child: Tab(text: localization.trader_alerts),
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                InsightsScreen(),
                PositionsTab(),
                TradesTab(),
                OrdersTab(),
                ActivePriceAlertsWidget(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
