import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/navigation/arguments/payment_method_args.dart';
import 'package:payment/src/navigation/arguments/transfer_type_page_arguments.dart';
import 'package:payment/src/navigation/arguments/withdraw_bank_transfer_arguments.dart';
import 'package:trader/di/di_initializer.dart';

class PaymentNavigationImpl extends PaymentNavigation {
  PaymentNavigationImpl({required this.navigatorKey});
  final GlobalKey<NavigatorState> navigatorKey;

  @override
  void goToEquitiPayCardsScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) => EquitiPayCardsScreen(
              url: url,
              title: title,
              transactionId: transactionId,
              accountNumber: accountNumber,
              depositFlowConfig: depositFlowConfig,
              maxPollingAttempts: maxPollingAttempts,
              pollingFrequencySeconds: pollingFrequencySeconds,
            ),
      ),
    );
  }

  @override
  void goToDepositSelectAccountAndAmountScreen(
    DepositPaymentMethod method, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositSelectAccountAndAmountRoute.label,
      arguments: DepositPaymentMethodArgs(
        method: method,
        maxPollingAttempts: maxPollingAttempts,
        pollingFrequencySeconds: pollingFrequencySeconds,
        depositFlowConfig: depositFlowConfig,
      ),
    );
  }

  @override
  void goToPaymentStatusScreen({
    required String transactionId,
    required String accountNumber,
    required DepositFlowConfig depositFlowConfig,
    PaymentStatus? paymentStatus,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) => DepositStatusScreen(
              transactionId: transactionId,
              accountNumber: accountNumber,
              depositFlowConfig: depositFlowConfig,
              paymentStatus: paymentStatus,
              maxPollingAttempts: maxPollingAttempts,
              pollingFrequencySeconds: pollingFrequencySeconds,
            ),
      ),
    );
  }

  @override
  void goToFormBuilder() {
    // TODO: implement goToFormBuilder
  }

  @override
  void goToPaymentOptionsScreen({
    required DepositFlowConfig depositFlowConfig,
  }) {
    // TODO: implement goToPaymentOptionsScreen
  }
  void goToWithdrawSelectAccountAndAmountScreen({
    bool replace = false,
    required WithdrawalPaymentMethod method,
    String? account,
    WithdrawCard? selectedCard,
  }) {
    if (replace) {
      navigatorKey.currentState!.pushReplacement(
        MaterialPageRoute<void>(
          builder:
              (context) => WithdrawAccountsAndAmountScreen(
                paymentMethod: method,
                account: account,
                selectedCard: selectedCard,
              ),
        ),
      );
    } else {
      navigatorKey.currentState!.push(
        MaterialPageRoute<void>(
          builder:
              (context) => WithdrawAccountsAndAmountScreen(
                paymentMethod: method,
                account: account,
                selectedCard: selectedCard,
              ),
        ),
      );
    }
  }

  @override
  void goToPaymentNotAvailableYetPage() {
    // TODO: implement goToPaymentNotAvailableYetPage
  }

  @override
  void goToDepositSelectBankScreen(DepositPaymentMethodGroup methodGroup) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder:
            (context) =>
                DepositSelectBankScreen(paymentMethodGroup: methodGroup),
      ),
    );
  }

  @override
  void goToDespositBankDetailsScreen(Bank bank) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder: (context) => DepositBankDetailsScreen(bank: bank),
      ),
    );
  }

  @override
  void goToWithdrawCardPage(WithdrawalPaymentMethod method) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder: (context) => WithdrawCardPage(paymentMethod: method),
      ),
    );
  }

  @override
  void goToWithdrawAddNewBankPage(WithdrawFlowParams withdrawFlowParams) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawAddNewBankRoute.label,
      data: {"withdrawFlowParams": withdrawFlowParams},
    );
  }

  @override
  void goToAdditionalPaymentScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) => AdditionalPaymentMethodsScreen(
              url: url,
              title: title,
              transactionId: transactionId,
              accountNumber: accountNumber,
              depositFlowConfig: depositFlowConfig,
              maxPollingAttempts: maxPollingAttempts,
              pollingFrequencySeconds: pollingFrequencySeconds,
            ),
      ),
    );
  }

  @override
  void goBackToDepositPaymentOptionsScreen() {
    navigatorKey.currentState!.popUntil(
      (route) =>
          route.settings.name == PaymentRouteSchema.depositOptionsRoute.label,
    );
  }

  @override
  void goBackToDepositSelectAccountAndAmountScreen() {
    navigatorKey.currentState!.popUntil(
      (route) =>
          route.settings.name ==
          PaymentRouteSchema.depositSelectAccountAndAmountRoute.label,
    );
  }

  void goToWithdrawSkrillAndNetellerScreen(WithdrawalPaymentMethod method) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder: (context) => WithdrawSkrillAndNetellerScreen(method: method),
      ),
    );
  }

  @override
  void goToTransferTypeScreen(TransferTypePageArguments arguments) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder: (context) {
          return TransferTypeScreen(
            bank: arguments.bank,
            bankTransferAmountModel: arguments.bankTransferAmountModel,
          );
        },
      ),
    );
  }

  @override
  void goToWithdrawBankTransferScreen(
    WithdrawBankTransferArguments withdrawBankTransferArguments,
  ) {
    navigatorKey.currentState!.push(
      MaterialPageRoute<void>(
        builder: (context) {
          return WithdrawBankTransfer(
            tradingAccountId: withdrawBankTransferArguments.tradingAccountId,
            bankTransferAmountModel:
                withdrawBankTransferArguments.bankTransferAmountModel,
          );
        },
      ),
    );
  }

  void goToWithdrawNewBankUploadDocScreen({
    required String operationId,
    required String tradingAccountId,
    bool replace = false,
  }) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        PaymentRouteSchema.withdrawNewBankUploadDocRoute.label,
        data: {
          "operationId": operationId,
          "tradingAccountId": tradingAccountId,
        },
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        PaymentRouteSchema.withdrawNewBankUploadDocRoute.label,
        data: {
          "operationId": operationId,
          "tradingAccountId": tradingAccountId,
        },
      );
    }
  }

  @override
  void goToTransferFundsDestSelectionScreen(Account account) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) => TransferFundsDestSelectionScreen(account: account),
      ),
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) =>
                CreateAccountMainScreen(createAccountFlow: createAccountFlow),
      ),
    );
  }

  @override
  void goToTransferFundsScreen() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(builder: (context) => TransferFundsScreen()),
    );
  }

  @override
  void goToWithdrawStatusScreen({
    bool replace = false,
    VoidCallback? onContinue,
    WithdrawStatusType? status,
    String? popUntilRoute,
  }) {
    if (replace) {
      navigatorKey.currentState!.pushReplacement(
        MaterialPageRoute<void>(
          builder:
              (context) => WithdrawStatusScreen.fromWithDrawStatus(
                statusType: status,
                onButtonPressed: onContinue,
                popUntilRoute: popUntilRoute,
              ),
        ),
      );
    } else {
      navigatorKey.currentState!.push(
        MaterialPageRoute<void>(
          builder:
              (context) => WithdrawStatusScreen.fromWithDrawStatus(
                statusType: status,
                onButtonPressed: onContinue,
                popUntilRoute: popUntilRoute,
              ),
        ),
      );
    }
  }

  @override
  void goBackToSwitchAccounts() {
    navigatorKey.currentState!.popUntil(
      (route) =>
          route.settings.name == EquitiTraderRouteSchema.navBarRoute.label,
    );
    navigatorKey.currentState!.pushReplacement(
      MaterialPageRoute<void>(builder: (context) => SwitchAccountScreen()),
    );
  }

  @override
  void popUntilRoute(String routeLabel, {bool inclusive = false}) {
    navigatorKey.currentState!.popUntil(
      (route) => route.settings.name == routeLabel,
    );

    // If inclusive is true, pop one more time to remove the target route as well
    if (inclusive && navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop();
    }
  }

  @override
  void removeAllAndNavigateToHub() {
    // TODO: implement removeAllAndNavigateToHub
  }
}
