// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'equiti_pay_cards_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$EquitiPayCardsArgs {

 String get url; String get title; String get transactionId; String get accountNumber; DepositFlowConfig get depositFlowConfig; num? get maxPollingAttempts; num? get pollingFrequencySeconds;
/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EquitiPayCardsArgsCopyWith<EquitiPayCardsArgs> get copyWith => _$EquitiPayCardsArgsCopyWithImpl<EquitiPayCardsArgs>(this as EquitiPayCardsArgs, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EquitiPayCardsArgs&&(identical(other.url, url) || other.url == url)&&(identical(other.title, title) || other.title == title)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,url,title,transactionId,accountNumber,depositFlowConfig,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'EquitiPayCardsArgs(url: $url, title: $title, transactionId: $transactionId, accountNumber: $accountNumber, depositFlowConfig: $depositFlowConfig, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class $EquitiPayCardsArgsCopyWith<$Res>  {
  factory $EquitiPayCardsArgsCopyWith(EquitiPayCardsArgs value, $Res Function(EquitiPayCardsArgs) _then) = _$EquitiPayCardsArgsCopyWithImpl;
@useResult
$Res call({
 String url, String title, String transactionId, String accountNumber, DepositFlowConfig depositFlowConfig, num? maxPollingAttempts, num? pollingFrequencySeconds
});


$DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class _$EquitiPayCardsArgsCopyWithImpl<$Res>
    implements $EquitiPayCardsArgsCopyWith<$Res> {
  _$EquitiPayCardsArgsCopyWithImpl(this._self, this._then);

  final EquitiPayCardsArgs _self;
  final $Res Function(EquitiPayCardsArgs) _then;

/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? url = null,Object? title = null,Object? transactionId = null,Object? accountNumber = null,Object? depositFlowConfig = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_self.copyWith(
url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}
/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}


/// @nodoc


class _EquitiPayCardsArgs implements EquitiPayCardsArgs {
  const _EquitiPayCardsArgs({required this.url, required this.title, required this.transactionId, required this.accountNumber, required this.depositFlowConfig, this.maxPollingAttempts, this.pollingFrequencySeconds});
  

@override final  String url;
@override final  String title;
@override final  String transactionId;
@override final  String accountNumber;
@override final  DepositFlowConfig depositFlowConfig;
@override final  num? maxPollingAttempts;
@override final  num? pollingFrequencySeconds;

/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EquitiPayCardsArgsCopyWith<_EquitiPayCardsArgs> get copyWith => __$EquitiPayCardsArgsCopyWithImpl<_EquitiPayCardsArgs>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EquitiPayCardsArgs&&(identical(other.url, url) || other.url == url)&&(identical(other.title, title) || other.title == title)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,url,title,transactionId,accountNumber,depositFlowConfig,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'EquitiPayCardsArgs(url: $url, title: $title, transactionId: $transactionId, accountNumber: $accountNumber, depositFlowConfig: $depositFlowConfig, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class _$EquitiPayCardsArgsCopyWith<$Res> implements $EquitiPayCardsArgsCopyWith<$Res> {
  factory _$EquitiPayCardsArgsCopyWith(_EquitiPayCardsArgs value, $Res Function(_EquitiPayCardsArgs) _then) = __$EquitiPayCardsArgsCopyWithImpl;
@override @useResult
$Res call({
 String url, String title, String transactionId, String accountNumber, DepositFlowConfig depositFlowConfig, num? maxPollingAttempts, num? pollingFrequencySeconds
});


@override $DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class __$EquitiPayCardsArgsCopyWithImpl<$Res>
    implements _$EquitiPayCardsArgsCopyWith<$Res> {
  __$EquitiPayCardsArgsCopyWithImpl(this._self, this._then);

  final _EquitiPayCardsArgs _self;
  final $Res Function(_EquitiPayCardsArgs) _then;

/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? url = null,Object? title = null,Object? transactionId = null,Object? accountNumber = null,Object? depositFlowConfig = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_EquitiPayCardsArgs(
url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}

/// Create a copy of EquitiPayCardsArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

// dart format on
