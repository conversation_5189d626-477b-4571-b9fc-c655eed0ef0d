import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/add_bank_account_button.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_account_list.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/continue_cancel_button.dart';

class BankAccountSuccessWidget extends StatelessWidget {
  const BankAccountSuccessWidget({
    super.key,
    required this.bankTransferAmountModel,
  });

  final WithdrawFlowParams bankTransferAmountModel;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);

    return Padding(
      padding: const EdgeInsets.all(18.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DuploText(
            text: EquitiLocalization.of(context).payments_saved_bank_accounts,
            color: theme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
            style: DuploTextStyles.of(context).textXl,
          ),
          const SizedBox(height: 10),
          const BankAccountList(),
          const SizedBox(height: 10),
          AddBankAccountButton(
            bankTransferAmountModel: bankTransferAmountModel,
          ),
          const Spacer(),
          ContinueCancelButton(
            bankTransferAmountModel: bankTransferAmountModel,
          ),
        ],
      ),
    );
  }
}
