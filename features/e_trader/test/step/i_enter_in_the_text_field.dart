import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Usage: I enter {"100"} in the {"Amount"} text field
Future<void> iEnterInTheTextField(
  WidgetTester tester,
  String value,
  String identifier,
) async {
  // Find the field directly by semantics identifier
  final textField = find.bySemanticsIdentifier(identifier);

  expect(
    textField,
    findsOneWidget,
    reason: 'Could not find text field with identifier: $identifier',
  );

  // Enter the text
  await tester.enterText(textField, value);
  await tester.pump();
}
