import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

class DuploVerificationCodeInput extends StatelessWidget {
  const DuploVerificationCodeInput({
    super.key,
    this.label,
    this.hintText,
    required this.controller,
    this.onChanged,
    this.onCompleted,
    this.onSubmitted,
    this.autoFocus,
    this.centerPinput = false,
    this.errorText,
  });
  final String? label, hintText;
  final TextEditingController controller;
  final void Function(String)? onChanged, onCompleted, onSubmitted;
  final bool? autoFocus;
  final bool centerPinput;
  final String? errorText;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final pinTheme = PinTheme(
      width: 40,
      height: 40,
      textStyle: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w500,
        //todo [sambhav] somehow the textBrandPrimary is looking black so using border brand
        // color: theme.text.textBrandPrimary,
        color: theme.border.borderBrand,
      ),
      decoration: BoxDecoration(
        color: theme.background.bgPrimary,
        borderRadius: BorderRadius.circular(DuploRadius.radius_sm_6),
        border: Border.all(color: theme.border.borderPrimary),
        boxShadow: [
          BoxShadow(
            color: Color(0x0C0A0C12),
            blurRadius: 2,
            offset: Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
    );

    final preFilledWidget = DuploText(
      text: '-',
      style: textStyles.displaySm,
      fontWeight: DuploFontWeight.semiBold,
      color: theme.text.textPlaceholder,
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          DuploText(
            text: label!,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.medium,
            color: theme.text.textSecondary,
          ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Row(
            mainAxisAlignment:
                (centerPinput)
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
            children: [
              Pinput(
                key: Key('verification_code_input'),
                controller: controller,
                onChanged: onChanged,
                onCompleted: onCompleted,
                onSubmitted: onSubmitted,
                autofocus: autoFocus ?? false,
                length: 4,
                forceErrorState: errorText != null && errorText!.isNotEmpty,
                errorText: errorText,
                errorTextStyle: TextStyle(
                  color: theme.text.textErrorPrimary,
                  fontSize: textStyles.textSm.fontSize,
                  height: textStyles.textSm.lineHeight,
                  fontFamily: textStyles.textSm.fontFamily,
                ),
                crossAxisAlignment: CrossAxisAlignment.center,
                preFilledWidget: preFilledWidget,
                defaultPinTheme: pinTheme,
                focusedPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderBrand,
                      width: 2,
                    ),
                  ),
                ),
                submittedPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderBrand,
                      width: 2,
                    ),
                  ),
                ),
                errorPinTheme: pinTheme.copyWith(
                  decoration: pinTheme.decoration?.copyWith(
                    border: Border.all(
                      color: theme.border.borderError,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (hintText != null)
          DuploText(
            text: hintText!,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.regular,
            color: theme.text.textTertiary,
          ),
      ],
    );
  }
}
