part of 'deposit_payment_options_bloc.dart';

@freezed
sealed class DepositPaymentOptionsState with _$DepositPaymentOptionsState {
  const factory DepositPaymentOptionsState({
    DepositPaymentMethodsModel? paymentMethodsData,
    @Default(PaymentMethodsProccessState.loading())
    PaymentMethodsProccessState paymentMethodsProccessState,
    DepositFlowConfig? depositFlowConfig,
  }) = _DepositPaymentOptionsState;
}

@freezed
sealed class PaymentMethodsProccessState with _$PaymentMethodsProccessState {
  const factory PaymentMethodsProccessState.loading() =
      PaymentMethodsProccessStateLoading;

  const factory PaymentMethodsProccessState.success() =
      PaymentMethodsProccessStateSuccess;

  const factory PaymentMethodsProccessState.error() =
      PaymentMethodsProccessStateError;
}
