import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/domain/model/screen_mode.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart';

class ContinueCancelButton extends StatelessWidget {
  const ContinueCancelButton({
    super.key,
    required this.bankTransferAmountModel,
  });

  final WithdrawFlowParams bankTransferAmountModel;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WithdrawBankTransferBloc, WithdrawBankTransferState>(
      buildWhen:
          (previous, current) =>
              (current.index != null) ||
              (current.screenMode != previous.screenMode),
      builder: (builderContext, state) {
        return SizedBox(
          width: MediaQuery.sizeOf(context).width,
          child:
              state.screenMode == ScreenMode.modify
                  ? DuploButton.secondary(
                    title: EquitiLocalization.of(context).payments_cancel,
                    onTap: () {
                      builderContext.read<WithdrawBankTransferBloc>().add(
                        WithdrawBankTransferEvent.onChangeMode(ScreenMode.view),
                      );
                    },
                  )
                  : DuploButton.defaultPrimary(
                    trailingIcon: assets.Assets.images.arrowRightIc.keyName,
                    title: EquitiLocalization.of(context).payments_continue,
                    onTap: () {
                      context.read<WithdrawBankTransferBloc>().add(
                        WithdrawBankTransferEvent.onPressContinue(
                          bankTransferAmountModel,
                        ),
                      );
                    },
                    isDisabled: state.index == null,
                  ),
        );
      },
    );
  }
}
