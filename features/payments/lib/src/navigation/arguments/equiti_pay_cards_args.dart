import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:domain/domain.dart';

part 'equiti_pay_cards_args.freezed.dart';

@freezed
abstract class EquitiPayCardsArgs with _$EquitiPayCardsArgs {
  const factory EquitiPayCardsArgs({
    required String url,
    required String title,
    required String transactionId,
    required String accountNumber,
    required DepositFlowConfig depositFlowConfig,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) = _EquitiPayCardsArgs;
}
