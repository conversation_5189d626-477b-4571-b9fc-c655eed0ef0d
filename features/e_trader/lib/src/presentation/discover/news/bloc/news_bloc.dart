import 'package:e_trader/src/data/api/events_news_request_model.dart';
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/domain/model/news_events_sort_order.dart';
import 'package:e_trader/src/domain/usecase/events_news_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_news_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'news_bloc.freezed.dart';
part 'news_event.dart';
part 'news_state.dart';

class NewsBloc extends Bloc<NewsEvent, NewsState> {
  final GetNewsUseCase _getNewsUseCase;
  final EventsNewsLocalDataUseCase _newsLocalDataUseCase;
  final String? _tickerName;

  NewsBloc(this._getNewsUseCase, this._newsLocalDataUseCase, this._tickerName)
    : super(
        NewsState(
          processState: NewsProcessState.initial(),
          sortOrder: _newsLocalDataUseCase.getNewsSortingOption(),
        ),
      ) {
    on<_FetchNews>(_onFetchNews);
    on<_SearchNews>(
      _onSearchNews,
      transformer: throttleTransformer(_throttleDuration),
    );
    on<_ChangeSortOrder>(_onChangeSortOrder);
    on<_UpdateDateLabel>(_onUpdateDateLabel);
  }

  final _throttleDuration = Duration(milliseconds: 500);
  final int _pageSize = 10;

  Future<void> _onFetchNews(_FetchNews event, Emitter<NewsState> emit) async {
    if (event.loadMore && state.hasReachedMax) {
      return;
    }

    final bool isLoadingMore = event.loadMore;
    final int currentPage = isLoadingMore ? state.currentPage + 1 : 0;
    final int startIndex = currentPage;

    if (!isLoadingMore) {
      emit(
        state.copyWith(
          processState: const NewsProcessState.loading(),
          currentPage: 0,
          hasReachedMax: false,
        ),
      );
    } else {
      emit(state.copyWith(processState: const NewsProcessState.loadingMore()));
    }

    final request = EventsNewsRequestModel(
      start: startIndex,
      length: _pageSize,
      order: EventsSearchOrder(name: 'date', dir: 'desc'),
      search: EventsSearchCriteria(value: event.query, matchingType: 1),
      filters: [
        EventsSearchFilter(field: 'tickername', value: _tickerName ?? ''),
      ],
    );

    final result = await _getNewsUseCase(body: request).run();

    result.fold(
      (failure) {
        emit(state.copyWith(processState: NewsProcessState.error('$failure')));
      },
      (response) {
        final List<NewsItem> allNewsItems =
            isLoadingMore
                ? [...state.allNewsItems, ...response.data]
                : response.data;

        final groupedList = _groupNewsItems(allNewsItems);
        final bool hasReachedMax =
            response.count <= startIndex + response.data.length;
        emit(
          state.copyWith(
            processState: const NewsProcessState.success(),
            groupedNews: groupedList,
            hasReachedMax: hasReachedMax,
            pageSize: _pageSize,
            currentPage: currentPage,
            count: response.count,
            allNewsItems: allNewsItems,
          ),
        );
      },
    );
  }

  Future<void> _onChangeSortOrder(
    _ChangeSortOrder event,
    Emitter<NewsState> emit,
  ) async {
    await _newsLocalDataUseCase.saveNewsSortingOption(event.sortOrder);
    if (!isClosed) {
      emit(
        state.copyWith(
          processState: NewsProcessState.loading(),
          hasReachedMax: false,
          sortOrder: event.sortOrder,
          currentPage: 0,
        ),
      );

      add(NewsEvent.fetchNews());
    }
  }

  void _onUpdateDateLabel(_UpdateDateLabel event, Emitter<NewsState> emit) {
    if (state.dateLabel == event.dateLabel) return;
    emit(state.copyWith(dateLabel: event.dateLabel));
  }

  Future<void> _onSearchNews(_SearchNews event, Emitter<NewsState> emit) async {
    final String trimmedQuery = event.query.trim();

    if (trimmedQuery.isEmpty) {
      emit(
        state.copyWith(
          processState: const NewsProcessState.success(),
          groupedNews: [],
          currentPage: 0,
          hasReachedMax: false,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        processState: const NewsProcessState.loading(),
        currentPage: 0,
      ),
    );

    if (trimmedQuery.length > 1) {
      add(NewsEvent.fetchNews(query: trimmedQuery, loadMore: event.loadMore));

      final previousSearches =
          await _newsLocalDataUseCase.getNewsPreviousSearches();

      if (!previousSearches.contains(trimmedQuery)) {
        await _newsLocalDataUseCase.saveNewsPreviousSearches(trimmedQuery);
      }
    }
  }

  List<Object?> _groupNewsItems(List<NewsItem> newsItems) {
    final List<Object?> groupedList = [];
    final Map<DayPart, List<NewsItem>> labelGroups = {};
    final Map<DateTime, List<NewsItem>> specificDateGroups = {};

    for (final item in newsItems) {
      final DayPart label = _getDateLabel(item.item.parsedDate.toLocal());
      if (label == DayPart.specificDate) {
        final dateKey = DateTime(
          item.item.parsedDate.year,
          item.item.parsedDate.month,
          item.item.parsedDate.day,
        );
        specificDateGroups.putIfAbsent(dateKey, () => []).add(item);
      } else {
        labelGroups.putIfAbsent(label, () => []).add(item);
      }
    }

    final orderedDayParts = [
      DayPart.today,
      DayPart.yesterday,
      DayPart.tomorrow,
    ];

    for (final dayPart in orderedDayParts) {
      if (labelGroups.containsKey(dayPart)) {
        groupedList.add(dayPart);
        groupedList.addAll(labelGroups[dayPart]!);
      }
    }

    final sortedDates =
        specificDateGroups.keys.toList()
          ..sort((a, b) => b.compareTo(a)); // Newest first, optional

    for (final date in sortedDates) {
      groupedList.add(date); // ✅ Use DateTime directly
      groupedList.addAll(specificDateGroups[date]!);
    }

    return groupedList;
  }

  DayPart _getDateLabel(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final tomorrow = today.add(Duration(days: 1));
    final itemDate = DateTime(date.year, date.month, date.day);

    if (itemDate == today) {
      return DayPart.today;
    } else if (itemDate == yesterday) {
      return DayPart.yesterday;
    } else if (itemDate == tomorrow) {
      return DayPart.tomorrow;
    }
    return DayPart.specificDate;
  }
}
