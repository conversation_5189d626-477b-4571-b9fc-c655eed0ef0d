import 'package:api_client/api_client.dart';
import 'package:payment/src/di/di_container.dart';

void withdrawFeesInsufficientBalance() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/client-profiles/********?brokerId=********': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/client_profile/success.json',
        ),
      ],
      '/api/v1/withdrawal/fees': [
        MockResponse(
          code: 200,
          bodyFilePath:
              'resources/mocks/withdraw_fees/insufficient_balance_fees.json',
        ),
      ],
      'api/v1/client/bankAccounts': [
        MockResponse(
          bodyFilePath: 'resources/mocks/bank_transfer/success.json',
        ),
      ],
    });
}
