import 'package:duplo/duplo.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/debug/network_logging_bottom_sheet.dart';
import 'package:equiti_platform/deep_links/deep_link_handler.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_platform/di/di_initializer.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_it/get_it.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:theme_manager/theme_manager.dart';

Future<void> runMainApp(GetIt diContainer, AppConfig config) async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await configureDependencies(diContainer, env: config.env.name);
  await EquitiLocalizationManager.init();
  EquitiLocalizationManager.update();
  // todo (Abed): Uncomment post firebase notification fix for different envs
  // await FirebaseManagerBase.initialize(config.firebaseEnv);

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) {
    diContainer<ReporterBase>().start(
      app: const EquitiApp(),
      extraData: {"sentry_dsn_url": config.sentryDSNUrl},
    );
  });
}

class EquitiApp extends StatefulWidget {
  const EquitiApp({super.key});

  @override
  State<EquitiApp> createState() => _EquitiAppState();
}

class _EquitiAppState extends State<EquitiApp> {
  late String nextScreenToShow;

  late final EquitiNavigatorBase _navigator;
  late final RouterDelegate<Object> _routerDelegate;
  late final RouteInformationParser<Object> _routeInformationParser;

  @override
  void initState() {
    super.initState();
    _navigator = diContainer<EquitiNavigatorBase>();
    _routerDelegate = _navigator.routerDelegate;
    _routeInformationParser = _navigator.routeInformationParser;

    // Initialize deep link handler
    _initializeDeepLinks();

    screenDecider();
  }

  @override
  void dispose() {
    DeepLinkHandler.dispose();
    super.dispose();
  }

  /// Initialize deep link handling
  Future<void> _initializeDeepLinks() async {
    try {
      await DeepLinkHandler.initialize();
    } catch (e) {
      debugPrint('Failed to initialize deep links: $e');
    }
  }

  void _init() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigator.pushReplacement(nextScreenToShow);
      FlutterNativeSplash.remove();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: diContainer<ThemeManager>(), // Listen to theme changes
      builder: (themeContext, _) {
        return ListenableBuilder(
          listenable: diContainer<LocaleManager>(), // Listen to locale changes
          builder: (localeContext, _) {
            return MaterialApp.router(
              locale: diContainer<LocaleManager>().locale,
              routerDelegate: _routerDelegate,
              routeInformationParser: _routeInformationParser,
              theme: ThemeData(
                useMaterial3: true,
                actionIconTheme: ActionIconThemeData(
                  backButtonIconBuilder: (backButtonBuilderContext) {
                    final theme = backButtonBuilderContext.duploTheme;
                    return Assets.images
                        .arrowLeftDirectional(backButtonBuilderContext)
                        .svg(
                          colorFilter: ColorFilter.mode(
                            theme.foreground.fgPrimary,
                            BlendMode.srcIn,
                          ),
                        );
                  },
                ),
              ),
              supportedLocales: EquitiLocalization.supportedLocales,
              localizationsDelegates: EquitiLocalization.localizationsDelegates,
              builder: (builderContext, child) {
                final duploThemeData =
                    diContainer<ThemeManager>().isDarkMode
                        ? DuploThemeData.dark()
                        : DuploThemeData.light();

                final locale = Localizations.localeOf(builderContext);

                return DuploTheme(
                  data: duploThemeData,
                  child: DuploTextStyles(
                    locale: locale,
                    child: _buildAppWithDebugFAB(child!, duploThemeData),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  /// Builds the app with debug FAB overlay (only in debug mode)
  Widget _buildAppWithDebugFAB(Widget child, DuploThemeData theme) {
    return Stack(
      children: [
        child,
        Positioned(
          bottom: 80,
          right: 16,
          child: FloatingActionButton(
            mini: true,
            backgroundColor: theme.utility.utilityBrand500,
            foregroundColor: Colors.white,
            onPressed: () {
              NetworkLoggingSheet.show(
                diContainer<GlobalKey<NavigatorState>>().currentContext!,
              );
            },
            child: const Icon(Icons.network_check, size: 20),
          ),
        ),
      ],
    );
  }

  void screenDecider() async {
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    final TokenManager manager = diContainer<TokenManager>();
    final tokenState = await manager.getState();
    if (tokenState == TokenManagerState.authenticated) {
      nextScreenToShow = OnboardingRouteSchema.progressTrackerPage.label;
    } else if (tokenState == TokenManagerState.loggedOutUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    } else if (tokenState == TokenManagerState.noUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    }
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    _init();
  }
}
