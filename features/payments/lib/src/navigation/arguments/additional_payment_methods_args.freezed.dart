// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'additional_payment_methods_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AdditionalPaymentMethodsArgs {

 String get url; String get title; String get transactionId; String get accountNumber; DepositFlowConfig get depositFlowConfig; num? get maxPollingAttempts; num? get pollingFrequencySeconds;
/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdditionalPaymentMethodsArgsCopyWith<AdditionalPaymentMethodsArgs> get copyWith => _$AdditionalPaymentMethodsArgsCopyWithImpl<AdditionalPaymentMethodsArgs>(this as AdditionalPaymentMethodsArgs, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalPaymentMethodsArgs&&(identical(other.url, url) || other.url == url)&&(identical(other.title, title) || other.title == title)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,url,title,transactionId,accountNumber,depositFlowConfig,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'AdditionalPaymentMethodsArgs(url: $url, title: $title, transactionId: $transactionId, accountNumber: $accountNumber, depositFlowConfig: $depositFlowConfig, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class $AdditionalPaymentMethodsArgsCopyWith<$Res>  {
  factory $AdditionalPaymentMethodsArgsCopyWith(AdditionalPaymentMethodsArgs value, $Res Function(AdditionalPaymentMethodsArgs) _then) = _$AdditionalPaymentMethodsArgsCopyWithImpl;
@useResult
$Res call({
 String url, String title, String transactionId, String accountNumber, DepositFlowConfig depositFlowConfig, num? maxPollingAttempts, num? pollingFrequencySeconds
});


$DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class _$AdditionalPaymentMethodsArgsCopyWithImpl<$Res>
    implements $AdditionalPaymentMethodsArgsCopyWith<$Res> {
  _$AdditionalPaymentMethodsArgsCopyWithImpl(this._self, this._then);

  final AdditionalPaymentMethodsArgs _self;
  final $Res Function(AdditionalPaymentMethodsArgs) _then;

/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? url = null,Object? title = null,Object? transactionId = null,Object? accountNumber = null,Object? depositFlowConfig = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_self.copyWith(
url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}
/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}


/// @nodoc


class _AdditionalPaymentMethodsArgs implements AdditionalPaymentMethodsArgs {
  const _AdditionalPaymentMethodsArgs({required this.url, required this.title, required this.transactionId, required this.accountNumber, required this.depositFlowConfig, this.maxPollingAttempts, this.pollingFrequencySeconds});
  

@override final  String url;
@override final  String title;
@override final  String transactionId;
@override final  String accountNumber;
@override final  DepositFlowConfig depositFlowConfig;
@override final  num? maxPollingAttempts;
@override final  num? pollingFrequencySeconds;

/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AdditionalPaymentMethodsArgsCopyWith<_AdditionalPaymentMethodsArgs> get copyWith => __$AdditionalPaymentMethodsArgsCopyWithImpl<_AdditionalPaymentMethodsArgs>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AdditionalPaymentMethodsArgs&&(identical(other.url, url) || other.url == url)&&(identical(other.title, title) || other.title == title)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,url,title,transactionId,accountNumber,depositFlowConfig,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'AdditionalPaymentMethodsArgs(url: $url, title: $title, transactionId: $transactionId, accountNumber: $accountNumber, depositFlowConfig: $depositFlowConfig, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class _$AdditionalPaymentMethodsArgsCopyWith<$Res> implements $AdditionalPaymentMethodsArgsCopyWith<$Res> {
  factory _$AdditionalPaymentMethodsArgsCopyWith(_AdditionalPaymentMethodsArgs value, $Res Function(_AdditionalPaymentMethodsArgs) _then) = __$AdditionalPaymentMethodsArgsCopyWithImpl;
@override @useResult
$Res call({
 String url, String title, String transactionId, String accountNumber, DepositFlowConfig depositFlowConfig, num? maxPollingAttempts, num? pollingFrequencySeconds
});


@override $DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class __$AdditionalPaymentMethodsArgsCopyWithImpl<$Res>
    implements _$AdditionalPaymentMethodsArgsCopyWith<$Res> {
  __$AdditionalPaymentMethodsArgsCopyWithImpl(this._self, this._then);

  final _AdditionalPaymentMethodsArgs _self;
  final $Res Function(_AdditionalPaymentMethodsArgs) _then;

/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? url = null,Object? title = null,Object? transactionId = null,Object? accountNumber = null,Object? depositFlowConfig = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_AdditionalPaymentMethodsArgs(
url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}

/// Create a copy of AdditionalPaymentMethodsArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

// dart format on
