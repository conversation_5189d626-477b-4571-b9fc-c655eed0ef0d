// ignore_for_file: prefer-number-format

import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/device_time_zone_use_case.dart';
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:url_launcher/url_launcher.dart';

class ChartWebview extends StatefulWidget {
  final String accountNumber;
  final int digits;
  final String symbol;
  final int timeFrameInMinutes;
  final TradingChartType chartType;

  final void Function(ChartViewProcessState)? onChangeChartState;

  const ChartWebview({
    super.key,
    required this.accountNumber,
    required this.symbol,
    required this.digits,
    required this.chartType,
    required this.timeFrameInMinutes,
    this.onChangeChartState,
  });

  @override
  State<ChartWebview> createState() => ChartWebviewState();
}

class ChartWebviewState extends State<ChartWebview>
    with AutomaticKeepAliveClientMixin {
  final _localHostUrl = 'http://localhost:8080/';

  @override
  bool get wantKeepAlive => true;

  String _timeZone = 'Etc/UTC';
  late TradingChartType _currentChartType;
  late int _currentTimeFrame;

  @override
  void initState() {
    super.initState();
    _startLocalhostServer();
    _currentChartType = widget.chartType;
    _currentTimeFrame = widget.timeFrameInMinutes;

    DeviceTimeZoneUseCase.getDeviceTimezone().then((tZone) {
      _timeZone = tZone;
    });
  }

  void updateConfig({
    required TradingChartType chartType,
    required int timeFrameInMinutes,
  }) {
    if (_currentChartType != chartType) {
      _currentChartType = chartType;
      final newType = _currentChartType == TradingChartType.candles() ? 1 : 3;
      _evaluateJavaScript(
        source: """
        window.tvWidget.chart().setChartType($newType);
      """,
      );
    }

    if (_currentTimeFrame != timeFrameInMinutes) {
      _currentTimeFrame = timeFrameInMinutes;
      _evaluateJavaScript(
        source: """
        window.tvWidget.chart().setResolution("$_currentTimeFrame");
  """,
      );
    }
  }

  StreamSubscription<SymbolQuoteModel>? _prodcutSubscription;
  InAppWebViewController? _webViewController;
  InAppLocalhostServer _localhostServer = InAppLocalhostServer();

  Future<void> _startLocalhostServer() async {
    await _localhostServer.close().then((value) {
      if (!_localhostServer.isRunning()) {
        _localhostServer.start();
      }
    });
  }

  @override
  void dispose() {
    _webViewController?.dispose();
    _webViewController = null;
    if (_localhostServer.isRunning()) {
      _localhostServer.close();
    }

    _prodcutSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return InAppWebView(
      key: ValueKey(widget.symbol),
      initialSettings: InAppWebViewSettings(
        hardwareAcceleration: true,
        supportZoom: false,
        cacheEnabled: true,
        domStorageEnabled: true,
        javaScriptEnabled: true,
      ),
      gestureRecognizers: {const Factory(EagerGestureRecognizer.new)},
      initialUrlRequest: URLRequest(
        url: WebUri.uri(
          Uri.parse('$_localHostUrl${trader.Assets.chart.index}'),
        ),
      ),
      onWebViewCreated: (controller) => _webViewController = controller,
      onLoadStop:
          (controller, _) => _setupJavaScriptHandlers(context, controller),
      onReceivedError:
          (controller, url, error) =>
              widget.onChangeChartState?.call(ChartViewProcessState.error()),
      onPageCommitVisible: _handlePageCommitVisible,
      onConsoleMessage: (controller, consoleMessage) {
        print("🔥 JS LOG: ${consoleMessage.message}");
      },
    );
  }

  void _handlePageCommitVisible(
    InAppWebViewController controller,
    WebUri? uri,
  ) async {
    if (uri!.toString().contains('https://www.tradingview.com/?utm_source')) {
      await _webViewController?.goBack();
      await launchUrl(Uri.parse('https://www.tradingview.com/')).then((_) {
        widget.onChangeChartState?.call(ChartViewProcessState.loading());
      });
    }
  }

  void _setupJavaScriptHandlers(
    BuildContext context,
    InAppWebViewController controller,
  ) {
    controller
      ..addJavaScriptHandler(
        handlerName: 'init',
        callback: (_) => _buildInitHandlerData(),
      )
      ..addJavaScriptHandler(
        handlerName: 'resolveSymbol',
        callback: (_) => _buildResolveSymbolHandlerData(),
      )
      ..addJavaScriptHandler(
        handlerName: 'getBars',
        callback: (args) => _fetchBarsData(context, args),
      )
      ..addJavaScriptHandler(
        handlerName: 'onLoadingFinished',
        callback: (_) {
          widget.onChangeChartState?.call(ChartViewProcessState.success());
          // Setup orientation detection after chart is loaded
          _setupOrientationDetection();
        },
      );

    _subscribePriceUpdates(context);
  }

  /// Setup orientation detection in JavaScript
  Future<void> _setupOrientationDetection() async {
    final durationMs = 200;
    final curve = "ease-in-out";

    await _evaluateJavaScript(
      source: '''
      (function() {
        console.log('Setting up orientation detection...');
        console.log('Animation config: ${durationMs}ms, $curve');

        let currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
        let isAnimating = false;
        console.log('Initial orientation:', currentOrientation);

        function animateChartSize() {
          if (isAnimating) {
            console.log('Animation already in progress, skipping...');
            return;
          }

          const chartContainer = document.querySelector('#tradingview_widget') ||
                                document.querySelector('.tradingview-widget-container') ||
                                document.querySelector('iframe') ||
                                document.body;

          if (!chartContainer) {
            console.log('Chart container not found');
            return;
          }

          const newOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';

          if (newOrientation !== currentOrientation) {
            console.log('Orientation changed from', currentOrientation, 'to', newOrientation);
            isAnimating = true;
            currentOrientation = newOrientation;

            // Setup animation
            chartContainer.style.willChange = 'width, height, transform';
            chartContainer.style.transition = 'all ${durationMs}ms $curve';
            chartContainer.style.backfaceVisibility = 'hidden';

            // Always animate to current window dimensions
            const targetWidth = window.innerWidth + 'px';
            const targetHeight = window.innerHeight + 'px';

            console.log('Animating to new size:', targetWidth, 'x', targetHeight);

            requestAnimationFrame(() => {
              if (newOrientation === 'landscape') {
                // Landscape: Use fullscreen positioning
                chartContainer.style.position = 'fixed';
                chartContainer.style.top = '0';
                chartContainer.style.left = '0';
                chartContainer.style.zIndex = '1000';
              } else {
                // Portrait: Use relative positioning
                chartContainer.style.position = 'relative';
                chartContainer.style.top = 'auto';
                chartContainer.style.left = 'auto';
                chartContainer.style.zIndex = 'auto';
              }

              // Always animate to current window size
              chartContainer.style.width = targetWidth;
              chartContainer.style.height = targetHeight;
              chartContainer.style.transform = 'translateZ(0)';
            });

            console.log('Animating to', newOrientation, 'mode with size:', targetWidth, 'x', targetHeight);

            // Clean up after animation
            setTimeout(() => {
              chartContainer.style.willChange = 'auto';
              chartContainer.style.backfaceVisibility = 'visible';
              chartContainer.style.transform = 'none';
              chartContainer.style.transition = '';
              isAnimating = false;

              // Trigger resize
              window.dispatchEvent(new Event('resize'));
              if (window.tvWidget && window.tvWidget.resize) {
                window.tvWidget.resize();
              }

              console.log('Animation complete and cleaned up');
            }, ${durationMs} + 100);
          }
        }

        // Listen for orientation change
        window.addEventListener('orientationchange', () => {
          console.log('Orientation change detected via orientationchange');
          setTimeout(animateChartSize, 150);
        });

        // Listen for resize events with debouncing
        let resizeTimeout;
        window.addEventListener('resize', () => {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(() => {
            console.log('Resize event detected');
            animateChartSize();
          }, 100);
        });

        // Also listen for visual viewport changes (better for mobile)
        if (window.visualViewport) {
          window.visualViewport.addEventListener('resize', () => {
            console.log('Visual viewport resize detected');
            setTimeout(animateChartSize, 100);
          });
        }

        console.log('Orientation detection setup complete');
      })();
    ''',
    );
  }

  Map<String, dynamic> _buildInitHandlerData() {
    final isDark = diContainer<ThemeManager>().isDarkMode;
    return {
      "identifier": '${widget.accountNumber}${widget.symbol}',
      "symbol": widget.symbol,
      "defaultInterval": widget.timeFrameInMinutes,
      "disabledFeatures": _fullChartDisabledFeatures,
      "theme": isDark ? 'Dark' : 'Light',
      "timezone": _timeZone,
      "chartType": widget.chartType == TradingChartType.candles() ? 1 : 3,
    };
  }

  Map<String, dynamic> _buildResolveSymbolHandlerData() {
    return {
      "ticker": widget.symbol,
      "name": widget.symbol,
      "description": widget.symbol,
      "session": '24x7',
      "exchange": 'FX',
      "timezone": "UTC",
      "minmov": 1,
      "pricescale": pow(10, widget.digits),
      "has_intraday": true,
      "visible_plots_set": "ohlc",
      "volume_precision": 5,
      "data_status": 'streaming',
    };
  }

  Future<List<Map<String, Object?>>> _fetchBarsData(
    BuildContext context,
    List<Object?> args,
  ) async {
    final Map<String, Object?>? request =
        args.safeFirst<Map<String, Object?>>();
    if (request == null) {
      widget.onChangeChartState?.call(ChartViewProcessState.error());
      return [];
    }

    try {
      if (!mounted) return [];
      final result = await context.read<TradingChartViewBloc>().onGetChart(
        symbol: widget.symbol,
        fromDate: request["from"] as String,
        toDate: request["to"] as String,
        timeframMinutes: request["resolution"] as String,
      );
      return result;
    } catch (e) {
      print("Error in _fetchBarsData: $e");
      return [];
    }
  }

  void _subscribePriceUpdates(BuildContext context) async {
    final result =
        await context
            .read<TradingChartViewBloc>()
            .getSymbolQuotesStream(symbol: widget.symbol)
            .run();

    result.fold(
      (_) => widget.onChangeChartState?.call(ChartViewProcessState.error()),
      (stream) {
        _prodcutSubscription = stream.listen(
          (data) {
            if (!mounted) return;

            final jsonStr = jsonEncode(data.toJson());
            _evaluateJavaScript(
              source: """
                window.dispatchEvent(new CustomEvent("PricingEvent", { detail: $jsonStr }));
              """,
            );
          },
          onError:
              (_, __) => widget.onChangeChartState?.call(
                ChartViewProcessState.error(),
              ),
        );
      },
    );
  }

  Future<void> _evaluateJavaScript({required String source}) async {
    if (_webViewController == null) return;
    try {
      await _webViewController!.evaluateJavascript(source: source);
    } catch (e) {
      debugPrint("Evaluate javascript error: $e");
    }
  }

  List<String> get _fullChartDisabledFeatures => const [
    "header_screenshot",
    "header_fullscreen_button",
    "header_compare",
    "header_symbol_search",
    "symbol_search_hot_key",
    "legend_inplace_edit",
    "display_market_status",
    "control_bar",
    "header_fullscreen_button",
    "header_layouttoggle",
    "border_around_the_chart",
    "use_localstorage_for_settings",
  ];
}

extension SafeListAccess on List<Object?> {
  T? safeFirst<T>() {
    if (isEmpty || first == null) return null;
    return first as T?;
  }
}
