import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:payment/src/data/payment_status_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/model/payment_event.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/presentation/widgets/leave_confirmation_bottom_sheet.dart';
import 'dart:async';

const String kPaymentMessageListenerJS = '''
if (!window.hasMessageListener) {
  window.addEventListener("message", (event) => {
    try {
      const data = event.data || {};
      const action = data.action?.trim() || "";
      const gatewayCode = data.gateway_code?.trim() || "";

      // Avoid sending empty messages
      if (!action && !gatewayCode) {
        console.warn("⚠️ Ignored message with empty action and gateway_code");
        return;
      }

      if (window.flutter_inappwebview?.callHandler) {
        window.flutter_inappwebview.callHandler(
          'paymentHandler',
          { action, gateway_code: gatewayCode }
        );
      } else {
        console.error("❌ flutter_inappwebview.callHandler is not available");
      }
    } catch (err) {
      console.error("❌ Error handling message:", err);
    }
  });

  window.hasMessageListener = true;
}
''';

class EquitiPayCardsScreen extends StatefulWidget {
  final String url;
  final String title;
  final String transactionId;
  final String accountNumber;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;
  final DepositFlowConfig depositFlowConfig;

  const EquitiPayCardsScreen({
    required this.url,
    required this.title,
    required this.transactionId,
    required this.accountNumber,
    required this.depositFlowConfig,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
  });

  @override
  _EquitiPayCardsScreenState createState() => _EquitiPayCardsScreenState();
}

class _EquitiPayCardsScreenState extends State<EquitiPayCardsScreen> {
  bool isLoading = true;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    // Start a 10-minute timer when the screen is initialized
    _startTimeoutTimer();
  }

  @override
  void dispose() {
    // Cancel the timer when the screen is disposed
    _timeoutTimer?.cancel();
    _cancelTimeoutTimer();
    super.dispose();
  }

  void _startTimeoutTimer() {
    // Cancel any existing timer first
    _cancelTimeoutTimer();

    // Set a 10-minute timeout (600 seconds)
    _timeoutTimer = Timer(const Duration(minutes: 10), () {
      print("⚠️ Payment timeout: No callback received after 10 minutes");
      _navigateToPaymentStatusPage();
    });
  }

  void _cancelTimeoutTimer() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }

  /// Handles payment events received from the WebView
  ///
  /// Processes only 3DS-FAILED and final_status events,
  /// navigating to PaymentStatusScreen in both scenarios.
  void _handlePaymentEvent(PaymentEvent event) {
    // Cancel the timeout timer as we received a callback
    _cancelTimeoutTimer();

    print(
      "🟢 Handling payment event: ${event.action}, Gateway Code: ${event.gatewayCode}",
    );

    switch (event.action) {
      case '3DS-FAILED':
        _navigateToPaymentStatusPage(status: PaymentStatus.rejected);
        break;
      case 'final_status':
        switch (event.gatewayCode) {
          case 'ABORTED':
          case 'ACQUIRER_SYSTEM_ERROR':
          case 'AUTHENTICATION_FAILED':
          case 'CANCELLED':
          case 'DECLINED':
          case 'DECLINED_AVS':
          case 'DECLINED_AVS_CSC':
          case 'DECLINED_CSC':
          case 'DECLINED_DO_NOT_CONTACT':
          case 'DECLINED_INVALID_PIN':
          case 'DECLINED_PAYMENT_PLAN':
          case 'DECLINED_PIN_REQUIRED':
          case 'DUPLICATE_BATCH':
          case 'EXCEEDED_RETRY_LIMIT':
          case 'INVALID_CSC':
          case 'LOCK_FAILURE':
          case 'NOT_ENROLLED_3D_SECURE':
          case 'NOT_SUPPORTED':
          case 'NO_BALANCE':
          case 'REFERRED':
          case 'SUBMITTED':
          case 'SYSTEM_ERROR':
          case 'UNKNOWN':
          case 'BLOCKED':
          case 'INSUFFICIENT_FUNDS':
          case 'PENDING':
          case 'TIMED_OUT':
          case 'UNSPECIFIED_FAILURE':
          case 'EXPIRED_CARD':
            _navigateToPaymentStatusPage(status: PaymentStatus.rejected);
            break;
          default:
            _navigateToPaymentStatusPage();
            break;
        }
        break;
      default:
        print("⚠️ Unhandled payment action: ${event.action}");
        break;
    }
  }

  void _navigateToPaymentStatusPage({PaymentStatus? status}) {
    diContainer<PaymentNavigation>().goToPaymentStatusScreen(
      paymentStatus: status,
      transactionId: widget.transactionId,
      accountNumber: widget.accountNumber,
      depositFlowConfig: widget.depositFlowConfig,
      maxPollingAttempts: widget.maxPollingAttempts,
      pollingFrequencySeconds: widget.pollingFrequencySeconds,
    );
  }

  @override
  Widget build(BuildContext context) {
    print(widget.url);
    final theme = context.duploTheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          DuploSheet.showModalSheet<Widget>(
            context: context,
            hasTopBarLayer: false,
            hideCloseButton: true,
            content: (contextContext) {
              return LeaveConfirmationBottomSheet();
            },
            title: '',
          );
        }
      },
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(title: widget.title),
        body: Stack(
          children: [
            Opacity(
              opacity: isLoading ? 0.0 : 1.0, // Hide WebView when loading
              child: InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                onLoadStart: (controller, url) {
                  setState(() {
                    isLoading = true;
                  });
                },
                onLoadStop: (controller, url) {
                  setState(() {
                    isLoading = false;
                  });
                  controller.evaluateJavascript(
                    source: kPaymentMessageListenerJS,
                  );
                  controller.addJavaScriptHandler(
                    handlerName: "paymentHandler",
                    callback: (args) {
                      try {
                        if (!(args.firstOrNull is Map)) {
                          print("⚠️ Args.first is not Map");
                          print(args.firstOrNull.runtimeType);
                        }
                        if (args.isNotEmpty && args.firstOrNull is Map) {
                          final eventData = Map<String, dynamic>.from(
                            args.firstOrNull as Map,
                          );
                          final event = PaymentEvent.fromJson(eventData);
                          print(
                            "🟢 Action: ${event.action}, Gateway Code: ${event.gatewayCode}",
                          );

                          // Handle the payment event
                          _handlePaymentEvent(event);
                        } else {
                          print("⚠️ Unexpected payload: $args");
                        }
                      } catch (e, st) {
                        print("❌ Failed to parse payment event: $e\n$st");
                      }
                    },
                  );
                },
              ),
            ),
            if (isLoading)
              Center(
                child: DuploLoadingIndicator(
                  color: theme.foreground.fgBrandPrimary,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
