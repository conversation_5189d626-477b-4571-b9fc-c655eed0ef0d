// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'change_leverage_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChangeLeverageEvent {

 String get accountNumber;
/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChangeLeverageEventCopyWith<ChangeLeverageEvent> get copyWith => _$ChangeLeverageEventCopyWithImpl<ChangeLeverageEvent>(this as ChangeLeverageEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageEvent&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber));
}


@override
int get hashCode => Object.hash(runtimeType,accountNumber);

@override
String toString() {
  return 'ChangeLeverageEvent(accountNumber: $accountNumber)';
}


}

/// @nodoc
abstract mixin class $ChangeLeverageEventCopyWith<$Res>  {
  factory $ChangeLeverageEventCopyWith(ChangeLeverageEvent value, $Res Function(ChangeLeverageEvent) _then) = _$ChangeLeverageEventCopyWithImpl;
@useResult
$Res call({
 String accountNumber
});




}
/// @nodoc
class _$ChangeLeverageEventCopyWithImpl<$Res>
    implements $ChangeLeverageEventCopyWith<$Res> {
  _$ChangeLeverageEventCopyWithImpl(this._self, this._then);

  final ChangeLeverageEvent _self;
  final $Res Function(ChangeLeverageEvent) _then;

/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountNumber = null,}) {
  return _then(_self.copyWith(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _OnGetLeverages implements ChangeLeverageEvent {
   _OnGetLeverages({required this.accountNumber});
  

@override final  String accountNumber;

/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGetLeveragesCopyWith<_OnGetLeverages> get copyWith => __$OnGetLeveragesCopyWithImpl<_OnGetLeverages>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetLeverages&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber));
}


@override
int get hashCode => Object.hash(runtimeType,accountNumber);

@override
String toString() {
  return 'ChangeLeverageEvent.onGetLeverages(accountNumber: $accountNumber)';
}


}

/// @nodoc
abstract mixin class _$OnGetLeveragesCopyWith<$Res> implements $ChangeLeverageEventCopyWith<$Res> {
  factory _$OnGetLeveragesCopyWith(_OnGetLeverages value, $Res Function(_OnGetLeverages) _then) = __$OnGetLeveragesCopyWithImpl;
@override @useResult
$Res call({
 String accountNumber
});




}
/// @nodoc
class __$OnGetLeveragesCopyWithImpl<$Res>
    implements _$OnGetLeveragesCopyWith<$Res> {
  __$OnGetLeveragesCopyWithImpl(this._self, this._then);

  final _OnGetLeverages _self;
  final $Res Function(_OnGetLeverages) _then;

/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountNumber = null,}) {
  return _then(_OnGetLeverages(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnChangeLeverages implements ChangeLeverageEvent {
   _OnChangeLeverages({required this.selectedLeverage, required this.accountNumber});
  

 final  String selectedLeverage;
@override final  String accountNumber;

/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnChangeLeveragesCopyWith<_OnChangeLeverages> get copyWith => __$OnChangeLeveragesCopyWithImpl<_OnChangeLeverages>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChangeLeverages&&(identical(other.selectedLeverage, selectedLeverage) || other.selectedLeverage == selectedLeverage)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber));
}


@override
int get hashCode => Object.hash(runtimeType,selectedLeverage,accountNumber);

@override
String toString() {
  return 'ChangeLeverageEvent.onChangeLeverages(selectedLeverage: $selectedLeverage, accountNumber: $accountNumber)';
}


}

/// @nodoc
abstract mixin class _$OnChangeLeveragesCopyWith<$Res> implements $ChangeLeverageEventCopyWith<$Res> {
  factory _$OnChangeLeveragesCopyWith(_OnChangeLeverages value, $Res Function(_OnChangeLeverages) _then) = __$OnChangeLeveragesCopyWithImpl;
@override @useResult
$Res call({
 String selectedLeverage, String accountNumber
});




}
/// @nodoc
class __$OnChangeLeveragesCopyWithImpl<$Res>
    implements _$OnChangeLeveragesCopyWith<$Res> {
  __$OnChangeLeveragesCopyWithImpl(this._self, this._then);

  final _OnChangeLeverages _self;
  final $Res Function(_OnChangeLeverages) _then;

/// Create a copy of ChangeLeverageEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedLeverage = null,Object? accountNumber = null,}) {
  return _then(_OnChangeLeverages(
selectedLeverage: null == selectedLeverage ? _self.selectedLeverage : selectedLeverage // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$ChangeLeverageState {

 ChangeLeverageProcessState get processState; ChangeLeverageProcessState get changeLeverage; List<int> get leverages; int? get selectedLeverage;
/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChangeLeverageStateCopyWith<ChangeLeverageState> get copyWith => _$ChangeLeverageStateCopyWithImpl<ChangeLeverageState>(this as ChangeLeverageState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.changeLeverage, changeLeverage) || other.changeLeverage == changeLeverage)&&const DeepCollectionEquality().equals(other.leverages, leverages)&&(identical(other.selectedLeverage, selectedLeverage) || other.selectedLeverage == selectedLeverage));
}


@override
int get hashCode => Object.hash(runtimeType,processState,changeLeverage,const DeepCollectionEquality().hash(leverages),selectedLeverage);

@override
String toString() {
  return 'ChangeLeverageState(processState: $processState, changeLeverage: $changeLeverage, leverages: $leverages, selectedLeverage: $selectedLeverage)';
}


}

/// @nodoc
abstract mixin class $ChangeLeverageStateCopyWith<$Res>  {
  factory $ChangeLeverageStateCopyWith(ChangeLeverageState value, $Res Function(ChangeLeverageState) _then) = _$ChangeLeverageStateCopyWithImpl;
@useResult
$Res call({
 ChangeLeverageProcessState processState, ChangeLeverageProcessState changeLeverage, List<int> leverages, int? selectedLeverage
});


$ChangeLeverageProcessStateCopyWith<$Res> get processState;$ChangeLeverageProcessStateCopyWith<$Res> get changeLeverage;

}
/// @nodoc
class _$ChangeLeverageStateCopyWithImpl<$Res>
    implements $ChangeLeverageStateCopyWith<$Res> {
  _$ChangeLeverageStateCopyWithImpl(this._self, this._then);

  final ChangeLeverageState _self;
  final $Res Function(ChangeLeverageState) _then;

/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? changeLeverage = null,Object? leverages = null,Object? selectedLeverage = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ChangeLeverageProcessState,changeLeverage: null == changeLeverage ? _self.changeLeverage : changeLeverage // ignore: cast_nullable_to_non_nullable
as ChangeLeverageProcessState,leverages: null == leverages ? _self.leverages : leverages // ignore: cast_nullable_to_non_nullable
as List<int>,selectedLeverage: freezed == selectedLeverage ? _self.selectedLeverage : selectedLeverage // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}
/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChangeLeverageProcessStateCopyWith<$Res> get processState {
  
  return $ChangeLeverageProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChangeLeverageProcessStateCopyWith<$Res> get changeLeverage {
  
  return $ChangeLeverageProcessStateCopyWith<$Res>(_self.changeLeverage, (value) {
    return _then(_self.copyWith(changeLeverage: value));
  });
}
}


/// @nodoc


class _ChangeLeverageState implements ChangeLeverageState {
   _ChangeLeverageState({this.processState = const ChangeLeverageProcessState.loading(), this.changeLeverage = const ChangeLeverageProcessState.initial(), final  List<int> leverages = const [], this.selectedLeverage}): _leverages = leverages;
  

@override@JsonKey() final  ChangeLeverageProcessState processState;
@override@JsonKey() final  ChangeLeverageProcessState changeLeverage;
 final  List<int> _leverages;
@override@JsonKey() List<int> get leverages {
  if (_leverages is EqualUnmodifiableListView) return _leverages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_leverages);
}

@override final  int? selectedLeverage;

/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeLeverageStateCopyWith<_ChangeLeverageState> get copyWith => __$ChangeLeverageStateCopyWithImpl<_ChangeLeverageState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeLeverageState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.changeLeverage, changeLeverage) || other.changeLeverage == changeLeverage)&&const DeepCollectionEquality().equals(other._leverages, _leverages)&&(identical(other.selectedLeverage, selectedLeverage) || other.selectedLeverage == selectedLeverage));
}


@override
int get hashCode => Object.hash(runtimeType,processState,changeLeverage,const DeepCollectionEquality().hash(_leverages),selectedLeverage);

@override
String toString() {
  return 'ChangeLeverageState(processState: $processState, changeLeverage: $changeLeverage, leverages: $leverages, selectedLeverage: $selectedLeverage)';
}


}

/// @nodoc
abstract mixin class _$ChangeLeverageStateCopyWith<$Res> implements $ChangeLeverageStateCopyWith<$Res> {
  factory _$ChangeLeverageStateCopyWith(_ChangeLeverageState value, $Res Function(_ChangeLeverageState) _then) = __$ChangeLeverageStateCopyWithImpl;
@override @useResult
$Res call({
 ChangeLeverageProcessState processState, ChangeLeverageProcessState changeLeverage, List<int> leverages, int? selectedLeverage
});


@override $ChangeLeverageProcessStateCopyWith<$Res> get processState;@override $ChangeLeverageProcessStateCopyWith<$Res> get changeLeverage;

}
/// @nodoc
class __$ChangeLeverageStateCopyWithImpl<$Res>
    implements _$ChangeLeverageStateCopyWith<$Res> {
  __$ChangeLeverageStateCopyWithImpl(this._self, this._then);

  final _ChangeLeverageState _self;
  final $Res Function(_ChangeLeverageState) _then;

/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? changeLeverage = null,Object? leverages = null,Object? selectedLeverage = freezed,}) {
  return _then(_ChangeLeverageState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ChangeLeverageProcessState,changeLeverage: null == changeLeverage ? _self.changeLeverage : changeLeverage // ignore: cast_nullable_to_non_nullable
as ChangeLeverageProcessState,leverages: null == leverages ? _self._leverages : leverages // ignore: cast_nullable_to_non_nullable
as List<int>,selectedLeverage: freezed == selectedLeverage ? _self.selectedLeverage : selectedLeverage // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChangeLeverageProcessStateCopyWith<$Res> get processState {
  
  return $ChangeLeverageProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of ChangeLeverageState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChangeLeverageProcessStateCopyWith<$Res> get changeLeverage {
  
  return $ChangeLeverageProcessStateCopyWith<$Res>(_self.changeLeverage, (value) {
    return _then(_self.copyWith(changeLeverage: value));
  });
}
}

/// @nodoc
mixin _$ChangeLeverageProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChangeLeverageProcessState()';
}


}

/// @nodoc
class $ChangeLeverageProcessStateCopyWith<$Res>  {
$ChangeLeverageProcessStateCopyWith(ChangeLeverageProcessState _, $Res Function(ChangeLeverageProcessState) __);
}


/// @nodoc


class ChangeLeverageInitial implements ChangeLeverageProcessState {
  const ChangeLeverageInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChangeLeverageProcessState.initial()';
}


}




/// @nodoc


class ChangeLeverageLoading implements ChangeLeverageProcessState {
  const ChangeLeverageLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChangeLeverageProcessState.loading()';
}


}




/// @nodoc


class ChangeLeverageSuccess implements ChangeLeverageProcessState {
  const ChangeLeverageSuccess();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChangeLeverageProcessState.success()';
}


}




/// @nodoc


class ChangeLeverageError implements ChangeLeverageProcessState {
  const ChangeLeverageError({required this.message});
  

 final  String message;

/// Create a copy of ChangeLeverageProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChangeLeverageErrorCopyWith<ChangeLeverageError> get copyWith => _$ChangeLeverageErrorCopyWithImpl<ChangeLeverageError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChangeLeverageError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'ChangeLeverageProcessState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $ChangeLeverageErrorCopyWith<$Res> implements $ChangeLeverageProcessStateCopyWith<$Res> {
  factory $ChangeLeverageErrorCopyWith(ChangeLeverageError value, $Res Function(ChangeLeverageError) _then) = _$ChangeLeverageErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$ChangeLeverageErrorCopyWithImpl<$Res>
    implements $ChangeLeverageErrorCopyWith<$Res> {
  _$ChangeLeverageErrorCopyWithImpl(this._self, this._then);

  final ChangeLeverageError _self;
  final $Res Function(ChangeLeverageError) _then;

/// Create a copy of ChangeLeverageProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(ChangeLeverageError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
