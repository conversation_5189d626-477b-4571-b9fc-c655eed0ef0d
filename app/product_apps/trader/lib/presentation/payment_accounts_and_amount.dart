import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent paymentAccountsAndAmount() {
  // todo (aakash): Add error cases here
  return DisplayableComponent(
    title: 'Payment Accounts and Amount',
    children: [
      DisplayableComponent(
        title: 'Payment Accounts and Amount',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/client/accounts': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/payment_list_of_accounts/success.json',
                ),
              ],
              '/api/v1/conversionRate': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/conversion_rate/success.json',
                ),
              ],
            });

          return DepositAccountsAndAmountScreen(
            paymentMethod: DepositPaymentMethod(
              name: "card",
              mop: DepositMop.card,
              currencyAmountDetails: [
                CurrencyAmountDetail(
                  currency: 'AED',
                  suggestedAmounts: [500, 1000, 1500],
                  minAmount: 1,
                  maxAmount: 1000,
                ),
                CurrencyAmountDetail(
                  currency: 'USD',
                  suggestedAmounts: [50, 100, 150],
                  minAmount: 1,
                  maxAmount: 2000,
                ),
              ],
              currencies: ["USD", "AED"],
            ),
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
    ],
  );
}
