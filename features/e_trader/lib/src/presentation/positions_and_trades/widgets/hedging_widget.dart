import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class HedgingWidget extends StatelessWidget {
  const HedgingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    var localization = EquitiLocalization.of(context);

    return Semantics(
      identifier: 'hedged_button_positions',
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        child: DuploText(
          text: localization.trader_hedged,
          style: context.duploTextStyles.textXs,
          color: theme.text.textPrimary,
          fontWeight: DuploFontWeight.regular,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(width: 2, color: theme.border.borderPrimary),
          color: theme.background.bgTertiary,
        ),
      ),
    );
  }
}
