import 'package:user_account/user_account.dart';
import 'package:domain/domain.dart';

abstract interface class HubNavigation {
  void gotoBrokerage();
  void gotoGold();
  void gotoWealth();
  void gotoSettings();
  void gotoProfile(ClientProfileData clientProfileData);
  void gotoHistoricalPerformance();
  void logout();
  void goToDepositPaymentOptions(DepositFlowConfig depositFlowConfig);
  void goToWithdrawPaymentOptions();
  void goToTransferFundsScreen();
  void goToTrading();
}
