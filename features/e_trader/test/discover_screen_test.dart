// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/statements_tab.dart';
import 'scenarios/statements_empty_scenario.dart';
import 'scenarios/statements_success_scenario.dart';
import 'scenarios/events_empty_scenario.dart';
import 'scenarios/events_failure_scenario.dart';
import 'scenarios/events_success_scenario.dart';
import 'scenarios/news_success_scenario.dart';
import 'package:e_trader/src/presentation/discover/discover_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import './step/i_wait_for_seconds.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import './step/i_tap_on_the_duplosearchbox.dart';
import './step/i_enter_in_the_search_field.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Discover Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Open news tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Open news tab''');
        await theAppIsRendered(
          tester,
          DiscoverScreen(),
          scenarios: [newsSuccessScenario],
        );
        await iWait(tester);
        await screenshotVerifiedWithCustomPump(tester, 'open_news_tab');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Open news tab''', success);
      }
    });
    testGoldens('''Faild loading news''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Faild loading news''');
        await theAppIsRendered(
          tester,
          DiscoverScreen(),
          scenarios: [newsFailureScenario],
        );
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'failed_laoding_news');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Faild loading news''', success);
      }
    });
    testGoldens('''Empty events''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Empty events''');
        await theAppIsRendered(
          tester,
          DiscoverScreen(),
          scenarios: [eventsEmptyScenario],
        );
        await iTapIdentifier(tester, 'discover_events_tab');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'empty_events');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Empty events''', success);
      }
    });
    testGoldens('''Events tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Events tab''');
        await theAppIsRendered(
          tester,
          DiscoverScreen(),
          scenarios: [eventsSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, 'discover_events_tab');
        await iWaitForSeconds(tester, 1);
        await screenshotVerified(tester, 'event_tab');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Events tab''', success);
      }
    });
    testGoldens('''News search''', (tester) async {
      var success = true;
      try {
        await beforeEach('''News search''');
        await theAppIsRendered(
          tester,
          DiscoverScreen(),
          scenarios: [newsSuccessScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "CAD");
        await iWait(tester);
        await screenshotVerified(tester, 'news_search_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''News search''', success);
      }
    });
  });
}
