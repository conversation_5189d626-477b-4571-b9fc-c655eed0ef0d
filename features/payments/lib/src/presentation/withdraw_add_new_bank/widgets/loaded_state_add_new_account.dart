import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/domain/model/transfer_type.dart';
import 'package:payment/src/domain/model/withdraw_add_new_account_param/withdraw_add_new_account_param.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/withdraw_fees_display.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/bloc/withdraw_add_new_bank_bloc.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/widgets/account_information_form.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/widgets/bank_information_form.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/widgets/withdraw_add_new_bank_bottom_actions.dart';
import 'package:prelude/prelude.dart';

class LoadedStateAddNewAccount extends StatefulWidget {
  const LoadedStateAddNewAccount({
    super.key,
    required this.withdrawFlowParams,
    required this.nameOfAccountHolderController,
    required this.bankNameController,
    required this.branchNameController,
    required this.swiftBicController,
    required this.accountNumberController,
    required this.accountNicknameController,
  });
  final WithdrawFlowParams withdrawFlowParams;
  final TextEditingController nameOfAccountHolderController;
  final TextEditingController bankNameController;
  final TextEditingController branchNameController;
  final TextEditingController swiftBicController;
  final TextEditingController accountNumberController;
  final TextEditingController accountNicknameController;
  @override
  State<LoadedStateAddNewAccount> createState() =>
      _LoadedStateAddNewAccountState();
}

class _LoadedStateAddNewAccountState extends State<LoadedStateAddNewAccount> {
  Timer? _debounceTimer;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localization.payments_withdraw_to_your_bank_account,
      ),
      // todo(sambhav aakash): need to confirm if this will be sticky or not
      // bottomNavigationBar: Padding(
      //   padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
      //   child: WithdrawAddNewBankBottomActions(),
      // ),
      body: Scrollbar(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(DuploSpacing.spacing_xl_16),
                  decoration: BoxDecoration(
                    border: Border.all(color: theme.border.borderPrimary),
                    color: theme.background.bgSecondary,
                    borderRadius: BorderRadius.circular(
                      DuploRadius.radius_sm_6,
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Assets.images.infoIcon.svg(
                        height: 20,
                        width: 20,
                        colorFilter: ColorFilter.mode(
                          theme.utility.utilityGray600,
                          BlendMode.srcIn,
                        ),
                      ),
                      SizedBox(width: DuploSpacing.spacing_xl_16),
                      Expanded(
                        child: DuploText(
                          text: localization.payments_submit_form_description,
                          style: textStyles.textSm,
                          color: theme.text.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                BankInformationForm(
                  nameOfAccountHolderController:
                      widget.nameOfAccountHolderController,
                  bankNameController: widget.bankNameController,
                  branchNameController: widget.branchNameController,
                  swiftBicController: widget.swiftBicController,
                  onChange: _onChangeCalled,
                ),
                AccountInformationForm(
                  accountNumberController: widget.accountNumberController,
                  accountNicknameController: widget.accountNicknameController,
                  onChange: _onChangeCalled,
                ),
                SizedBox(height: DuploSpacing.spacing_md_8),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: DuploSpacing.spacing_xl_16,
                  ),
                  child: BlocBuilder<
                    WithdrawAddNewBankBloc,
                    WithdrawAddNewBankState
                  >(
                    buildWhen:
                        (previous, current) =>
                            previous.selectedTransferTypeIndex !=
                                current.selectedTransferTypeIndex ||
                            previous.hasInsufficientFunds !=
                                current.hasInsufficientFunds,
                    builder: (ctx, state) {
                      // Get the selected transfer type from state
                      final hasValidSelection =
                          state.transferType.isNotEmpty &&
                          state.selectedTransferTypeIndex != -1;
                      final selectedTransferType =
                          hasValidSelection
                              ? state.transferType.elementAtOrNull(
                                state.selectedTransferTypeIndex,
                              )
                              : null;

                      // Find matching TransferType enum value
                      final transferType = _findMatchingTransferType(
                        selectedTransferType,
                      );

                      return Column(
                        children: [
                          // Show insufficient funds error if present
                          if (state.hasInsufficientFunds)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: DuploAlertMessage.error(
                                title:
                                    localization
                                        .payments_insufficient_funds_title,
                                subtitle: _getInsufficientFundsMessage(
                                  context,
                                  state,
                                ),
                              ),
                            ),
                          WithdrawFeesDisplay(
                            args: (
                              amount:
                                  widget.withdrawFlowParams.amount.toDouble(),
                              accountId:
                                  widget.withdrawFlowParams.tradingAccountId,
                              accountCurrency:
                                  widget.withdrawFlowParams.accountCurrency,
                              transactionCurrency:
                                  widget.withdrawFlowParams.currency,
                              paymentType:
                                  widget.withdrawFlowParams.paymentType.name,
                              convertedAmount:
                                  widget.withdrawFlowParams.amount.toDouble(),
                              transferType: transferType,
                            ),
                            content: (
                              idle: localization.payments_fee_idle_text,
                              loading: localization.payments_fee_loading_text,
                              zeroFees:
                                  localization.payments_fee_zero_fees_text,
                              nonZeroFees: (fees, total, currency) {
                                final formattedFees =
                                    EquitiFormatter.formatNumber(
                                      value: fees,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    );
                                return localization
                                    .payments_fee_non_zero_fees_text(
                                      formattedFees,
                                      currency,
                                    );
                              },
                            ),
                            onFeesChange: (withdrawFeesState) {
                              // Extract fees from the state and trigger validation
                              final fees = withdrawFeesState.fees;
                              ctx.read<WithdrawAddNewBankBloc>().add(
                                WithdrawAddNewBankEvent.onFeesChanged(
                                  fees,
                                  widget.withdrawFlowParams,
                                ),
                              );
                            },
                          ),
                        ],
                      );
                    },
                  ),
                ),
                WithdrawAddNewBankBottomActions(
                  onRequestWithdrawButtonPressed:
                      () => _onRequestWithdrawButtonPressed(context),
                  onWithdrawalDetailsPressed: () {
                    log('onWithdrawalDetailsPressed');
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onRequestWithdrawButtonPressed(BuildContext ctx) {
    final bloc = ctx.read<WithdrawAddNewBankBloc>();
    final WithdrawAddNewAccountParam formData = _getParamData();
    bloc.add(
      WithdrawAddNewBankEvent.onRequestWithdrawButtonPressed(
        withdrawAddNewAccountParam: formData,
        withdrawFlowParams: widget.withdrawFlowParams,
      ),
    );
  }

  WithdrawAddNewAccountParam _getParamData() {
    final state = context.read<WithdrawAddNewBankBloc>().state;

    // Get selected country from state
    final selectedCountry = _getSelectedCountry(state);

    // Get selected transfer type from state
    final selectedTransferType = _getSelectedTransferType(state);

    return WithdrawAddNewAccountParam(
      nameOfAccountHolder: widget.nameOfAccountHolderController.text,
      bankName: widget.bankNameController.text,
      branchName: widget.branchNameController.text,
      swiftBic: widget.swiftBicController.text,
      accountNumber: widget.accountNumberController.text,
      accountNickname: widget.accountNicknameController.text,
      transferType: selectedTransferType,
      country: selectedCountry,
    );
  }

  CountryData? _getSelectedCountry(WithdrawAddNewBankState state) {
    final hasValidCountrySelection =
        state.countries.isNotEmpty && state.selectedBankLocationIndex != -1;
    return hasValidCountrySelection
        ? state.countries.elementAtOrNull(state.selectedBankLocationIndex)
        : null;
  }

  String? _getSelectedTransferType(WithdrawAddNewBankState state) {
    final hasValidTransferTypeSelection =
        state.transferType.isNotEmpty && state.selectedTransferTypeIndex != -1;
    return hasValidTransferTypeSelection
        ? state.transferType.elementAtOrNull(state.selectedTransferTypeIndex)
        : null;
  }

  void _onChangeCalled() {
    _cancelPreviousTimer();
    _startNewTimer();
  }

  void _cancelPreviousTimer() {
    _debounceTimer?.cancel();
  }

  void _startNewTimer() {
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _processFormChange();
    });
  }

  void _processFormChange() {
    final bloc = context.read<WithdrawAddNewBankBloc>();
    final formData = _getParamData();
    bloc.add(
      WithdrawAddNewBankEvent.onFormChanged(
        withdrawAddNewAccountParam: formData,
      ),
    );
  }

  TransferType _findMatchingTransferType(String? selectedTransferType) {
    if (selectedTransferType == null) return TransferType.none;

    return TransferType.values
            .where(
              (type) =>
                  type.name.toLowerCase() == selectedTransferType.toLowerCase(),
            )
            .firstOrNull ??
        TransferType.none;
  }

  /// Generates localized insufficient funds error message
  String _getInsufficientFundsMessage(
    BuildContext context,
    WithdrawAddNewBankState state,
  ) {
    final localization = EquitiLocalization.of(context);

    // Return empty string if validation data is not available
    if (state.validationWithdrawalAmount == null ||
        state.validationAccountBalance == null ||
        state.validationWithdrawalFees == null ||
        state.validationCurrency == null) {
      return '';
    }

    final formattedBalance = state.validationAccountBalance!.toStringAsFixed(2);
    final totalRequired =
        state.validationWithdrawalAmount! + state.validationWithdrawalFees!;
    final formattedTotal = totalRequired.toStringAsFixed(2);
    final currency = state.validationCurrency!;

    if (state.validationWithdrawalFees! > 0) {
      final formattedFees = state.validationWithdrawalFees!.toStringAsFixed(2);
      return localization.payments_insufficient_funds_with_fees(
        formattedBalance,
        formattedTotal,
        formattedFees,
        currency,
      );
    }
    return localization.payments_insufficient_funds_without_fees(
      formattedBalance,
      formattedTotal,
      currency,
    );
  }
}
