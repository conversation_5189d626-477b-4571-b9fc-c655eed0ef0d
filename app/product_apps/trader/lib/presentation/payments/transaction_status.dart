// ignore_for_file: no-empty-block

import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';

import '../../di/di_initializer.dart';

const kUrl =
    "https://pbl-equiti-staging.equiti-pay.com/v2/card/GzIEhoZpJ07cWqbqmisa?is_pci=false";

DisplayableComponent transactionStatus() {
  return DisplayableComponent(
    title: 'Transaction Status',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          _returnMockStatus("success");
          return DepositStatusScreen(
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Pending',
        onTap: () {
          _returnMockStatus("pending");
          return DepositStatusScreen(
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Rejected',
        onTap: () {
          _returnMockStatus("rejected");
          return DepositStatusScreen(
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/v1/payment/status/123': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/status/status_new.json',
                ),
                MockResponse(
                  bodyFilePath: 'resources/mocks/status/status_new.json',
                ),
                MockResponse(
                  bodyFilePath: 'resources/mocks/status/failure.json',
                ),
              ],
            });
          return DepositStatusScreen(
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",
              depositType: DepositType.additional,
            ),
          );
        },
      ),
    ],
  );
}

void _returnMockStatus(String status) {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'api/v1/payment/status/123': [
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(
          bodyFilePath: 'resources/mocks/status/status_$status.json',
        ),
      ],
    });
}
