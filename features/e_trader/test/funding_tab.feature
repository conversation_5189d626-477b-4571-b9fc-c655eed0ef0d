import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/funding_tab.dart';
import 'scenarios/funding_empty_scenario.dart';
import 'scenarios/funding_success_scenario.dart';

Feature: Fudning Tab 

@testMethodName: testGoldens
Scenario Outline: User views Funding Tab
  Given The {FundingTab()} app is rendered <scenario>
  And I wait
  And screenshot verified <golden_file_name>
  Examples:
  | scenario                                  | golden_file_name      |
  | scenarios: [fundingSuccessScenario]       | 'funding_success'     |
  | scenarios: [fundingEmptyScenario]         | 'funding_empty'       |

@testMethodName: testGoldens
Scenario: User select sort Funding
    Given The {FundingTab()} app is rendered {scenarios:[fundingSuccessScenario]}
    And I wait
    Then i tap {"grouped_buttons_sort_funding"} identifier
    And I wait
    Then screenshot verified {'funding_sort'}

@testMethodName: testGoldens
Scenario: User select filter Funding
    Given The {FundingTab()} app is rendered {scenarios:[fundingSuccessScenario]}
    And I wait
    Then i tap {"grouped_buttons_filter_funding"} identifier
    And I wait
    Then screenshot verified {'funding_filter'}

@testMethodName: testGoldens
Scenario: User select filter Funding but no data
    Given The {FundingTab()} app is rendered {scenarios:[fundingSuccessScenario]}
    And I wait
    Then i tap {"grouped_buttons_filter_funding"} identifier
    Given The {FundingTab()} app is rendered {scenarios:[fundingEmptyScenario]}
    And I wait
    Then i tap {"MoneyOut"} identifier
    Then i tap {"apply_filters_funding"} identifier
    And I wait
    Then screenshot verified {'funding_filter_no_data'}