import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:host/host.dart';
import 'package:onboarding/onboarding.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent createAccount() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/v1/broker-settings': [
        MockResponse(
          bodyFilePath: 'resources/mocks/broker_settings/success.json',
        ),
      ],
    });
  return DisplayableComponent(
    title: 'Create Account',
    children: [
      DisplayableComponent(
        title: 'First Live Account',
        onTap: () {
          return CreateAccountBannerScreen();
        },
      ),
      DisplayableComponent(
        title: 'Additional Live Account',
        onTap: () {
          return CreateAccountMainScreen(
            createAccountFlow: CreateAccountFlow.additionalLiveAccount,
          );
        },
      ),
      DisplayableComponent(
        title: 'Demo Account',
        onTap: () {
          return CreateAccountMainScreen(
            createAccountFlow: CreateAccountFlow.demoAccount,
          );
        },
      ),
    ],
  );
}
