// ignore_for_file: prefer-number-format
import 'package:decimal/decimal.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/order_limit/order_limit_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/model/order_limit_config.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class OrderLimitWidget extends StatelessWidget {
  const OrderLimitWidget({
    super.key,
    required this.tradeType,
    required this.pipValue,
    required this.pipMultipler,
    required this.pipSize,
    required this.currentPrice,
    required this.initialPrice,
    required this.digits,
    required this.onOrderLimitStateChanged,
    required this.orderType,
    this.isEnabled = false,
    this.isDisabled = false,
    this.orderLimitType = OrderLimitType.takeProfit,
    this.methodOrder = const [
      MethodTypeEnum.distance,
      MethodTypeEnum.price,
      MethodTypeEnum.profitOrLoss,
    ],
  });

  final TradeType tradeType;
  final OrderLimitType orderLimitType;
  final double pipValue;
  final double pipMultipler;
  final double pipSize;
  final double currentPrice;
  final double initialPrice;
  final int digits;
  final bool isEnabled;
  final bool isDisabled;
  final List<MethodTypeEnum> methodOrder;
  final OrderType orderType;

  final void Function(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  )
  onOrderLimitStateChanged;

  @override
  Widget build(BuildContext context) {
    final methodsMap = {
      MethodTypeEnum.distance: MethodType.distance(
        pipValue: Decimal.parse(pipValue.toString()),
        pipSize: Decimal.parse(pipSize.toString()),
        orderLimitType: orderLimitType,
        currentPrice: Decimal.parse(currentPrice.toString()),
        tradeType: tradeType,
      ),
      MethodTypeEnum.price: MethodType.price(
        pipValue: Decimal.parse(pipValue.toString()),
        pipSize: Decimal.parse(pipSize.toString()),
        pipMultiplier: Decimal.parse(pipMultipler.toString()),
        orderLimitType: orderLimitType,
        currentPrice: Decimal.parse(currentPrice.toString()),
        tradeType: tradeType,
      ),
      MethodTypeEnum.profitOrLoss: MethodType.profitOrLoss(
        pipValue: Decimal.parse(pipValue.toString()),
        pipSize: Decimal.parse(pipSize.toString()),
        orderLimitType: orderLimitType,
        currentPrice: Decimal.parse(currentPrice.toString()),
        tradeType: tradeType,
      ),
    };

    final methods = methodOrder.map((type) => methodsMap[type]!).toList();

    final config = OrderLimitConfig(
      methods: methods,
      currentPrice: Decimal.parse(currentPrice.toString()),
      initialPrice: Decimal.parse(initialPrice.toString()),
      digits: digits,
    );
    return BlocProvider(
      create:
          (_) => diContainer<OrderLimitBloc>(param1: config, param2: isEnabled),
      child: _OrderLimitContent(
        config: config,
        pipSize: pipSize,
        isEnabled: isEnabled,
        isDisabled: isDisabled,
        tradeType: tradeType,
        onOrderLimitStateChanged: onOrderLimitStateChanged,
        locale: Localizations.localeOf(context).toString(),
        digits: digits,
        orderType: orderType,
      ),
    );
  }
}

class _OrderLimitContent extends StatefulWidget {
  const _OrderLimitContent({
    required this.config,
    required this.isEnabled,
    required this.isDisabled,
    required this.digits,
    required this.locale,
    required this.onOrderLimitStateChanged,
    required this.tradeType,
    required this.pipSize,
    required this.orderType,
  });

  final OrderLimitConfig config;
  final bool isEnabled;
  final bool isDisabled;
  final int digits;
  final String locale;
  final TradeType tradeType;
  final double pipSize;
  final OrderType orderType;

  final void Function(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  )
  onOrderLimitStateChanged;

  @override
  State<_OrderLimitContent> createState() => _OrderLimitContentState();
}

class _OrderLimitContentState extends State<_OrderLimitContent> {
  late final TextEditingController _inputController;
  final GlobalKey _widgetKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    if (widget.config.methods.firstOrNull is PriceMethod) {
      _inputController.text = EquitiFormatter.formatDynamicDigits(
        value:
            context.read<OrderLimitBloc>().state.calculations.price.toDouble(),
        digits: widget.digits,
        locale: widget.locale,
      );
    } else if (widget.config.methods.firstOrNull is ProfitOrLossMethod) {
      _inputController.text = EquitiFormatter.formatDynamicDigits(
        value:
            context
                .read<OrderLimitBloc>()
                .state
                .calculations
                .profitOrLoss
                .toDouble(),
        digits: 2,
        locale: widget.locale,
      );
    } else if (widget.config.methods.firstOrNull is DistanceMethod) {
      _inputController.text = EquitiFormatter.formatDynamicDigits(
        value:
            context
                .read<OrderLimitBloc>()
                .state
                .calculations
                .distance
                .toDouble(),
        digits: widget.digits,
        locale: widget.locale,
      );
    }
  }

  @override
  void didUpdateWidget(covariant _OrderLimitContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      context.read<OrderLimitBloc>().add(
        OrderLimitEvent.valuesChanged(config: widget.config),
      );
    }
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  void _updateInputValue(OrderLimitState state) {
    final calculations = state.calculations;

    final value = switch (state.selectedMethodType) {
      DistanceMethod() => () {
        return EquitiFormatter.formatDynamicDigits(
          value: calculations.distance.toDouble(),
          digits: widget.digits,
          locale: Localizations.localeOf(context).toString(),
        );
      }(),
      PriceMethod() => () {
        return EquitiFormatter.formatDynamicDigits(
          value: calculations.price.toDouble(),
          digits: widget.digits,
          locale: Localizations.localeOf(context).toString(),
        );
      }(),
      ProfitOrLossMethod() => () {
        return EquitiFormatter.formatDynamicDigits(
          value: calculations.profitOrLoss.toDouble(),
          digits: 2,
          locale: Localizations.localeOf(context).toString(),
        );
      }(),
    };
    _inputController.text = value;
    _inputController.selection = TextSelection.collapsed(offset: value.length);
  }

  @override
  Widget build(BuildContext _) => MultiBlocListener(
    listeners: [
      BlocListener<OrderLimitBloc, OrderLimitState>(
        listenWhen:
            (previous, current) =>
                previous.selectedMethodType.runtimeType !=
                current.selectedMethodType.runtimeType,
        listener: (context, state) {
          _updateInputValue(state);
        },
      ),
      BlocListener<OrderLimitBloc, OrderLimitState>(
        listenWhen:
            (previous, current) =>
                previous.processState != current.processState,
        listener: (context, state) {
          widget.onOrderLimitStateChanged(state.processState);
        },
      ),
      BlocListener<OrderLimitBloc, OrderLimitState>(
        listenWhen:
            (previous, current) =>
                !previous.processState.isActive() &&
                current.processState.isActive(),
        listener: (context, state) {
          // Auto-scroll when component becomes enabled
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_widgetKey.currentContext != null) {
              Scrollable.ensureVisible(
                _widgetKey.currentContext!,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });
        },
      ),
    ],
    child: BlocBuilder<OrderLimitBloc, OrderLimitState>(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        final localization = EquitiLocalization.of(context);

        final isProfit =
            state.selectedMethodType.orderLimitType ==
            OrderLimitType.takeProfit;
        String _getConiditionText() {
          switch (widget.tradeType) {
            case TradeType.buy:
              switch (state.selectedMethodType.orderLimitType) {
                case OrderLimitType.takeProfit:
                  return localization.trader_lessThan;
                case OrderLimitType.stopLoss:
                  return localization.trader_moreThan;
              }
            case TradeType.sell:
              switch (state.selectedMethodType.orderLimitType) {
                case OrderLimitType.takeProfit:
                  return localization.trader_moreThan;
                case OrderLimitType.stopLoss:
                  return localization.trader_lessThan;
              }
          }
        }

        String _getErrorMessage(
          OrderLimitErrorCode errorCode,
          OrderType orderType,
        ) {
          return switch (errorCode) {
            OrderLimitErrorCode.invalidInput =>
              localization.trader_invalidInput,
            OrderLimitErrorCode.invalidPrice =>
              state.selectedMethodType.orderLimitType ==
                      OrderLimitType.takeProfit
                  ? (orderType == OrderType.marketOrder
                      ? localization.trader_tpPriceValidation(
                        _getConiditionText(),
                      )
                      : localization.trader_tpOrderPriceValidation(
                        _getConiditionText(),
                      ))
                  : (orderType == OrderType.marketOrder
                      ? localization.trader_slPriceValidation(
                        _getConiditionText(),
                      )
                      : localization.trader_slOrderPriceValidation(
                        _getConiditionText(),
                      )),
            OrderLimitErrorCode.invalidDistance =>
              localization.trader_invalidDistance,
            OrderLimitErrorCode.invalidProfitOrLoss =>
              localization.trader_invalidProfitLoss,
          };
        }

        return StepperControlWidget(
          inputSpacing: state.processState.isActive() ? 8.0 : 0.0,
          key: _widgetKey,
          leadingWidget: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IgnorePointer(
                ignoring: widget.isDisabled,
                child: DuploSwitchControl(
                  initialValue: state.processState.isActive(),
                  onChanged:
                      (value) => context.read<OrderLimitBloc>().add(
                        OrderLimitEvent.orderLimitEnabled(isEnabled: value),
                      ),
                ),
              ),
              Flexible(
                child: DuploText(
                  text:
                      state.selectedMethodType.orderLimitType ==
                              OrderLimitType.takeProfit
                          ? localization.trader_takeProfit
                          : localization.trader_stopLoss,
                  style: context.duploTextStyles.textMd,
                  color: context.duploTheme.text.textPrimary,
                  textAlign: TextAlign.start,
                  maxLines: 1,
                  fontWeight: DuploFontWeight.semiBold,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: () {
                  final String title =
                      state.selectedMethodType.orderLimitType ==
                              OrderLimitType.takeProfit
                          ? localization.trader_takeProfit
                          : localization.trader_stopLoss;

                  final String description =
                      state.selectedMethodType.orderLimitType ==
                              OrderLimitType.takeProfit
                          ? localization.trader_takeProfitMessage
                          : localization.trader_stopLossMessage;
                  DuploDialog.showInfoDialog(
                    context: context,
                    title: title,
                    description: description,
                  );
                },
                child: trader.Assets.images.infoCircle.svg(
                  width: 24,
                  height: 24,
                ),
              ),
            ],
          ),
          segmentControWidget:
              !state.processState.isActive()
                  ? null
                  : DuploHorizontalTabs<MethodType>.buttonBorder(
                    options: state.config.methods,
                    selectedValue: state.selectedMethodType,
                    onChanged: (value) {
                      if (state.processState.isActive()) {
                        context.read<OrderLimitBloc>().add(
                          OrderLimitEvent.methodChanged(value),
                        );
                      }
                    },
                    textBuilder:
                        (method) => switch (method) {
                          DistanceMethod() => 'Distance',
                          PriceMethod() => 'Price',
                          ProfitOrLossMethod profitLoss =>
                            profitLoss.orderLimitType ==
                                    OrderLimitType.takeProfit
                                ? 'Profit'
                                : 'Loss',
                        },
                  ),
          inputWidget:
              !state.processState.isActive()
                  ? const SizedBox.shrink()
                  : StepperNumberInputWidget(
                    forceNegative:
                        state.selectedMethodType.orderLimitType ==
                            OrderLimitType.stopLoss &&
                        state.selectedMethodType is ProfitOrLossMethod,
                    enabled: state.processState.isActive(),
                    controller: _inputController,
                    prescisionFactor: widget.digits,
                    textInputFormatter: switch (state.selectedMethodType) {
                      ProfitOrLossMethod() => DecimalPrecisionInputFormatter(
                        decimalRange: 2,
                      ),
                      DistanceMethod() => DecimalPrecisionInputFormatter(
                        decimalRange: 1,
                      ),
                      PriceMethod() => DecimalPrecisionInputFormatter(
                        decimalRange: widget.digits,
                      ),
                    },
                    hintText: _getInputHint(state.selectedMethodType, isProfit),
                    minimumValue: switch (state.selectedMethodType) {
                      DistanceMethod() || PriceMethod() => 0,
                      ProfitOrLossMethod() => null,
                    },
                    errorText: switch (state.processState) {
                      TradeComponentErrorState<
                        OrderLimitCalculation,
                        OrderLimitErrorCode
                      >
                      errorState =>
                        _getErrorMessage(errorState.error, widget.orderType),
                      _ => null,
                    },
                    onValueChange: (value) {
                      context.read<OrderLimitBloc>().add(
                        OrderLimitEvent.inputChanged(input: value),
                      );
                    },
                    changeFactor: switch (state.selectedMethodType) {
                      PriceMethod() => widget.pipSize,
                      _ => 1,
                    },
                  ),
          footerWidget:
              !state.processState.isActive()
                  ? null
                  : _FooterWidget(config: widget.config),
          bordered: false,
        );
      },
    ),
  );

  String _getInputHint(MethodType methodType, bool isProfit) {
    final localization = EquitiLocalization.of(context);

    return switch (methodType) {
      DistanceMethod() => localization.trader_enterDistance,
      PriceMethod() => localization.trader_enterPrice,
      ProfitOrLossMethod() =>
        isProfit
            ? localization.trader_enterProfit
            : localization.trader_enterLoss,
    };
  }
}

class _FooterWidget extends StatelessWidget {
  const _FooterWidget({required this.config});

  final OrderLimitConfig config;

  @override
  Widget build(BuildContext context) {
    final String locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    final state = context.watch<OrderLimitBloc>().state;
    final calculations = state.calculations;
    final isProfit =
        state.selectedMethodType.orderLimitType == OrderLimitType.takeProfit;
    final profitPrefix =
        calculations.profitOrLoss.toDouble().isNegative ? '' : '+';
    final firstPair = switch (state.selectedMethodType) {
      DistanceMethod() || PriceMethod() => KeyValuePair(
        label:
            isProfit
                ? localization.trader_profitTitle
                : localization.trader_lossTitle,
        value:
            state.processState.isActive()
                ? '$profitPrefix${EquitiFormatter.formatDynamicDigits(value: calculations.profitOrLoss.toDouble(), digits: 2, locale: locale)}'
                : '-',
      ),
      ProfitOrLossMethod() => KeyValuePair(
        label: localization.trader_priceTitle,
        value:
            state.processState.isActive()
                ? EquitiFormatter.formatDynamicDigits(
                  value: calculations.price.toDouble(),
                  digits: config.digits,
                  locale: locale,
                )
                : '-',
      ),
    };
    final secondPair = switch (state.selectedMethodType) {
      DistanceMethod() => KeyValuePair(
        label: localization.trader_priceTitle,
        value:
            state.processState.isActive()
                ? '${EquitiFormatter.formatDynamicDigits(value: calculations.price.toDouble(), digits: config.digits, locale: locale)}'
                : '-',
      ),
      PriceMethod() || ProfitOrLossMethod() => KeyValuePair(
        label: localization.trader_distanceTitle,
        value:
            state.processState.isActive()
                ? '${EquitiFormatter.formatDynamicDigits(digits: 1, value: calculations.distance.toDouble(), locale: locale)} PIPs'
                : '-',
      ),
    };
    return OrderLimitFooterWidget(
      firstPair: firstPair,
      secondPair: secondPair,
      firstColor:
          state.processState.isActive()
              ? isProfit
                  ? context.duploTheme.text.textSuccessPrimary
                  : context.duploTheme.text.textErrorPrimary
              : null,
    );
  }
}

enum MethodTypeEnum { distance, price, profitOrLoss }
