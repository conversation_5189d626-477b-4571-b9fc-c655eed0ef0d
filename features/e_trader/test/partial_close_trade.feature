import 'partial_close_trade_test_view.dart';
import 'scenarios/single_position_success_senario.dart';
import 'package:equiti_test/equiti_test.dart';
import 'scenarios/close_trade_failure_scenario.dart';
import 'scenarios/close_trade_success_scenario.dart';

Feature: Partial close Bottom Sheet
@testMethodName: testGoldens
Scenario: User views trade details bottom sheet
  Given The {PartialCloseTradeTestView()} app is rendered {scenarios:[singlePositionSuccessSenario]}
  And I tap {'open_partial_close_trade_sheet'} identifier
  And I wait for {1} seconds

  Then screenshot verified {'partial_close_bottom_sheet'} with custom pump

@testMethodName: testGoldens
Scenario: Partially close a position failure
  Given The {PartialCloseTradeTestView()} app is rendered {scenarios:[singlePositionSuccessSenario,closeTradefailureScenario]}
  And I tap {'open_partial_close_trade_sheet'} identifier
  And I wait
  And I tap {"partial_close_button"} duplo button
  And I wait for {4} seconds
  Then screenshot verified {'partial_close_failure'} with custom pump  


@testMethodName: testGoldens
  Scenario Outline: Input error when amount entered is outside valid range
  Given The {PartialCloseTradeTestView()} app is rendered {scenarios:[singlePositionSuccessSenario]}
  And I tap {'open_partial_close_trade_sheet'} identifier
  And I wait for {1} seconds
  When I enter <amount> into {0} input field
  And <amount> is displayed in the amount input field
  Then screenshot verified <screenshot_name> with custom pump
  Examples:
      | amount  | screenshot_name |
      | '0'     | 'lot_too_low'   |
      | '500'   | 'lot_too_high'  |

