// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'position_option_test_data.dart';
import 'package:e_trader/fusion.dart';
import 'scenarios/position_option_success_scenario.dart';
import 'scenarios/position_option_failure_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import 'package:bdd_widget_test/step/i_wait.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''PositionOption Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''User views position details bottom sheet''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User views position details bottom sheet''');
        await theAppIsRendered(
          tester,
          PositionOptionTestData(),
          scenarios: [positionOptionSuccessScenario],
        );
        await iTapIdentifier(tester, 'show_position_option_sheet');
        await screenshotVerifiedWithCustomPump(
          tester,
          'position_success_sheet',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User views position details bottom sheet''',
          success,
        );
      }
    });
    testGoldens('''User views position details bottom sheet with hub error''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach(
          '''User views position details bottom sheet with hub error''',
        );
        await theAppIsRendered(
          tester,
          PositionOptionTestData(),
          scenarios: [positionOptionFailureScenario],
        );
        await iTapIdentifier(tester, 'show_position_option_sheet');
        await screenshotVerifiedWithCustomPump(
          tester,
          'position_success_sheet_with_error',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User views position details bottom sheet with hub error''',
          success,
        );
      }
    });
    testGoldens('''User clicks on one of the close trade types''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach('''User clicks on one of the close trade types''');
        await theAppIsRendered(
          tester,
          PositionOptionTestData(),
          scenarios: [positionOptionSuccessScenario],
        );
        await iTapIdentifier(tester, 'show_position_option_sheet');
        await iWait(tester);
        await iTapIdentifier(tester, 'all_buy_trades');
        await screenshotVerifiedWithCustomPump(
          tester,
          'position_close_trade_example',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User clicks on one of the close trade types''',
          success,
        );
      }
    });
  });
}
