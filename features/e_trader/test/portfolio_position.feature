import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/portfolio/positions/portfolio_position_screen.dart';
import 'scenarios/portfolio_insights_empty_scenario.dart';
import 'scenarios/portfolio_insights_failure_scenario.dart';
import 'scenarios/portfolio_insights_success_scenario.dart';
import 'scenarios/portfolio_position_success_scenario.dart';
import 'scenarios/portfolio_position_error_scenario.dart';
import 'scenarios/portfolio_position_empty_scenario.dart';
import 'scenarios/prtofolio_orders_success_scenario.dart';
import 'scenarios/portfolio_orders_empty_scenario.dart';
import 'scenarios/portfolio_orders_failure_scenario.dart';

Feature: Portfolio Position Management

@testMethodName: testGoldens
Scenario Outline: Verify Portfolio Position screen golden tests
  Given The {PortfolioPositionScreen()} app is rendered <scenario>
  And screenshot verified <golden_file_name>
  Examples:
  | scenario                                      | golden_file_name             |
  | scenarios: [portfolioPositionSuccessScenario] | 'portfolio_position_success' |

 @testMethodName: testGoldens
  Scenario: Success order tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioPositionSuccessScenario, portfolioOrdersSuccessScenario]}
    And I tap {'orders_tab'} identifier
    And I wait
    And screenshot verified {'portfolio_Success_orders'}

 @testMethodName: testGoldens
  Scenario: Empty order tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioPositionSuccessScenario, portfolioOrdersEmptyScenario]}
    And I tap {'orders_tab'} identifier
    Then i Wait for {1} seconds
    And screenshot verified {'portfolio_empty_orders'} 

 @testMethodName: testGoldens
  Scenario: Error order tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioPositionSuccessScenario, portfolioOrdersFailureScenario]}
    And I tap {'orders_tab'} identifier
    Then i Wait for {1} seconds
    And screenshot verified {'portfolio_error_orders'}

 @testMethodName: testGoldens
  Scenario: Success insights tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioInsightsSuccessScenario]}
    And I tap {'margin_tab'} identifier
    Then I wait
    And screenshot verified {'portfolio_insights_success'}
 
 @testMethodName: testGoldens
  Scenario: Failure insights tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioInsightsFailureScenario]}
    And I tap {'margin_tab'} identifier
    Then I wait
    And screenshot verified {'portfolio_insights_failure'}

 @testMethodName: testGoldens
  Scenario: Empty insights tab 
    Given The {PortfolioPositionScreen()} app is rendered {scenarios: [portfolioInsightsEmptyScenario]}
    And I tap {'margin_tab'} identifier
    Then I wait
    And screenshot verified {'portfolio_insights_empty'}
