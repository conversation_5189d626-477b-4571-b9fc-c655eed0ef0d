import 'dart:async';

import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:e_trader/src/data/api/trading_account_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/account_balance_hub_response.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/model/account_view_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'accounts_bloc.freezed.dart';
part 'accounts_event.dart';
part 'accounts_state.dart';

class AccountsBloc extends Bloc<AccountsEvent, AccountsState>
    with DisposableMixin {
  AccountsBloc(
    this._getTradingAccountsUseCase,
    this._saveSelectedAccountUseCase,
    this._getSelectedAccountUseCase,
    this._subscribeToTradingAccountBalanceUseCase,
    this._updateTradingAccountBalanceHubUseCase,
    this._equitiTraderNavigation,
    this._saveTradingPreferencesUseCase,
  ) : super(_AccountsState()) {
    on<AccountsEvent>(
      (event, emit) => switch (event) {
        _FetchAccounts() => _fetchAccounts(emit),
        _OnAccountSelected val => _onAccountSelected(val, emit),
        _GoToSymbols() => _goToSymbols(),
        _GoToDepositPaymentOptions() => _gotToDepositPaymentOptions(),
        _GoToWithdrawPaymentOptions() => _goToWithdrawPaymentOptions(),
        _GoToTransferOptions() => _goToTransferOptions(),
        _RefreshAccounts() => _refreshAccounts(emit),
      },
    );
  }
  final SaveTradingPreferencesUseCase _saveTradingPreferencesUseCase;
  final GetTradingAccountsUseCase _getTradingAccountsUseCase;
  final SaveSelectedAccountUseCase _saveSelectedAccountUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final SubscribeToTradingAccountBalanceUseCase
  _subscribeToTradingAccountBalanceUseCase;
  final UpdateTradingAccountBalanceHubUseCase
  _updateTradingAccountBalanceHubUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;
  final Map<String, double> _accountEquities = {};
  final Map<String, double> _accountProfits = {};
  final Map<String, double> _accountCredits = {};
  final Map<String, double> _accountBalances = {};

  /// Intelligently merges existing account view models with new ones.
  /// This preserves existing accounts and their state while adding new accounts
  /// and updating properties of existing accounts.
  /// Also subscribes to balance updates for newly added trading accounts.
  Future<List<AccountViewModel>> _mergeAccountViewModels(
    List<AccountViewModel> existingAccounts,
    List<AccountViewModel> newAccounts, {
    bool shouldSubscribeToNewAccounts = false,
  }) async {
    final Map<String, AccountViewModel> existingAccountsMap = {
      for (final account in existingAccounts) account.accountId: account,
    };

    final List<AccountViewModel> mergedAccounts = [];
    final List<String> newTradingAccountNumbers = [];

    // Process new accounts
    for (final newAccount in newAccounts) {
      final existingAccount = existingAccountsMap[newAccount.accountId];

      if (existingAccount != null) {
        // Update existing account with new data while preserving important state
        mergedAccounts.add(
          newAccount.copyWith(
            // Preserve selection state if it was previously selected
            isSelected: existingAccount.isSelected || newAccount.isSelected,
          ),
        );
        // Remove from map to track which accounts were processed
        existingAccountsMap.remove(newAccount.accountId);
      } else {
        // Add new account
        mergedAccounts.add(newAccount);

        // Track new trading accounts for subscription
        if (shouldSubscribeToNewAccounts &&
            newAccount.platformType != PlatformType.equiti &&
            newAccount.platformType != PlatformType.unknown &&
            newAccount.accountNumber != null) {
          newTradingAccountNumbers.add(newAccount.accountNumber!);
        }
      }
    }

    // Add any remaining existing accounts that weren't in the new list
    // This handles the case where an account might temporarily not be returned
    // but we want to keep it in the UI until we're sure it's been removed
    mergedAccounts.addAll(existingAccountsMap.values);

    // Subscribe to balance updates for new trading accounts
    if (newTradingAccountNumbers.isNotEmpty) {
      try {
        await _updateTradingAccountBalanceHubUseCase(
          eventType: TradingSocketEvent.accountBalance.subscribe,
          accountNumbers: newTradingAccountNumbers,
        );
      } catch (error) {
        // Log error but don't fail the merge operation
        addError(error);
      }
    }

    return mergedAccounts;
  }

  FutureOr<void> _fetchAccounts(Emitter<AccountsState> emit) async {
    final selectedAccount = _getSelectedAccountUseCase();
    final result =
        await TaskEither<
          Exception,
          (List<TradingAccountModel>, Stream<AccountBalanceHubResponse?>)
        >.Do(($) async {
          final accounts = await $(_getTradingAccountsUseCase());
          final accountBalanceStream = await $(
            _subscribeToTradingAccountBalanceUseCase(
              accounts
                  .where(
                    (account) =>
                        account.platformType != PlatformType.equiti &&
                        account.platformType != PlatformType.unknown,
                  )
                  .map((account) => account.accountNumber)
                  .toList(),
              subscriberId: '${AccountsBloc}_$hashCode',
              eventType: TradingSocketEvent.accountBalance.subscribe,
            ),
          );

          return (accounts, accountBalanceStream);
        }).run();
    await result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(processState: AccountsProcessState.error()));
      },
      (value) async {
        final accounts = value.$1;
        final accountBalanceStream = value.$2;

        // Create new wallet view models from fresh data
        final newWallets =
            accounts
                .where((account) => account.platformType == PlatformType.equiti)
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        // Merge wallets intelligently with existing ones
        final wallets = await _mergeAccountViewModels(
          state.wallets,
          newWallets,
        );

        // Create new trading account view models from fresh data
        final newTradingAccounts =
            accounts
                .where(
                  (account) =>
                      account.platformType != PlatformType.equiti &&
                      account.platformType != PlatformType.unknown,
                )
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        // Merge trading accounts intelligently with existing ones and subscribe to new ones
        final tradingAccounts = await _mergeAccountViewModels(
          state.tradingAccounts,
          newTradingAccounts,
          shouldSubscribeToNewAccounts: true,
        );

        emit(
          state.copyWith(
            processState: AccountsProcessState.success(),
            accounts: accounts,
            tradingAccounts: tradingAccounts,
            wallets: wallets,
            totalEquity: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.equityAlternateCurrency ?? 0.0),
            ),
            totalProfit: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.profitAlternateCurrency ?? 0.0),
            ),
            totalCredit: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.creditAlternateCurrency ?? 0.0),
            ),
            totalBalance: accounts.fold<double>(
              0,
              (sum, account) => sum + (account.balanceAlternateCurrency ?? 0.0),
            ),
          ),
        );

        if (tradingAccounts.isEmpty) {
          return;
        }

        await emit.forEach(
          accountBalanceStream.distinct(),
          onData: (accountBalanceResponse) {
            final updatedTradingAccounts =
                state.tradingAccounts
                    .map(
                      (accountModel) => _updateAccountBalance(
                        accountModel,
                        accountBalanceResponse,
                      ),
                    )
                    .toList();

            if (accountBalanceResponse != null) {
              final accountNumber =
                  accountBalanceResponse.account.accountNumber;
              _accountEquities[accountNumber!] =
                  accountBalanceResponse.account.equityAlternateCurrency;
              _accountProfits[accountNumber] =
                  accountBalanceResponse.account.profitAlternateCurrency;
              _accountCredits[accountNumber] =
                  accountBalanceResponse.account.creditAlternateCurrency;
              _accountBalances[accountNumber] =
                  accountBalanceResponse.account.balanceAlternateCurrency;
            }

            final updatedTotalEquity = _accountEquities.values.fold<double>(
              0,
              (sum, equity) => sum + equity,
            );
            final updatedTotalProfit = _accountProfits.values.fold<double>(
              0,
              (sum, profit) => sum + profit,
            );
            final updatedTotalCredit = _accountCredits.values.fold<double>(
              0,
              (sum, credit) => sum + credit,
            );
            final updatedTotalBalance = _accountBalances.values.fold<double>(
              0,
              (sum, balance) => sum + balance,
            );

            return state.copyWith(
              tradingAccounts: updatedTradingAccounts,
              totalEquity: updatedTotalEquity,
              totalProfit: updatedTotalProfit,
              totalCredit: updatedTotalCredit,
              totalBalance: updatedTotalBalance,
              processState: AccountsProcessState.success(),
            );
          },
          onError: (error, stackTrace) {
            return state.copyWith(processState: AccountsProcessState.error());
          },
        );
      },
    );
  }

  AccountViewModel _updateAccountBalance(
    AccountViewModel accountModel,
    AccountBalanceHubResponse? accountBalanceResponse,
  ) => accountModel.copyWith(
    balance:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.balance
            : accountModel.balance,
    equity:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.equity
            : accountModel.equity,
    margin:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.margin
            : accountModel.margin,
    profit:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.profit
            : accountModel.profit,
    credit:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.credit
            : accountModel.credit,
    marginLevel:
        accountBalanceResponse?.account.accountNumber ==
                accountModel.accountNumber
            ? accountBalanceResponse?.account.marginLevel
            : accountModel.marginLevel,
  );

  FutureOr<void> _onAccountSelected(
    _OnAccountSelected val,
    Emitter<AccountsState> emit,
  ) {
    final accountId = val.accountId;

    final updatedTradingAccounts =
        state.tradingAccounts
            .map(
              (tradingAccount) => tradingAccount.copyWith(
                isSelected: tradingAccount.accountId == accountId,
              ),
            )
            .toList();

    final selectedAccount = state.accounts.firstOrNullWhere(
      (account) => account.recordId == accountId,
    );

    if (selectedAccount != null) {
      _saveSelectedAccountUseCase(selectedAccount);
      // todo (Abed): Uncomment post firebase notification fix for different envs
      // await _pushNotificationTokenUseCase(
      //   accountNumber: selectedAccount.accountNumber,
      // ).run();
      _saveTradingPreferencesUseCase.saveLeverage(
        selectedAccount.leverage!.toString(),
      );
    }

    emit(state.copyWith(tradingAccounts: updatedTradingAccounts));

    add(AccountsEvent.goToSymbols());
  }

  Future<void> _goToSymbols() async {
    await _updateTradingAccountBalanceHubUseCase(
      eventType: TradingSocketEvent.accountBalance.unsubscribe,
      accountNumbers:
          state.tradingAccounts
              .map((account) => account.accountNumber!)
              .toList(),
    );
    _equitiTraderNavigation.navigateToSymbols();
  }

  void _gotToDepositPaymentOptions() {
    _equitiTraderNavigation.navigateToDepositOptions(
      depositFlowConfig: DepositFlowConfig(
        origin: EquitiTraderRouteSchema.switchAccountRoute.url,
        depositType: DepositType.additional,
      ),
    );
  }

  void _goToWithdrawPaymentOptions() {
    _equitiTraderNavigation.navigateToWithdrawOptions();
  }

  void _goToTransferOptions() {
    _equitiTraderNavigation.goToTransferFundsScreen();
  }

  FutureOr<void> _refreshAccounts(Emitter<AccountsState> emit) async {
    emit(state.copyWith(processState: AccountsProcessState.loading()));
    final result = await _getTradingAccountsUseCase().run();
    await result.fold(
      (error) {
        addError(error);
      },
      (accounts) async {
        final selectedAccount = _getSelectedAccountUseCase();

        // Create new wallet view models from fresh data
        final newWallets =
            accounts
                .where((account) => account.platformType == PlatformType.equiti)
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        // Merge wallets intelligently with existing ones
        final wallets = await _mergeAccountViewModels(
          state.wallets,
          newWallets,
        );

        // Create new trading account view models from fresh data
        final newTradingAccounts =
            accounts
                .where(
                  (account) =>
                      account.platformType != PlatformType.equiti &&
                      account.platformType != PlatformType.unknown,
                )
                .map(
                  (accountModel) => AccountViewModel(
                    name: accountModel.name,
                    nickName: accountModel.nickName,
                    homeCurrency: accountModel.homeCurrency,
                    accountId: accountModel.recordId,
                    accountNumber: accountModel.accountNumber,
                    balance: accountModel.balance,
                    credit: accountModel.credit,
                    equity: accountModel.equity,
                    margin: accountModel.margin,
                    platformType: accountModel.platformType,
                    isSelected:
                        selectedAccount?.recordId == accountModel.recordId,
                    platformAccountType: accountModel.platformAccountType,
                    profit: accountModel.profit,
                    marginLevel: accountModel.marginLevel,
                  ),
                )
                .toList();

        // Merge trading accounts intelligently with existing ones and subscribe to new ones
        final tradingAccounts = await _mergeAccountViewModels(
          state.tradingAccounts,
          newTradingAccounts,
          shouldSubscribeToNewAccounts: true,
        );

        emit(
          state.copyWith(
            wallets: wallets,
            tradingAccounts: tradingAccounts,
            accounts: accounts,
            processState: AccountsProcessState.success(),
          ),
        );
      },
    );
  }
}
