# azure-pipelines/azure-pipelines-build-and-deploy-ios-equiti-platform.yaml
name: Build iOS Pipeline
trigger: none

parameters:
- name: environment
  displayName: 'Environment'
  type: string
  default: 'development'
  values:
  - development
  - release
  # - staging
  # - production
- name: deploy
  displayName: 'Deploy to TestFlight'
  type: boolean
  default: true

variables:
- group: MatchSecrets
- group: AppStoreSecrets
# Environment to app flavor mapping
- name: AppFlavor
  ${{ if eq(parameters.environment, 'development') }}:
    value: 'dev'
  ${{ if eq(parameters.environment, 'staging') }}:
    value: 'stg'
  ${{ if eq(parameters.environment, 'release') }}:
    value: 'rel'
  ${{ if eq(parameters.environment, 'production') }}:
    value: 'prod'

stages:
- stage: 'Build_IOS'
  displayName: 'Build IOS'
  jobs:
  - job: Build_IOS_Job
    pool: 
      vmImage: 'macOS-15'
    timeoutInMinutes: 90
    steps:
    - checkout: self
      fetchDepth: 0

    - script: |
        xcodebuild -version
      displayName: 'Print Xcode Version'

    - template: ../templates/install-flutter.yaml
      parameters:
        channel: 'stable'
        customVersion: '3.32.6'
    
    - script: dart pub global run melos bs
      displayName: '<PERSON><PERSON> Bootstrap'

    - template: ../templates/setup-ios-container.yaml

    - script: |
        cd app/equiti_platform/ios
        bundle exec fastlane setup_signing
      displayName: "Setup Signing"
      env:
        MATCH_PASSWORD: $(MATCH_PASSWORD)
        MATCH_GIT_BEARER_TOKEN: $(MATCH_GIT_BEARER_TOKEN)
        APP_ENVIRONMENT: ${{ parameters.environment }}

    - script: |
        cd app/equiti_platform
        flutter build ios --flavor $(AppFlavor) -t lib/main_$(AppFlavor).dart --release --dart-define-from-file=../../.env/config.$(AppFlavor).json --build-number=$(Build.BuildId) --no-codesign --verbose
      displayName: 'Build iOS App'

    - template: ../templates/download-azure-secure-file.yaml
      parameters:
        secureFileName: 'app_store_connect_api_key.p8'
        destinationPath: 'app/equiti_platform/ios/fastlane/api_key.p8'

    - script: |
        cd app/equiti_platform/ios
        bundle exec fastlane build
      displayName: 'Build iOS ipa'
      env:
        APP_FLAVOR: $(AppFlavor)

    - task: PublishBuildArtifacts@1
      condition: succeeded()
      inputs:
        PathToPublish: 'app/equiti_platform/build/ios/ipa/Equiti Platform $(AppFlavor).ipa'
        ArtifactName: 'Flutter_iOS_IPA_Artifact'
      displayName: 'Publish iOS IPA Artifact'
    
    - script: |
        cd app/equiti_platform/ios
        bundle exec fastlane deploy_testflight
      displayName: 'Deploy iOS app to testflight'
      condition: eq(${{ parameters.deploy }}, true)
      env:
        APPSTORE_CONNECT_KEY_ID: $(APPSTORE_CONNECT_KEY_ID)
        APPSTORE_CONNECT_ISSUER_ID: $(APPSTORE_CONNECT_ISSUER_ID)
        APP_FLAVOR: $(AppFlavor)
