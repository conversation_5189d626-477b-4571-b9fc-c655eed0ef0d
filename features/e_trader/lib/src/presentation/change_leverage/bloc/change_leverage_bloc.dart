import 'dart:async';

import 'package:e_trader/src/domain/usecase/change_leverage_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_leverage_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_trading_preferences_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'change_leverage_bloc.freezed.dart';
part 'change_leverage_event.dart';
part 'change_leverage_state.dart';

class ChangeLeverageBloc extends Bloc<ChangeLeverageEvent, ChangeLeverageState>
    with DisposableMixin {
  final GetLeverageUseCase _getLeverageUseCase;
  final ChangeLeverageUseCase _changeLeverageUseCase;
  final GetTradingPreferencesUseCase _getTradingPreferencesUseCase;
  final SaveTradingPreferencesUseCase _saveTradingPreferencesUseCase;
  final LoggerBase _logger;

  ChangeLeverageBloc({
    required LoggerBase logger,
    required GetLeverageUseCase getLeverageUseCase,
    required ChangeLeverageUseCase changeLeverageUseCase,
    required GetTradingPreferencesUseCase getTradingPreferencesUseCase,
    required SaveTradingPreferencesUseCase saveTradingPreferencesUseCase,
    int? leverage,
  }) : _logger = logger,
       _getLeverageUseCase = getLeverageUseCase,
       _changeLeverageUseCase = changeLeverageUseCase,
       _getTradingPreferencesUseCase = getTradingPreferencesUseCase,
       _saveTradingPreferencesUseCase = saveTradingPreferencesUseCase,
       super(ChangeLeverageState(selectedLeverage: leverage)) {
    on<_OnGetLeverages>(_onGetLeverages);
    on<_OnChangeLeverages>(_onChangeLeverages);
  }

  FutureOr<void> _onGetLeverages(
    _OnGetLeverages event,
    Emitter<ChangeLeverageState> emit,
  ) async {
    final result =
        await _getLeverageUseCase(accountNumber: event.accountNumber).run();
    final savedLeverageString =
        await _getTradingPreferencesUseCase.getLeverage();
    result.fold(
      (exception) {
        if (!isClosed)
          emit(
            state.copyWith(
              processState: ChangeLeverageProcessState.error(message: "error"),
            ),
          );
        addError(exception);
      },
      (leverages) {
        if (!isClosed)
          emit(
            state.copyWith(
              processState: ChangeLeverageProcessState.success(),
              leverages: leverages,
              selectedLeverage:
                  int.tryParse(savedLeverageString.toString()) ??
                  state.selectedLeverage,
            ),
          );
      },
    );
  }

  FutureOr<void> _onChangeLeverages(
    _OnChangeLeverages event,
    Emitter<ChangeLeverageState> emit,
  ) async {
    emit(state.copyWith(changeLeverage: ChangeLeverageProcessState.loading()));

    final result =
        await _changeLeverageUseCase(
          newLeverage: event.selectedLeverage.toString(),
          accountNumber: event.accountNumber,
        ).run();

    result.fold(
      (exception) {
        if (!isClosed)
          emit(
            state.copyWith(
              changeLeverage: ChangeLeverageProcessState.error(
                message: "error",
              ),
              processState: ChangeLeverageProcessState.success(),
            ),
          );
        addError(exception);
      },
      (leverages) {
        if (leverages.isSuccess) {
          _saveTradingPreferencesUseCase.saveLeverage(
            event.selectedLeverage.toString(),
          );
          if (!isClosed)
            emit(
              state.copyWith(
                changeLeverage: ChangeLeverageProcessState.success(),
                processState: ChangeLeverageProcessState.success(),
                selectedLeverage: int.tryParse(
                  event.selectedLeverage.toString(),
                ),
              ),
            );
        } else {
          if (!isClosed)
            emit(
              state.copyWith(
                changeLeverage: ChangeLeverageProcessState.error(
                  message: leverages.message,
                ),
                processState: ChangeLeverageProcessState.error(
                  message: leverages.message,
                ),
                selectedLeverage: int.tryParse(
                  event.selectedLeverage.toString(),
                ),
              ),
            );
        }
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }
}
