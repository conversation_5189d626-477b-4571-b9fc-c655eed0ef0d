import 'package:duplo/src/UI/controls/highlight_option_box_widget.dart';
import 'package:duplo/src/UI/controls/stepper_control_widget.dart';
import 'package:duplo/src/UI/controls/stepper_number_input_with_keyboard_widget.dart';
import 'package:duplo/src/UI/models/key_value_pair.dart';

import 'package:duplo/src/UI/text_display/order_limit_footer_widget.dart';
import 'package:flutter/material.dart';

Widget _stepperControlWithOptions(
  List<String> options,
  int selectedIndex,
  String title,
  bool addFooter, [
  bool addBorder = true,
]) {
  Widget? segmentControlWidget;
  if (options.isNotEmpty) {
    segmentControlWidget = HighlightOptionBoxWidget(
      selectedIndex: selectedIndex,
      options: options,
      onSelectionChange: (value, index) {
        print(index);
      },
    );
  }

  final inputWidget = StepperNumberInputWithKeyboardWidget(
    prescisionFactor: 2,
    hintText: 'Enter a number',
    onValueChange: (value) {
      print(value);
    },
    changeFactor: 0.1,
    inputControl: null,
    inputEditingController: TextEditingController(),
    digits: 5,
  );

  Widget? footer;
  if (addFooter) {
    footer = OrderLimitFooterWidget(
      firstPair: KeyValuePair(label: "Price", value: "32"),
      firstColor: Colors.red,
      secondColor: Colors.green,
    );
  }

  final stepper = StepperControlWidget(
    title: title,
    segmentControWidget: segmentControlWidget,
    inputWidget: inputWidget,
    footerWidget: footer,
    bordered: addBorder,
  );
  return Center(child: stepper);
}

final normalState = _stepperControlWithOptions(
  ["-25", "-15", "-5", "5", "15", "25"],
  1,
  "Normal State",
  true,
);
final withoutBorder = _stepperControlWithOptions(
  ["-25", "-15", "-5", "5", "15", "25"],
  1,
  "Without Border",
  true,
  false,
);
final withoutFooter = _stepperControlWithOptions(
  ["-25", "-15", "-5", "5", "15", "25"],
  1,
  "Without Footer",
  false,
);
