import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:duplo/src/components/locale_aware_assets_extension.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class TextChevronWidget extends StatelessWidget {
  const TextChevronWidget({
    super.key,
    required this.title,
    this.titleStyle,
    required this.onPressed,
    this.backgroundColor,
    this.semanticsIdentifier,
    this.trailingText,
  });
  final String title;
  final DuploTextStyle? titleStyle;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final String? semanticsIdentifier;
  final String? trailingText;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    return Semantics(
      identifier: semanticsIdentifier ?? "text_chevron_widget",
      child: DuploTap(
        onTap: onPressed,
        child: Container(
          color: backgroundColor ?? Colors.transparent,
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Expanded(
                child: DuploText(
                  text: title,
                  style: titleStyle ?? duploTextStyles.textMd,
                  color: theme.text.textSecondary,
                  fontWeight: DuploFontWeight.medium,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (trailingText != null) ...[
                const SizedBox(width: 8.0),
                DuploText(
                  text: trailingText!,
                  style: duploTextStyles.textXs,
                  color: theme.text.textQuaternary,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(width: 8.0),
              Assets.images.chevronRightDirectional(context).svg(),
            ],
          ),
        ),
      ),
    );
  }
}
