import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:payment/src/domain/repository/deposit_repository.dart';
import 'package:payment/src/domain/repository/list_of_accounts_repository.dart';
import 'package:payment/src/domain/repository/payment_options_repository.dart';
import 'package:payment/src/domain/repository/deposit_status_repository.dart';
import 'package:payment/src/domain/repository/transfer_repository.dart';
import 'package:payment/src/domain/repository/withdraw_repository.dart';
import 'package:payment/src/domain/usecase/check_withdrawal_allowed_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/domain/usecase/conversion_rate_usecase.dart';
import 'package:payment/src/domain/usecase/delete_bank_account_use_case.dart';
import 'package:payment/src/domain/usecase/deposit_status_usecase.dart';
import 'package:payment/src/domain/usecase/get_bank_accounts_use_case.dart';
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart';
import 'package:payment/src/domain/usecase/get_transfer_type_usecase.dart';
import 'package:payment/src/domain/usecase/get_withdraw_card_usecase.dart';
import 'package:payment/src/domain/usecase/get_withdrawal_fees_usecase.dart';
import 'package:payment/src/domain/usecase/list_of_account_use_case.dart';
import 'package:payment/src/domain/usecase/payment_options_deposit_usecase.dart';
import 'package:payment/src/domain/usecase/payment_options_withdraw_usecase.dart';
import 'package:payment/src/domain/usecase/submit_transfer_use_case.dart';
import 'package:payment/src/domain/usecase/upload_document_usecase.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';
import 'package:payment/src/presentation/deposit_payment_methods/bloc/deposit_payment_options_bloc.dart';

import 'package:payment/src/presentation/payment_status/bloc/deposit_status_bloc.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/bloc/transfer_funds_dest_selection_bloc.dart';
import 'package:payment/src/presentation/transfer_funds/bloc/transfer_funds_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/bloc/amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/bloc/withdraw_fees_bloc.dart';
import 'package:payment/src/presentation/withdraw_accounts_and_amount/bloc/withdraw_accounts_and_amount_bloc.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/bloc/withdraw_add_new_bank_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/bloc/transfer_type_bloc.dart';
import 'package:payment/src/presentation/withdraw_new_bank_upload_doc/bloc/withdraw_new_bank_upload_doc_bloc.dart';
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/bloc/withdraw_card_bloc.dart';
import 'package:payment/src/presentation/withdraw_payment_methods/bloc/withdraw_payment_methods_bloc.dart';
import 'package:payment/src/presentation/withdraw_skrill_and_neteller/bloc/withdraw_skrill_and_neteller_bloc.dart';
import 'package:payment/src/utils/payment/apple_pay_utils.dart';
import 'package:payment/src/utils/payment/google_pay_utils.dart';
import 'package:user_account/user_account.dart';

import '../presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import '../presentation/widgets/account_list_widget/bloc/account_list_bloc.dart';

@module
abstract class PaymentModule {
  @injectable
  GooglePayUtils googlePayUtils() => GooglePayUtils();

  @injectable
  ApplePayUtils applePayUtils() => ApplePayUtils();

  @injectable
  PaymentOptionsRepository paymentOptionsRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => PaymentOptionsRepository(apiClient: apiClient);

  @injectable
  PaymentOptionsDepositUsecase paymentOptionsUsecase(
    PaymentOptionsRepository repository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => PaymentOptionsDepositUsecase(
    repository: repository,
    getBrokerIdUseCase: getBrokerIdUseCase,
  );

  @injectable
  DepositPaymentOptionsBloc depositPaymentOptionsBloc(
    PaymentOptionsDepositUsecase paymentOptionsUsecase,
    PaymentNavigation paymentNavigation,
    ClientProfileUseCase clientProfileUseCase,
  ) => DepositPaymentOptionsBloc(
    paymentOptionsUsecase: paymentOptionsUsecase,
    paymentNavigation: paymentNavigation,
    clientProfileUseCase: clientProfileUseCase,
  );

  @injectable
  ConversionRateUsecase conversionRateUsecase(
    PaymentOptionsRepository repository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => ConversionRateUsecase(
    repository: repository,
    getBrokerIdUseCase: getBrokerIdUseCase,
  );

  @injectable
  AmountConversionBloc amountConversionBloc(
    ConversionRateUsecase conversionRateUsecase,
  ) => AmountConversionBloc(conversionRateUsecase: conversionRateUsecase);
  @injectable
  ListOfAccountsRepository listOfAccountsRepository(ApiClientBase apiClient) =>
      ListOfAccountsRepository(apiClient: apiClient);

  @injectable
  ListOfAccountUseCase listOfAccountUseCase(
    ListOfAccountsRepository repository,
  ) => ListOfAccountUseCase(repository: repository);

  @injectable
  AccountListBloc accountListBloc(
    ListOfAccountUseCase listOfAccountUseCase,
    PaymentNavigation paymentNavigation,
  ) => AccountListBloc(
    listOfAccountUseCase: listOfAccountUseCase,
    paymentNavigation: paymentNavigation,
  );

  @injectable
  DepositRepository depositRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => DepositRepository(apiClient: apiClient);

  @injectable
  GetDepositDetailsUsecase getDepositDetailsUsecase(
    DepositRepository repository,
  ) => GetDepositDetailsUsecase(repository: repository);

  @injectable
  DepositAccountsAndAmountBloc paymentsAccountsAndAmountBloc(
    GetDepositDetailsUsecase getDepositDetailsUsecase,
    PaymentNavigation paymentNavigation,
    GooglePayUtils googlePayUtils,
    ApplePayUtils applePayUtils,
  ) => DepositAccountsAndAmountBloc(
    getDepositDetailsUsecase: getDepositDetailsUsecase,
    paymentNavigation: paymentNavigation,
    googlePayUtils: googlePayUtils,
    applePayUtils: applePayUtils,
  );

  @injectable
  DepositStatusRepository paymentStatusRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => DepositStatusRepository(apiClient: apiClient);

  @injectable
  DepositStatusUsecase paymentStatusUsecase(
    DepositStatusRepository repository,
  ) => DepositStatusUsecase(repository: repository);

  @injectable
  DepositStatusBloc paymentStatusBloc(
    DepositStatusUsecase depositStatusUsecase,
  ) => DepositStatusBloc(depositStatusUsecase: depositStatusUsecase);

  @injectable
  WithdrawPaymentMethodsBloc withdrawPaymentMethodsBloc(
    PaymentOptionsWithdrawUsecase paymentOptionsUsecase,
    PaymentNavigation paymentNavigation,
    ClientProfileUseCase clientProfileUseCase,
  ) => WithdrawPaymentMethodsBloc(
    paymentOptionsUsecase,
    paymentNavigation,
    clientProfileUseCase,
  );

  @injectable
  PaymentOptionsWithdrawUsecase paymentOptionsWithdrawUsecase(
    PaymentOptionsRepository repository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => PaymentOptionsWithdrawUsecase(
    repository: repository,
    getBrokerIdUseCase: getBrokerIdUseCase,
  );

  @injectable
  GetWithdrawalFeesUsecase getWithdrawalFeesUsecase(
    PaymentOptionsRepository repository,
    ClientProfileUseCase clientProfileUseCase,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => GetWithdrawalFeesUsecase(
    repository: repository,
    clientProfileUseCase: clientProfileUseCase,
    getBrokerIdUseCase: getBrokerIdUseCase,
  );

  @injectable
  WithdrawAccountsAndAmountBloc withdrawAccountsAndAmountBloc(
    PaymentNavigation paymentNavigation,
    WithdrawalUseCase withdrawalUseCase,
    GetWithdrawalFeesUsecase getWithdrawalFeesUsecase,
  ) => WithdrawAccountsAndAmountBloc(
    paymentNavigation,
    withdrawalUseCase,
    getWithdrawalFeesUsecase,
  );

  @injectable
  WithdrawCardBloc withdrawCardBloc(
    GetWithdrawCardUseCase getWithdrawCardUseCase,
    PaymentNavigation paymentNavigation,
  ) => WithdrawCardBloc(
    getWithdrawCardUseCase: getWithdrawCardUseCase,
    paymentNavigation: paymentNavigation,
  );

  @injectable
  GetWithdrawCardUseCase getWithdrawCardUseCase(
    WithdrawRepository withdrawRepository,
    ClientProfileUseCase clientProfileUseCase,
  ) => GetWithdrawCardUseCase(withdrawRepository, clientProfileUseCase);

  @injectable
  WithdrawalUseCase withdrawUseCase(
    WithdrawRepository withdrawRepository,
    ClientProfileUseCase clientProfileUseCase,
  ) => WithdrawalUseCase(withdrawRepository, clientProfileUseCase);

  @injectable
  WithdrawRepository withdrawRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
    AuthService authService,
  ) => WithdrawRepository(apiClient: apiClient, authService: authService);

  @injectable
  GetTransferTypeUseCase getTransferTypeUseCase(
    WithdrawRepository withdrawRepository,
  ) => GetTransferTypeUseCase(withdrawRepository);

  @injectable
  WithdrawBankTransferBloc withdrawBankTransferBloc(
    GetBankAccountsUseCase getBankAccountsUseCase,
    PaymentNavigation paymentNavigation,
    DeleteBankAccountUseCase deleteBankAccountUseCase,
  ) => WithdrawBankTransferBloc(
    getBankAccountsUseCase,
    paymentNavigation,
    deleteBankAccountUseCase,
  );

  @injectable
  GetBankAccountsUseCase getBankAccountsUseCase(
    WithdrawRepository withdrawRepository,
    ClientProfileUseCase clientProfileUseCase,
  ) => GetBankAccountsUseCase(
    withdrawRepository: withdrawRepository,
    clientProfileUseCase: clientProfileUseCase,
  );
  CheckWithdrawalAllowedUsecase checkWithdrawalAllowedUsecase(
    WithdrawRepository repository,
    ClientProfileUseCase clientProfileUseCase,
  ) => CheckWithdrawalAllowedUsecase(
    repository: repository,
    clientProfileUseCase: clientProfileUseCase,
  );

  @injectable
  WithdrawSkrillAndNetellerBloc withdrawSkrillAndNetellerBloc(
    CheckWithdrawalAllowedUsecase checkWithdrawalAllowedUsecase,
  ) => WithdrawSkrillAndNetellerBloc(checkWithdrawalAllowedUsecase);

  @injectable
  WithdrawFeesBloc withdrawFeesBloc(
    GetWithdrawalFeesUsecase getWithdrawalFeesUsecase,
  ) => WithdrawFeesBloc(getWithdrawalFeesUsecase: getWithdrawalFeesUsecase);

  @injectable
  TransferTypeBloc transferTypeBloc(
    WithdrawalUseCase withdrawalUseCase,
    ClientProfileUseCase clientProfileUseCase,
  ) => TransferTypeBloc(withdrawalUseCase, clientProfileUseCase);

  @injectable
  DeleteBankAccountUseCase deleteBankAccountUseCase(
    WithdrawRepository withdrawRepository,
  ) => DeleteBankAccountUseCase(withdrawRepository);
  WithdrawAddNewBankBloc withdrawAddNewBankBloc(
    GetCountryUseCase getCountryUseCase,
    GetTransferTypeUseCase getTransferTypeUseCase,
    PaymentNavigation paymentNavigation,
    WithdrawalUseCase withdrawalUseCase,
  ) => WithdrawAddNewBankBloc(
    getCountryUseCase: getCountryUseCase,
    getTransferTypeUseCase: getTransferTypeUseCase,
    paymentNavigation: paymentNavigation,
    withdrawalUseCase: withdrawalUseCase,
  );

  @injectable
  UploadDocumentUseCase uploadDocumentUseCase(
    WithdrawRepository withdrawRepository,
  ) => UploadDocumentUseCase(withdrawRepository);

  @injectable
  WithdrawNewBankUploadDocBloc withdrawNewBankUploadDocBloc({
    required UploadDocumentUseCase uploadDocumentUseCase,
  }) => WithdrawNewBankUploadDocBloc(
    uploadDocumentUseCase: uploadDocumentUseCase,
  );
  @injectable
  TransferFundsBloc transferFundsBloc(PaymentNavigation paymentNavigation) =>
      TransferFundsBloc(paymentNavigation);

  @injectable
  TransferRepository transferFundsRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => TransferRepository(apiClient);

  @injectable
  SubmitTransferUseCase submitTransferUseCase(TransferRepository repository) =>
      SubmitTransferUseCase(repository);

  @injectable
  TransferFundsDestSelectionBloc transferFundsDestSelectionBloc(
    SubmitTransferUseCase submitTransferUseCase,
  ) => TransferFundsDestSelectionBloc(
    submitTransferUseCase: submitTransferUseCase,
  );
}
