import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';

part 'withdraw_flow_params.freezed.dart';
part 'withdraw_flow_params.g.dart';

@freezed
sealed class WithdrawFlowParams with _$WithdrawFlowParams {
  const factory WithdrawFlowParams({
    required String tradingAccountId,
    required WithdrawalMop paymentType,
    required String accountCurrency,
    required String currency,
    required String conversionRateString,
    required num amount,
    required num convertedAmount,
    required num conversionRate,
    required num accountBalance, // Add account balance for validation
  }) = _WithdrawFlowParams;

  factory WithdrawFlowParams.fromJson(Map<String, dynamic> json) =>
      _$WithdrawFlowParamsFromJson(json);
}
