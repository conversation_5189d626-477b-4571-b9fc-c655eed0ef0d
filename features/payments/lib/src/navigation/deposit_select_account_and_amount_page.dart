import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:payment/src/navigation/arguments/payment_method_args.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/deposit_accounts_and_amount_screen.dart';

class DepositSelectAccountAndAmountPage extends EquitiPage {
  const DepositSelectAccountAndAmountPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    // Both equiti_platform and trader now use arguments
    final arguments = routeData.arguments as DepositPaymentMethodArgs;

    return DepositAccountsAndAmountScreen(
      paymentMethod: arguments.method,
      maxPollingAttempts: arguments.maxPollingAttempts,
      pollingFrequencySeconds: arguments.pollingFrequencySeconds,
      depositFlowConfig: arguments.depositFlowConfig,
    );
  }

  @override
  String get label =>
      PaymentRouteSchema.depositSelectAccountAndAmountRoute.label;
  @override
  String get url => PaymentRouteSchema.depositSelectAccountAndAmountRoute.url;
}
