import 'package:duplo/duplo.dart';
import 'package:flutter_test/flutter_test.dart';

/// Usage: I tap duplo {"Partial Close"} button
Future<void> iTapDuploButton(WidgetTester tester, String identifier) async {
  final buttonFinder = find.byWidgetPredicate(
    (widget) =>
        widget is DuploButton && widget.semanticsIdentifier == identifier,
  );

  if (buttonFinder.evaluate().isNotEmpty) {
    await tester.ensureVisible(buttonFinder);
    await tester.pump();
    await tester.tap(buttonFinder);
    await tester.pump();
    // Wait a bit for any animations to complete
    await tester.pump(const Duration(milliseconds: 500));
  } else {
    throw Exception('DuploButton with identifier "$identifier" not found');
  }
}
