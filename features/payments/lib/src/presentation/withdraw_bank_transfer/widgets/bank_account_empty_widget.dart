import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart';

class BankAccountEmptyWidget extends StatelessWidget {
  const BankAccountEmptyWidget({
    super.key,
    required this.bankTransferAmountModel,
  });
  final WithdrawFlowParams bankTransferAmountModel;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            assets.Assets.images.emptyAccountIc.svg(),
            const SizedBox(height: 10),
            DuploText(
              text: EquitiLocalization.of(context).payments_no_accounts_found,
              color: theme.text.textPrimary,
              fontWeight: DuploFontWeight.semiBold,
              style: DuploTextStyles.of(context).textXl,
            ),
            const SizedBox(height: 10),
            DuploText(
              textAlign: TextAlign.center,
              text:
                  EquitiLocalization.of(
                    context,
                  ).payments_no_accounts_found_description,
              color: theme.text.textSecondary,
              fontWeight: DuploFontWeight.regular,
              style: DuploTextStyles.of(context).textSm,
            ),
            const Spacer(),
            BlocBuilder<WithdrawBankTransferBloc, WithdrawBankTransferState>(
              buildWhen: (previous, current) => false,
              builder: (builderContext, state) {
                return SizedBox(
                  width: MediaQuery.sizeOf(context).width * 0.9,
                  child: DuploButton.defaultPrimary(
                    trailingIcon: assets.Assets.images.addIc.keyName,
                    title:
                        EquitiLocalization.of(context).payments_add_new_account,
                    onTap: () {
                      final bloc =
                          builderContext.read<WithdrawBankTransferBloc>();
                      bloc.add(
                        WithdrawBankTransferEvent.onAddNewAccountPressed(
                          bankTransferAmountModel,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
