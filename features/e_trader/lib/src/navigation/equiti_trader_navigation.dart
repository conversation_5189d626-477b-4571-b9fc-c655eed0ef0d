import 'package:domain/domain.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';

abstract class EquitiTraderNavigation {
  void navigateToSymbols();

  void navigateToProductDetail({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
  });

  void navigateToLogin({String? email});
  void navigateToSignUpOptions();
  void navigateToLoginOptions();
  void navigateToTradingPrefrences();
  void navigateToPortfolio({TradeConfirmationResult? result});
  void navigateToHub();
  void navigateToSwitchAccounts();
  void navigateToDepositOptions({required DepositFlowConfig depositFlowConfig});
  void navigateToWithdrawOptions();
  void goToTransferFundsScreen();
  void navigateToPerformance();
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  });
}
