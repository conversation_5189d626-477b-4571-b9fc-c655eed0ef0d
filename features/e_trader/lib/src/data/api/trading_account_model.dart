import 'package:e_trader/src/data/api/account_type.dart';
import 'package:e_trader/src/data/api/platform_account_type.dart';
import 'package:e_trader/src/data/api/platform_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trading_account_model.freezed.dart';
part 'trading_account_model.g.dart';

@freezed
abstract class TradingAccountModel with _$TradingAccountModel {
  const factory TradingAccountModel({
    @J<PERSON><PERSON>ey(name: 'accountId') required String recordId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'platformAccountNumber') required String accountNumber,
    required int accountIdLong,
    String? dateCreated,
    @Default(PlatformAccountType.unknown)
    @<PERSON>sonKey(
      name: 'platformAccountType',
      unknownEnumValue: PlatformAccountType.unknown,
    )
    PlatformAccountType platformAccountType,
    @JsonKey(name: 'accountCurrency') required String homeCurrency,
    int? leverage,
    @<PERSON><PERSON><PERSON>ey(name: 'currentBalance') double? balance,
    double? credit,
    double? equity,
    double? margin,
    @JsonKey(name: 'serverCode') String? server,
    @Default(AccountType.unknown)
    @<PERSON>sonKey(name: 'accountType', unknownEnumValue: AccountType.unknown)
    AccountType accountType,
    String? brokerId,
    String? name,
    @JsonKey(
      name: 'platformType',
      unknownEnumValue: PlatformType.unknown,
      fromJson: _platformTypeFromJson,
    )
    @Default(PlatformType.unknown)
    PlatformType platformType,
    String? clientId,
    @Default(false) bool isDemo,
    double? profit,
    String? accountStatus,
    String? leadSource,
    double? grossProfit,
    double? marginLevel,
    required String primaryEmail,
    String? accountGroup,
    String? classification,
    double? freeMargin,
    @Default(false) bool isSwapFree,
    double? balanceAlternateCurrency,
    double? marginAlternateCurrency,
    double? equityAlternateCurrency,
    double? profitAlternateCurrency,
    double? grossProfitAlternateCurrency,
    double? creditAlternateCurrency,
    String? accountCurrencyUsdPair,
    @Default("") String serverName,
    @Default("") String nickName,
  }) = _TradingAccountModel;

  factory TradingAccountModel.fromJson(Map<String, dynamic> json) =>
      _$TradingAccountModelFromJson(json);
}

// Add this helper function outside the class
PlatformType _platformTypeFromJson(Object? value) {
  if (value == null) return PlatformType.unknown;

  // Try to match the string value directly
  if (value is String) {
    for (final type in PlatformType.values) {
      if (type.name.toLowerCase() == value.toLowerCase()) {
        return type;
      }
    }

    switch (value) {
      case 'Equiti':
        return PlatformType.equiti;
      case 'MT4':
        return PlatformType.mt4;
      case 'MT5':
        return PlatformType.mt5;
      case 'Dulcimer':
        return PlatformType.dulcimer;
      default:
        return PlatformType.unknown;
    }
  }

  return PlatformType.unknown;
}
