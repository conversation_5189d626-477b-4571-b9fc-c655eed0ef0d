part of 'events_bloc.dart';

@freezed
sealed class EventsEvent with _$EventsEvent {
  const factory EventsEvent.fetchEvents({
    @Default('') String query,
    @Default(false) bool loadMore,
  }) = FetchEvents;

  const factory EventsEvent.searchEvents({
    required String query,
    @Default(false) bool loadMore,
  }) = SearchEvents;
  const factory EventsEvent.updateDateLabel({required String dateLabel}) =
      UpdateDateLabel;
  const factory EventsEvent.changeSortOrder(NewsEventsSortOrder sortOrder) =
      _ChangeSortOrder;
  const factory EventsEvent.addEventToCalendar({
    required String title,
    required String subtitle,
    required DateTime date,
    required String eventId,
    required String successMessage,
    required String errorMessage,
    required String permissionDeniedMessage,
    required String noCalendarFoundMessage,
    required String operationFailedMessage,
  }) = _AddEventToCalendar;
}
