import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';

import '../di/di_initializer.dart';

DisplayableComponent paymentOptions() {
  return DisplayableComponent(
    title: 'Deposit Payment Options',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
                  [
                    MockResponse(
                      bodyFilePath:
                          'resources/mocks/client_profile/success.json',
                    ),
                  ],
              '/api/v1/payment/payment-options': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/payment_options/success.json',
                ),
              ],
            });
          return DepositPaymentOptions(
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
    ],
  );
}
