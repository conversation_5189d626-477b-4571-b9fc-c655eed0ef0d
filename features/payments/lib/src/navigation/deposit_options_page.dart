import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/deposit_payment_methods/deposit_payment_options.dart';
import 'package:domain/domain.dart';

class DepositOptionsPage extends EquitiPage {
  const DepositOptionsPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    final depositFlowConfig = routeData.arguments as DepositFlowConfig;
    return DepositPaymentOptions(depositFlowConfig: depositFlowConfig);
  }

  @override
  String get label => PaymentRouteSchema.depositOptionsRoute.label;

  @override
  String get url => PaymentRouteSchema.depositOptionsRoute.url;
}
