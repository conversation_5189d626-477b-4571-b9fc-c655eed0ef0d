import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/widget/set_price_alert_widget.dart';
import 'scenarios/set_price_alert_success_scenario.dart';
import 'scenarios/set_price_alert_failure_scenario.dart';

Feature: Set Alert Golden Tests

  @testMethodName: testGoldens
  Scenario Outline: Set Alert default state 
    Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
    Then I Wait
    Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/all_good_success'    |
  
  @testMethodName: testGoldens
  Scenario Outline: Set Alert when Buy is selected -5%
  Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
  Then I Wait
  And I tap {'button_segment_index_1'} identifier
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/minus_5_percent'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert when Buy is selected +15%
  Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
  Then I Wait
  And I tap {'button_segment_index_3'} identifier
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/plus_15_percent'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert when Sell is selected
  Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
  Then i Wait
  Then i tap {"sell_trade_button"} key
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/sell_selected'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert when Sell is selected and then Buy
  Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
  Then i Wait
  Then i tap {"sell_trade_button"} key
  Then i Wait
  Then i tap {"buy_trade_button"} key
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/sell_selected_then_buy'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert default state and then increment
    Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
    Then i Wait
    Then i tap {"increment_button"} key
    Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/buy_default_increment'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert default state and then decrement
    Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
    Then i Wait
    Then i tap {"decrement_button"} key
    Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/buy_default_decrement'    |

  @testMethodName: testGoldens
  Scenario Outline: Set Alert default state reset percent
    Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
    Then i Wait
    And I tap {'button_segment_index_3'} identifier
    Then i Wait
    Then i tap {"decrement_button"} key
    Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertDefaultSuccesSenario]     | 'set_price_alert/reset_percent_selection'    |

@testMethodName: testGoldens
  Scenario Outline: Set Alert error state
    Given The {SetPriceAlertWidget(symbol: "EURCAD", symbolImageUrl: "", onHideTabbar: null)} app is rendered <scenario>
    Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [setPriceAlertFailureSenario]     | 'set_price_alert/set_price_fails'    |