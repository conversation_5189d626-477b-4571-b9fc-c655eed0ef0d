name: equiti_platform

packages:
  - app/**
  - utilities/**
  - features/**
  - core/**

command:
  bootstrap:
    hooks:
      pre: make -C scripts init_hooks
      post: dart pub global run melos run gen_l10n
    environment:
      sdk: 3.8.1
      flutter: 3.32.6
    dependencies:
      get_it: 8.0.3
      injectable: 2.5.0
      flutter_bloc: 9.1.1
      freezed_annotation: 3.0.0
      json_annotation: 4.9.0
      dio: 5.8.0+1
      intl: 0.20.2
      flutter_svg: 2.0.10+1
      bloc_concurrency: 0.3.0
      flutter_animate: 4.5.2
      flutter_custom_carousel: 0.1.0+1
      flutter_inappwebview: 6.1.5
      clock: 1.1.2

    dev_dependencies:
      build_verify: 3.1.1
      build_runner: 2.5.4
      bdd_widget_test: 1.8.1
      dependency_validator: 5.0.2
      freezed: 3.0.6
      flutter_gen_runner: 5.10.0
      injectable_generator: 2.7.0
      json_serializable: 6.9.5
      mocktail: 1.0.4
      dart_code_metrics_presets: 2.22.0
  clean:
    hooks:
      post: melos exec --flutter -c 1 -- flutter clean

scripts:
  upgrade:
    run: melos exec -c 1 -- flutter pub upgrade
    description: Upgrade all dependencies.
  analyze:
    run: |
      melos exec dcm analyze lib --no-congratulate
    description: Run `flutter analyze` on changed packages.

  update_goldens:
    run: melos exec --dir-exists="test/goldens" -c 1 -- APP_LOCALE=en APP_THEME=light flutter test --update-goldens --tags golden
    description: Re-generate all golden test files

  update_goldens_dark:
    run: melos exec --dir-exists="test/goldens" -c 1 -- APP_LOCALE=ar APP_THEME=dark flutter test --update-goldens --tags golden
    description: Re-generate all golden test files for duplo package
    packageFilters:
      scope:
        - duplo
        - login
        - onboarding
        - hub
        - payment
        - e_trader

  update_goldens_changes:
    run: |
      melos exec --dir-exists="test/goldens" -c 1 --diff=origin/main...HEAD -- "flutter test --no-optimization --update-goldens"
    description: Update goldens for changed packages.
  
  update_goldens_changes_dark:
    run: |
      melos exec --dir-exists="test/goldens" -c 1 --diff=origin/main...HEAD -- "flutter test --no-optimization --update-goldens"
    packageFilters:
      dirExists:
        - duplo
        - login
        - onboarding
        - hub
        - payments
    description: Update goldens for changed packages.
    

  fix_check:
    run: melos exec -- "dart fix --dry-run"
    description: Run `dart fix` checks on all packages.

  test:
    run: |
      melos exec -c 1 --fail-fast -- "very_good test"
    packageFilters:
      dirExists:
        - test
    description: Run tests on all packages.

  test_changes:
    run: |
      melos exec -c 1 --fail-fast --diff=origin/main...HEAD --include-dependents -- "very_good test"
    description: Run tests on changed packages.

  test_unit:
    run: |
      melos exec -c 1 --fail-fast -- "very_good test --no-optimization --exclude-tags golden"
    packageFilters:
      dirExists:
        - test
    description: Run unit tests on all packages.

  test_unit_changes:
    run: |
      melos exec -c 1 --fail-fast --diff=origin/main...HEAD --include-dependents -- "very_good test --exclude-tags golden"
    description: Run unit tests on changed packages.

  test_with_coverage:
    run: melos exec -c 1 --fail-fast -- APP_LOCALE=en APP_THEME=light MELOS_ROOT_PATH/scripts/test_with_coverage.sh
    packageFilters:
      dirExists:
        - test
    description: Run tests with coverage on all packages.

  test_with_coverage_dark:
    run: melos exec -c 1 --fail-fast -- APP_LOCALE=ar APP_THEME=dark MELOS_ROOT_PATH/scripts/test_with_coverage.sh
    packageFilters:
      dirExists:
        - duplo
        - login
        - onboarding
    description: Run tests with coverage on supported packages.

  test_with_coverage_changes:
    run: melos exec -c 1 --diff=origin/main...HEAD --include-dependents --fail-fast MELOS_ROOT_PATH/scripts/test_with_coverage.sh
    description: Run tests with coverage on changed packages.

  format_check_changes:
    run: melos run format_check_lib_changes && melos run format_check_test_changes
    description: Run `dart format` checks on changed packages.

  format_check_lib_changes:
    run: melos exec --diff=origin/main...HEAD -- dcm format lib --line-length=80 --exclude="{**/*.gen.dart,**/*.freezed.dart,**/*.g.dart,**/*.module.dart,}" --dry-run
    packageFilters:
      dirExists:
        - lib
    description: Run `dcm format` checks on changed dart packages.

  format_check_test_changes:
    run: melos exec --diff=origin/main...HEAD -- dcm format test --line-length=80 --exclude="{**/*_test.dart,}" --dry-run
    packageFilters:
      dirExists:
        - test
    description: Run `dcm format` checks on changed packages with tests.

  format_check:
    run: melos run format_check_lib && melos run format_check_test
    description: Run `dcm format` checks on all packages.

  format_check_lib:
    run: melos exec -c 1 -- dcm format lib --line-length=80 --exclude="{**/*.gen.dart,**/*.freezed.dart,**/*.g.dart,**/*.module.dart,}" --dry-run
    packageFilters:
      dirExists:
        - lib
    description: Run `dcm format` checks on dart packages.

  format_check_test:
    run: melos exec -c 1 -- dcm format test --line-length=80 --exclude="{**/*_test.dart,}" --dry-run
    packageFilters:
      dirExists:
        - test
    description: Run `dcm format` checks on packages with tests.

  format:
    run: melos run format_lib && melos run format_test
    description: Runs `dcm format` on all packages.

  format_lib:
    run: melos exec -c 1 -- dcm format lib --no-congratulate --line-length=80 --exclude="{**/*.gen.dart,**/*.freezed.dart,**/*.g.dart,**/*.module.dart,}"
    packageFilters:
      dirExists:
        - lib
    description: Runs `dcm format` on dart packages.

  format_test:
    run: melos exec -c 1 -- dcm format test --no-congratulate --line-length=80 --exclude="{**/*_test.dart,}"
    packageFilters:
      dirExists:
        - test
    description: Runs `dcm format` on packages with tests.

  dependency_validator:
    run: melos exec -c 1 -- "dart run dependency_validator"
    description: Run `dart run dependency_validator` checks on all packages.

  dependency_validator_changes:
    run: melos exec -c 1 --diff=origin/main...HEAD --include-dependents -- "dart run dependency_validator"
    description: Run `dart run dependency_validator` checks on changed packages.

  autogen:
    run: melos exec -c 1 -- "dart run build_runner build --delete-conflicting-outputs"
    packageFilters:
      dependsOn:
        - "build_runner"
    description: Run `dart run build_runner build --delete-conflicting-outputs`

  autogen_watch:
    run: melos exec -c 1 -- "dart run build_runner watch --delete-conflicting-outputs"
    packageFilters:
      dependsOn:
        - "build_runner"
    description: Run `dart run build_runner watch --delete-conflicting-outputs`

  autogen_changes:
    run: melos exec -c 1 -- "dart run build_runner build --delete-conflicting-outputs"
    packageFilters:
      dependsOn:
        - "build_runner"
      diff: origin/main...HEAD
    description: Run `dart run build_runner build --delete-conflicting-outputs`

  format_analyze_commit:
    run: |
      set -e
      melos run format
      melos run analyze
      melos run dependency_validator
      git add .
      git commit -m "refactor: melos run format and analyze"
      git push
    description: Format, analyze, commit with standard message, and push.

  verify-build:
    run: melos exec -c 1 -- "dart test test/build_verify_test.dart"
    description: Verify that all packages build.
    packageFilters:
      fileExists: test/build_verify_test.dart
      
  gen_l10n:
    run: dart pub global run melos run combine_arb_files && dart pub global run melos run gen_lok_l10n
    description: Combine and generate localization files.

  combine_arb_files:
    run : dart pub global run melos exec --scope="equiti_localization" -- "dart lib/src/combine_arb_files.dart"
    description: Combine all .arb files.

  gen_lok_l10n:
    run: dart pub global run melos exec --scope="equiti_localization" -- "dart run lokalise_flutter_sdk:gen-lok-l10n"
    description: Generate localization files using lokalise_flutter_sdk.

  gen_l10n_watch:
    run: melos run combine_arb_files_watch
    description: Combine and generate localization files.

  combine_arb_files_watch:
    run : melos exec --scope="equiti_localization" -- "dart lib/src/combine_arb_files.dart --watch"
    description: Combine and watch for all .arb files.