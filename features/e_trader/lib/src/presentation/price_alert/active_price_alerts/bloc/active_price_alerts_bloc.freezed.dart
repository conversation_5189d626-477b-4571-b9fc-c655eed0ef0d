// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_price_alerts_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ActivePriceAlertsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsEvent()';
}


}

/// @nodoc
class $ActivePriceAlertsEventCopyWith<$Res>  {
$ActivePriceAlertsEventCopyWith(ActivePriceAlertsEvent _, $Res Function(ActivePriceAlertsEvent) __);
}


/// @nodoc


class _StartActiveAlerts implements ActivePriceAlertsEvent {
  const _StartActiveAlerts({this.symbol});
  

 final  String? symbol;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StartActiveAlertsCopyWith<_StartActiveAlerts> get copyWith => __$StartActiveAlertsCopyWithImpl<_StartActiveAlerts>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StartActiveAlerts&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,symbol);

@override
String toString() {
  return 'ActivePriceAlertsEvent.startActiveAlerts(symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$StartActiveAlertsCopyWith<$Res> implements $ActivePriceAlertsEventCopyWith<$Res> {
  factory _$StartActiveAlertsCopyWith(_StartActiveAlerts value, $Res Function(_StartActiveAlerts) _then) = __$StartActiveAlertsCopyWithImpl;
@useResult
$Res call({
 String? symbol
});




}
/// @nodoc
class __$StartActiveAlertsCopyWithImpl<$Res>
    implements _$StartActiveAlertsCopyWith<$Res> {
  __$StartActiveAlertsCopyWithImpl(this._self, this._then);

  final _StartActiveAlerts _self;
  final $Res Function(_StartActiveAlerts) _then;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbol = freezed,}) {
  return _then(_StartActiveAlerts(
symbol: freezed == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _ProcessAlert implements ActivePriceAlertsEvent {
  const _ProcessAlert(this.alertResponse);
  

 final  ActiveAlertResponse? alertResponse;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessAlertCopyWith<_ProcessAlert> get copyWith => __$ProcessAlertCopyWithImpl<_ProcessAlert>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessAlert&&(identical(other.alertResponse, alertResponse) || other.alertResponse == alertResponse));
}


@override
int get hashCode => Object.hash(runtimeType,alertResponse);

@override
String toString() {
  return 'ActivePriceAlertsEvent.processAlert(alertResponse: $alertResponse)';
}


}

/// @nodoc
abstract mixin class _$ProcessAlertCopyWith<$Res> implements $ActivePriceAlertsEventCopyWith<$Res> {
  factory _$ProcessAlertCopyWith(_ProcessAlert value, $Res Function(_ProcessAlert) _then) = __$ProcessAlertCopyWithImpl;
@useResult
$Res call({
 ActiveAlertResponse? alertResponse
});


$ActiveAlertResponseCopyWith<$Res>? get alertResponse;

}
/// @nodoc
class __$ProcessAlertCopyWithImpl<$Res>
    implements _$ProcessAlertCopyWith<$Res> {
  __$ProcessAlertCopyWithImpl(this._self, this._then);

  final _ProcessAlert _self;
  final $Res Function(_ProcessAlert) _then;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? alertResponse = freezed,}) {
  return _then(_ProcessAlert(
freezed == alertResponse ? _self.alertResponse : alertResponse // ignore: cast_nullable_to_non_nullable
as ActiveAlertResponse?,
  ));
}

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActiveAlertResponseCopyWith<$Res>? get alertResponse {
    if (_self.alertResponse == null) {
    return null;
  }

  return $ActiveAlertResponseCopyWith<$Res>(_self.alertResponse!, (value) {
    return _then(_self.copyWith(alertResponse: value));
  });
}
}

/// @nodoc


class _EmitError implements ActivePriceAlertsEvent {
  const _EmitError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmitError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsEvent.emitError()';
}


}




/// @nodoc


class _UpdateActiveAlerts implements ActivePriceAlertsEvent {
  const _UpdateActiveAlerts(this.eventType, this.symbol);
  

 final  EventType eventType;
 final  String? symbol;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateActiveAlertsCopyWith<_UpdateActiveAlerts> get copyWith => __$UpdateActiveAlertsCopyWithImpl<_UpdateActiveAlerts>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateActiveAlerts&&(identical(other.eventType, eventType) || other.eventType == eventType)&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,eventType,symbol);

@override
String toString() {
  return 'ActivePriceAlertsEvent.updateActiveAlerts(eventType: $eventType, symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$UpdateActiveAlertsCopyWith<$Res> implements $ActivePriceAlertsEventCopyWith<$Res> {
  factory _$UpdateActiveAlertsCopyWith(_UpdateActiveAlerts value, $Res Function(_UpdateActiveAlerts) _then) = __$UpdateActiveAlertsCopyWithImpl;
@useResult
$Res call({
 EventType eventType, String? symbol
});




}
/// @nodoc
class __$UpdateActiveAlertsCopyWithImpl<$Res>
    implements _$UpdateActiveAlertsCopyWith<$Res> {
  __$UpdateActiveAlertsCopyWithImpl(this._self, this._then);

  final _UpdateActiveAlerts _self;
  final $Res Function(_UpdateActiveAlerts) _then;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? eventType = null,Object? symbol = freezed,}) {
  return _then(_UpdateActiveAlerts(
null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as EventType,freezed == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _RemoveDeletedAlert implements ActivePriceAlertsEvent {
  const _RemoveDeletedAlert(this.alertId);
  

 final  String alertId;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RemoveDeletedAlertCopyWith<_RemoveDeletedAlert> get copyWith => __$RemoveDeletedAlertCopyWithImpl<_RemoveDeletedAlert>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RemoveDeletedAlert&&(identical(other.alertId, alertId) || other.alertId == alertId));
}


@override
int get hashCode => Object.hash(runtimeType,alertId);

@override
String toString() {
  return 'ActivePriceAlertsEvent.removeDeletedAlert(alertId: $alertId)';
}


}

/// @nodoc
abstract mixin class _$RemoveDeletedAlertCopyWith<$Res> implements $ActivePriceAlertsEventCopyWith<$Res> {
  factory _$RemoveDeletedAlertCopyWith(_RemoveDeletedAlert value, $Res Function(_RemoveDeletedAlert) _then) = __$RemoveDeletedAlertCopyWithImpl;
@useResult
$Res call({
 String alertId
});




}
/// @nodoc
class __$RemoveDeletedAlertCopyWithImpl<$Res>
    implements _$RemoveDeletedAlertCopyWith<$Res> {
  __$RemoveDeletedAlertCopyWithImpl(this._self, this._then);

  final _RemoveDeletedAlert _self;
  final $Res Function(_RemoveDeletedAlert) _then;

/// Create a copy of ActivePriceAlertsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? alertId = null,}) {
  return _then(_RemoveDeletedAlert(
null == alertId ? _self.alertId : alertId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$ActivePriceAlertsState {

 List<PriceAlert> get alerts; ActivePriceAlertsProcessState get processState;
/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivePriceAlertsStateCopyWith<ActivePriceAlertsState> get copyWith => _$ActivePriceAlertsStateCopyWithImpl<ActivePriceAlertsState>(this as ActivePriceAlertsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsState&&const DeepCollectionEquality().equals(other.alerts, alerts)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(alerts),processState);

@override
String toString() {
  return 'ActivePriceAlertsState(alerts: $alerts, processState: $processState)';
}


}

/// @nodoc
abstract mixin class $ActivePriceAlertsStateCopyWith<$Res>  {
  factory $ActivePriceAlertsStateCopyWith(ActivePriceAlertsState value, $Res Function(ActivePriceAlertsState) _then) = _$ActivePriceAlertsStateCopyWithImpl;
@useResult
$Res call({
 List<PriceAlert> alerts, ActivePriceAlertsProcessState processState
});


$ActivePriceAlertsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$ActivePriceAlertsStateCopyWithImpl<$Res>
    implements $ActivePriceAlertsStateCopyWith<$Res> {
  _$ActivePriceAlertsStateCopyWithImpl(this._self, this._then);

  final ActivePriceAlertsState _self;
  final $Res Function(ActivePriceAlertsState) _then;

/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? alerts = null,Object? processState = null,}) {
  return _then(_self.copyWith(
alerts: null == alerts ? _self.alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<PriceAlert>,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ActivePriceAlertsProcessState,
  ));
}
/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivePriceAlertsProcessStateCopyWith<$Res> get processState {
  
  return $ActivePriceAlertsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _ActivePriceAlertsState implements ActivePriceAlertsState {
  const _ActivePriceAlertsState({final  List<PriceAlert> alerts = const [], this.processState = const ActivePriceAlertsProcessState.empty()}): _alerts = alerts;
  

 final  List<PriceAlert> _alerts;
@override@JsonKey() List<PriceAlert> get alerts {
  if (_alerts is EqualUnmodifiableListView) return _alerts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_alerts);
}

@override@JsonKey() final  ActivePriceAlertsProcessState processState;

/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivePriceAlertsStateCopyWith<_ActivePriceAlertsState> get copyWith => __$ActivePriceAlertsStateCopyWithImpl<_ActivePriceAlertsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ActivePriceAlertsState&&const DeepCollectionEquality().equals(other._alerts, _alerts)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_alerts),processState);

@override
String toString() {
  return 'ActivePriceAlertsState(alerts: $alerts, processState: $processState)';
}


}

/// @nodoc
abstract mixin class _$ActivePriceAlertsStateCopyWith<$Res> implements $ActivePriceAlertsStateCopyWith<$Res> {
  factory _$ActivePriceAlertsStateCopyWith(_ActivePriceAlertsState value, $Res Function(_ActivePriceAlertsState) _then) = __$ActivePriceAlertsStateCopyWithImpl;
@override @useResult
$Res call({
 List<PriceAlert> alerts, ActivePriceAlertsProcessState processState
});


@override $ActivePriceAlertsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$ActivePriceAlertsStateCopyWithImpl<$Res>
    implements _$ActivePriceAlertsStateCopyWith<$Res> {
  __$ActivePriceAlertsStateCopyWithImpl(this._self, this._then);

  final _ActivePriceAlertsState _self;
  final $Res Function(_ActivePriceAlertsState) _then;

/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? alerts = null,Object? processState = null,}) {
  return _then(_ActivePriceAlertsState(
alerts: null == alerts ? _self._alerts : alerts // ignore: cast_nullable_to_non_nullable
as List<PriceAlert>,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ActivePriceAlertsProcessState,
  ));
}

/// Create a copy of ActivePriceAlertsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivePriceAlertsProcessStateCopyWith<$Res> get processState {
  
  return $ActivePriceAlertsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$ActivePriceAlertsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState()';
}


}

/// @nodoc
class $ActivePriceAlertsProcessStateCopyWith<$Res>  {
$ActivePriceAlertsProcessStateCopyWith(ActivePriceAlertsProcessState _, $Res Function(ActivePriceAlertsProcessState) __);
}


/// @nodoc


class ActivePriceAlertsEmpty implements ActivePriceAlertsProcessState {
  const ActivePriceAlertsEmpty();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsEmpty);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState.empty()';
}


}




/// @nodoc


class ActivePriceAlertsConnected implements ActivePriceAlertsProcessState {
  const ActivePriceAlertsConnected();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsConnected);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState.connected()';
}


}




/// @nodoc


class ActivePriceAlertsError implements ActivePriceAlertsProcessState {
  const ActivePriceAlertsError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState.error()';
}


}




/// @nodoc


class ActivePriceAlertsLoading implements ActivePriceAlertsProcessState {
  const ActivePriceAlertsLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState.loading()';
}


}




/// @nodoc


class ActivePriceAlertsSuccess implements ActivePriceAlertsProcessState {
  const ActivePriceAlertsSuccess();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivePriceAlertsSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ActivePriceAlertsProcessState.success()';
}


}




// dart format on
