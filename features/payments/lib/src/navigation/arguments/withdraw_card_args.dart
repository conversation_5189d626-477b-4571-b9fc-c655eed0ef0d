import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';

part 'withdraw_card_args.freezed.dart';
part 'withdraw_card_args.g.dart';

@freezed
abstract class WithdrawCardArgs with _$WithdrawCardArgs {
  const factory WithdrawCardArgs({
    required WithdrawFlowParams withdrawFlowParams,
  }) = _WithdrawCardArgs;

  factory WithdrawCardArgs.fromJson(Map<String, dynamic> json) =>
      _$WithdrawCardArgsFromJson(json);
}
