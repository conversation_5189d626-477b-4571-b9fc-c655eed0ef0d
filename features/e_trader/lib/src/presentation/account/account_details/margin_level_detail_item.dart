import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/model/margin_segment_level.dart';
import 'package:flutter/material.dart';

class MarginLevelDetailItem extends StatelessWidget {
  const MarginLevelDetailItem({
    required this.title,
    required this.subTitle,
    required this.segmentLevel,
  });
  final String title;
  final String subTitle;
  final MarginSegmentLevel segmentLevel;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    // Get color based on segment level
    final segmentColor = _getSegmentColor(theme, segmentLevel);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Colored indicator pill
          Row(
            children: [
              Container(
                width: 20,
                height: 8,
                decoration: BoxDecoration(
                  color: segmentColor,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              const SizedBox(width: 8),
              DuploText(
                text: title,
                style: textStyles.textSm,
                fontWeight: DuploFontWeight.medium,
                color: _getSegmentColor(theme, segmentLevel),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
          DuploText(
            text: subTitle,
            style: textStyles.textSm,
            fontWeight: DuploFontWeight.regular,
            color: theme.text.textSecondary,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getSegmentColor(DuploThemeData theme, MarginSegmentLevel level) =>
      switch (level) {
        MarginSegmentLevel.protected => theme.foreground.fgBrandSecondary,
        MarginSegmentLevel.safe => theme.foreground.fgSuccessSecondary,
        MarginSegmentLevel.caution => theme.foreground.fgWarningSecondary,
        MarginSegmentLevel.marginCall => theme.foreground.fgErrorSecondary,
        MarginSegmentLevel.stopOut => theme.utility.utilityError700,
      };
}
