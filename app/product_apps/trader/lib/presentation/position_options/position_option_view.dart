import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';

class PositionOptionView extends StatelessWidget {
  const PositionOptionView({super.key, required this.model});
  final SymbolDetailViewModel model;

  /// TODO: <PERSON><PERSON> no <PERSON> here
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: TextButton(
          onPressed: () {
            showPositionOptionSheet(context, model, "USD");
          },
          child: Text('Show Position Option'),
        ),
      ),
    );
  }
}
