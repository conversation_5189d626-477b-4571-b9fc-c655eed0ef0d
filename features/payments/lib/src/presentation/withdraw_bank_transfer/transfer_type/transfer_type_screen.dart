import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/bloc/transfer_type_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/widgets/transfer_type_loaded_screen.dart';

class TransferTypeScreen extends StatelessWidget {
  const TransferTypeScreen({
    super.key,
    required this.bank,
    required this.bankTransferAmountModel,
  });
  final WithdrawFlowParams bankTransferAmountModel;
  final BankAccountData bank;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<TransferTypeBloc>()
                ..add(TransferTypeEvent.onRequestClientProfileData()),
      child: BlocBuilder<TransferTypeBloc, TransferTypeState>(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState,
        builder: (ctx, state) {
          return switch (state.processState) {
            TransferTypeLoadedState() => TransferTypeLoadedScreen(
              bankTransferAmountModel: bankTransferAmountModel,
              bank: bank,
            ),
            TransferTypeSuccessState(:final status, :final operationId) =>
              _handleSuccessState(status, operationId),
            TransferTypeErrorState() => WithdrawStatusScreen.error(
              onButtonPressed: () => _onTap(ctx),
            ),
          };
        },
      ),
    );
  }

  Widget _handleSuccessState(WithdrawStatusType status, String? operationId) {
    // If status is awaiting documents and we have an operationId, navigate to upload document screen
    if (status == WithdrawStatusType.awaitingDocuments && operationId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        diContainer<PaymentNavigation>().goToWithdrawNewBankUploadDocScreen(
          operationId: operationId,
          tradingAccountId: bankTransferAmountModel.tradingAccountId,
          replace: true,
        );
      });

      return _shimmerLoading();
    }

    // For all other statuses, show the status screen
    return WithdrawStatusScreen.fromWithDrawStatus(statusType: status);
  }

  void _onTap(BuildContext ctx) {
    final bloc = ctx.read<TransferTypeBloc>();
    bloc.add(const TransferTypeEvent.resetProcessState());
  }

  Widget _shimmerLoading() {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: DuploShimmerList(itemShimmerType: DuploShimmerType.animated),
        ),
      ),
    );
  }
}
