parameters:
- name: channel
  type: string
  default: 'stable'
- name: customVersion
  type: string
  default: '3.32.6'

steps:
- task: FlutterInstall@0
  displayName: 'Install Flutter'
  inputs:
    channel: ${{ parameters.channel }}
    version: 'custom'
    customVersion: ${{ parameters.customVersion }}
- script: |
    echo 'export PATH=$PATH:$HOME/.pub-cache/bin' >> ~/.profile
    source ~/.profile
- script: dart pub global activate melos
  displayName: 'Install Melos'