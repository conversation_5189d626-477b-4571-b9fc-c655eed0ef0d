import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';

import 'package:user_account/user_account.dart' show ClientProfileUseCase;

part 'transfer_type_bloc.freezed.dart';
part 'transfer_type_event.dart';
part 'transfer_type_state.dart';

class TransferTypeBloc extends Bloc<TransferTypeEvent, TransferTypeState> {
  final WithdrawalUseCase _withdrawalUseCase;
  final ClientProfileUseCase _clientProfileUseCase;

  TransferTypeBloc(this._withdrawalUseCase, this._clientProfileUseCase)
    : super(TransferTypeState()) {
    on<_OnChangeTransferType>(_onChangeTransferType);
    on<_OnWithdrawalRequest>(_onWithdrawalRequest);
    on<_OnRequestClientProfileData>(_onRequestClientProfileData);
    on<_ResetProcessState>(_onResetProcessState);
    on<_OnFeesChanged>(_onFeesChanged);
  }

  FutureOr<void> _onChangeTransferType(
    _OnChangeTransferType event,
    Emitter<TransferTypeState> emit,
  ) {
    emit(state.copyWith(transferType: event.transferType));
  }

  FutureOr<void> _onWithdrawalRequest(
    _OnWithdrawalRequest event,
    Emitter<TransferTypeState> emit,
  ) async {
    emit(state.copyWith(isRequestWithdrawButtonLoading: true));

    try {
      final result =
          await _withdrawalUseCase(
            request: WithdrawRequestModel(
              paymentType: event.amountModel.paymentType.name,
              accountInfo: AccountInfo(
                tradingAccountId: event.amountModel.tradingAccountId,
                accountCurrency: event.amountModel.accountCurrency,
              ),
              amount: Amount(
                currency: event.amountModel.currency,
                conversionRateString: event.amountModel.conversionRateString,
                amount: event.amountModel.amount,
                convertedAmount: event.amountModel.convertedAmount,
                conversionRate: event.amountModel.conversionRate,
              ),
              metadata: Metadata.bank(
                transferType: state.transferType ?? "",
                bankAccount: event.bankAccount,
              ),
            ),
          ).run();

      if (isClosed) return;

      result.fold(
        (failure) {
          log('Withdrawal request failed: $failure');
          emit(
            state.copyWith(
              processState: TransferTypeProcessState.error(
                message: "Something went wrong! Please try again later",
              ),
              isRequestWithdrawButtonLoading: false,
            ),
          );
        },
        (WithdrawResponseModel withdrawResponseModel) {
          switch (withdrawResponseModel) {
            case final WithdrawSuccessResponse success:
              log('Withdrawal request successful: ${success.data}');
              emit(
                state.copyWith(
                  processState: TransferTypeProcessState.success(
                    status: success.data.status ?? WithdrawStatusType.error,
                    operationId: success.data.operationId,
                  ),
                  isRequestWithdrawButtonLoading: false,
                ),
              );
              break;
            case final WithdrawInternalServerErrorResponse internalServerError:
              log(
                'Withdrawal internal server error: ${internalServerError.error}',
              );
              emit(
                state.copyWith(
                  processState: TransferTypeProcessState.error(
                    message: internalServerError.error,
                  ),
                  isRequestWithdrawButtonLoading: false,
                ),
              );
              break;
          }
        },
      );
    } catch (e) {
      log('Withdrawal request exception: $e');
      if (!isClosed) {
        emit(
          state.copyWith(
            processState: TransferTypeProcessState.error(
              message: "Something went wrong! Please try again later",
            ),
            isRequestWithdrawButtonLoading: false,
          ),
        );
      }
    }
  }

  FutureOr<void> _onRequestClientProfileData(
    _OnRequestClientProfileData event,
    Emitter<TransferTypeState> emit,
  ) async {
    final clientProfile = await _clientProfileUseCase().run();
    clientProfile.fold(
      (left) {
        log("Client profile request failed: $left");
      },
      (right) {
        if (!isClosed) emit(state.copyWith(clientAccountId: right.accountId));
      },
    );
  }

  FutureOr<void> _onResetProcessState(
    _ResetProcessState event,
    Emitter<TransferTypeState> emit,
  ) {
    emit(
      state.copyWith(
        processState: TransferTypeProcessState.loaded(),
        isRequestWithdrawButtonLoading: false,
        transferType: null,
      ),
    );
  }

  FutureOr<void> _onFeesChanged(
    _OnFeesChanged event,
    Emitter<TransferTypeState> emit,
  ) {
    // Validate withdrawal amount with new fees
    final validationResult = _validateWithdrawalAmount(
      event.amountModel.amount,
      event.amountModel.accountBalance,
      event.fees,
      event.amountModel.accountCurrency,
    );

    emit(
      state.copyWith(
        withdrawalFees: event.fees,
        hasInsufficientFunds: validationResult.hasInsufficientFunds,
        insufficientFundsMessage:
            '', // Will be generated in UI with localization
        // Store validation data for UI to generate localized message
        validationWithdrawalAmount: event.amountModel.amount,
        validationAccountBalance: event.amountModel.accountBalance,
        validationWithdrawalFees: event.fees,
        validationCurrency: event.amountModel.accountCurrency,
      ),
    );
  }

  /// Validates if the withdrawal amount plus fees exceeds the account balance
  ({bool hasInsufficientFunds, String errorMessage}) _validateWithdrawalAmount(
    num withdrawalAmount,
    num accountBalance,
    num withdrawalFees,
    String currency,
  ) {
    // Skip validation if amount is 0 or empty
    if (withdrawalAmount <= 0) {
      return (hasInsufficientFunds: false, errorMessage: '');
    }

    // Calculate total required amount (withdrawal amount + fees)
    final totalRequired = withdrawalAmount + withdrawalFees;

    // Check if total required exceeds account balance
    if (totalRequired > accountBalance) {
      final formattedBalance = accountBalance.toStringAsFixed(2);
      final formattedTotal = totalRequired.toStringAsFixed(2);

      String errorMessage;
      if (withdrawalFees > 0) {
        final formattedFees = withdrawalFees.toStringAsFixed(2);
        errorMessage =
            'Insufficient funds. Available balance: $formattedBalance $currency, Required: $formattedTotal $currency (including $formattedFees $currency fee)';
      } else {
        errorMessage =
            'Insufficient funds. Available balance: $formattedBalance $currency, Required: $formattedTotal $currency';
      }

      return (hasInsufficientFunds: true, errorMessage: errorMessage);
    }

    return (hasInsufficientFunds: false, errorMessage: '');
  }
}
