part of 'deposit_payment_options_bloc.dart';

@freezed
sealed class DepositPaymentOptionsEvent with _$DepositPaymentOptionsEvent {
  const factory DepositPaymentOptionsEvent.getPaymentOptions() =
      _GetPaymentOptions;

  const factory DepositPaymentOptionsEvent.setDepositFlowConfig({
    required DepositFlowConfig depositFlowConfig,
  }) = _SetDepositFlowConfig;

  const factory DepositPaymentOptionsEvent.navigateToSelectAccountAndAmount({
    required DepositPaymentMethodGroup paymentMethodGroup,
    required DepositPaymentMethod paymentMethod,
  }) = _NavigateToSelectAccountAndAmount;
}
