import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:domain/domain.dart';

part 'additional_payment_methods_args.freezed.dart';

@freezed
abstract class AdditionalPaymentMethodsArgs
    with _$AdditionalPaymentMethodsArgs {
  const factory AdditionalPaymentMethodsArgs({
    required String url,
    required String title,
    required String transactionId,
    required String accountNumber,
    required DepositFlowConfig depositFlowConfig,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) = _AdditionalPaymentMethodsArgs;
}
