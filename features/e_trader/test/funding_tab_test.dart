// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/funding_tab.dart';
import 'scenarios/funding_empty_scenario.dart';
import 'scenarios/funding_success_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Fudning Tab''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: User views Funding Tab (scenarios: [fundingSuccessScenario], 'funding_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Funding Tab (scenarios: [fundingSuccessScenario], 'funding_success')''',
          );
          await theAppIsRendered(
            tester,
            FundingTab(),
            scenarios: [fundingSuccessScenario],
          );
          await iWait(tester);
          await screenshotVerified(tester, 'funding_success');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Funding Tab (scenarios: [fundingSuccessScenario], 'funding_success')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Funding Tab (scenarios: [fundingEmptyScenario], 'funding_empty')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Funding Tab (scenarios: [fundingEmptyScenario], 'funding_empty')''',
          );
          await theAppIsRendered(
            tester,
            FundingTab(),
            scenarios: [fundingEmptyScenario],
          );
          await iWait(tester);
          await screenshotVerified(tester, 'funding_empty');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Funding Tab (scenarios: [fundingEmptyScenario], 'funding_empty')''',
            success,
          );
        }
      },
    );
    testGoldens('''User select sort Funding''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select sort Funding''');
        await theAppIsRendered(
          tester,
          FundingTab(),
          scenarios: [fundingSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_sort_funding");
        await iWait(tester);
        await screenshotVerified(tester, 'funding_sort');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select sort Funding''', success);
      }
    });
    testGoldens('''User select filter Funding''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select filter Funding''');
        await theAppIsRendered(
          tester,
          FundingTab(),
          scenarios: [fundingSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_filter_funding");
        await iWait(tester);
        await screenshotVerified(tester, 'funding_filter');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select filter Funding''', success);
      }
    });
    testGoldens('''User select filter Funding but no data''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select filter Funding but no data''');
        await theAppIsRendered(
          tester,
          FundingTab(),
          scenarios: [fundingSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_filter_funding");
        await theAppIsRendered(
          tester,
          FundingTab(),
          scenarios: [fundingEmptyScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "MoneyOut");
        await iTapIdentifier(tester, "apply_filters_funding");
        await iWait(tester);
        await screenshotVerified(tester, 'funding_filter_no_data');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select filter Funding but no data''', success);
      }
    });
  });
}
