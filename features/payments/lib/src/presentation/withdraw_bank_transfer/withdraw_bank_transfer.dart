import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/model/screen_mode.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_account_empty_widget.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_account_error_widget.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_account_loading_widget.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/bank_account_success_widget.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/delete_account_error_bottom_sheet.dart';

class WithdrawBankTransfer extends StatelessWidget {
  const WithdrawBankTransfer({
    super.key,
    required this.tradingAccountId,
    required this.bankTransferAmountModel,
  });

  final String tradingAccountId;
  final WithdrawFlowParams bankTransferAmountModel;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<WithdrawBankTransferBloc>()..add(
                WithdrawBankTransferEvent.fetchBankAccounts(tradingAccountId),
              ),
      child: BlocListener<WithdrawBankTransferBloc, WithdrawBankTransferState>(
        listenWhen:
            (previous, current) =>
                previous.shouldShowDeleteAccountErrorBottomSheet !=
                current.shouldShowDeleteAccountErrorBottomSheet,
        listener: (listenerContext, state) {
          if (state.shouldShowDeleteAccountErrorBottomSheet)
            DuploSheet.showModalSheet<Widget>(
              context: context,
              hasTopBarLayer: false,
              hideCloseButton: true,
              content: (contextContext) {
                return DeleteAccountErrorBottomSheet();
              },
              title: '',
            );
        },
        child: Scaffold(
          backgroundColor: theme.background.bgPrimary,
          appBar: AppBar(
            backgroundColor: theme.background.bgPrimary,
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back),
            ),
            actions: [
              BlocBuilder<WithdrawBankTransferBloc, WithdrawBankTransferState>(
                buildWhen:
                    (previous, current) =>
                        previous.screenMode != current.screenMode,
                builder: (builderContext, state) {
                  return state.screenMode == ScreenMode.modify
                      ? Container()
                      : Padding(
                        padding: const EdgeInsets.only(right: 15.0),
                        child: InkWell(
                          key: Key("enable_delete_bank_account_button"),
                          onTap: () {
                            builderContext.read<WithdrawBankTransferBloc>().add(
                              WithdrawBankTransferEvent.onChangeMode(
                                ScreenMode.modify,
                              ),
                            );
                          },
                          child: assets.Assets.images.deleteModeIc.svg(),
                        ),
                      );
                },
              ),
            ],
            title: DuploText(
              text:
                  EquitiLocalization.of(
                    context,
                  ).payments_withdraw_to_your_bank_Account,
              color: theme.text.textPrimary,
              fontWeight: DuploFontWeight.semiBold,
              style: DuploTextStyles.of(context).textSm,
            ),
          ),
          body: SafeArea(
            child: BlocBuilder<
              WithdrawBankTransferBloc,
              WithdrawBankTransferState
            >(
              buildWhen:
                  (previous, current) =>
                      previous.withdrawBankTransferProccessState !=
                      current.withdrawBankTransferProccessState,
              builder: (builderContext, state) {
                switch (state.withdrawBankTransferProccessState) {
                  case WithdrawBankTransferProccessStateSuccess():
                    return BankAccountSuccessWidget(
                      bankTransferAmountModel: bankTransferAmountModel,
                    );
                  case WithdrawBankTransferProccessStateError():
                    return const BankAccountErrorWidget();
                  case WithdrawBankTransferProccessStateLoading():
                    return const BankAccountLoadingWidget();
                  case WithdrawBankTransferProccessStateSuccessEmpty():
                    return BankAccountEmptyWidget(
                      bankTransferAmountModel: bankTransferAmountModel,
                    );
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}
