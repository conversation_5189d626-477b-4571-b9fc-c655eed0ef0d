import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_status_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/usecase/deposit_status_usecase.dart';
import 'package:payment/src/presentation/payment_status/bloc/deposit_status_bloc.dart';
import 'package:payment/src/presentation/widgets/transaction_status_screen.dart';
import 'package:prelude/prelude.dart';

import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';

/// A screen to display the payment status of a transaction
/// If the status is provided, it will be displayed immediately
/// Otherwise, it will poll the API for the status
class DepositStatusScreen extends StatelessWidget {
  final String transactionId;
  final String accountNumber;
  final PaymentStatus? paymentStatus;
  final DepositFlowConfig depositFlowConfig;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;

  const DepositStatusScreen({
    Key? key,
    required this.transactionId,
    required this.accountNumber,
    this.paymentStatus,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
    required this.depositFlowConfig,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DepositStatusBloc>(
      create: (providerContext) {
        final depositStatusBloc = DepositStatusBloc(
          depositStatusUsecase: diContainer<DepositStatusUsecase>(),
          maxPollingAttempts: maxPollingAttempts,
          pollingFrequencySeconds: pollingFrequencySeconds,
        );
        // If status is provided, return the bloc
        log(
          "PaymentStatus: ${paymentStatus ?? ''} ${EquitiFormatter.formatNumber(value: maxPollingAttempts ?? 0, locale: "en")}  ${EquitiFormatter.formatNumber(value: pollingFrequencySeconds ?? 0, locale: "en")}",
        );
        if (paymentStatus != null) {
          return depositStatusBloc
            ..add(DepositStatusEvent.setPaymentStatus(status: paymentStatus!));
        }
        // Start polling if status is not provided
        depositStatusBloc.add(
          DepositStatusEvent.fetchPaymentStatus(
            transactionId: transactionId,
            accountNumber: accountNumber,
          ),
        );
        return depositStatusBloc;
      },
      child: BlocBuilder<DepositStatusBloc, DepositStatusState>(
        buildWhen: (previousState, currentState) {
          return previousState.processState != currentState.processState;
        },
        builder: (builderContext, state) {
          return switch (state.processState) {
            PaymentStatusLoadingProcessState() => _buildLoadingScreen(),
            PaymentStatusSuccessProcessState() => _buildStatusScreen(
              state.status!,
              builderContext,
            ),
            PaymentStatusErrorProcessState() => _buildErrorScreen(
              builderContext,
            ),
            PaymentStatusTimeoutProcessState() => _buildTimeoutScreen(
              builderContext,
            ),
          };
        },
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return LoadingView();
  }

  Widget _buildErrorScreen(BuildContext context) {
    return TransactionStatusScreen.error(
      onChangePaymentMethod: _navigateToPaymentOptions,
      context: context,
    );
  }

  Widget _buildTimeoutScreen(BuildContext context) {
    return TransactionStatusScreen.stillProcessing(
      onContinue: _navigateBasedOnDepositType,
      context: context,
    );
  }

  Widget _buildStatusScreen(PaymentStatus status, BuildContext context) {
    switch (status) {
      case PaymentStatus.pending:
        return TransactionStatusScreen.submitted(
          context: context,
          onContinue: _navigateBasedOnDepositType,
          onMakeAnotherDeposit: _navigateToPaymentOptions,
        );
      case PaymentStatus.rejected:
        return TransactionStatusScreen.declined(
          context: context,
          onTryAgain: _onRejectedTryAgainPressed,
          onChangePaymentMethod: _navigateToPaymentOptions,
        );
      case PaymentStatus.successful:
        return TransactionStatusScreen.success(
          context: context,
          onContinue: _navigateBasedOnDepositType,
          onMakeAnotherDeposit: _navigateToPaymentOptions,
        );
      default:
        // This should not happen as 'new' status is handled by polling
        return _buildLoadingScreen();
    }
  }

  void _onRejectedTryAgainPressed() {
    diContainer<PaymentNavigation>()
        .goBackToDepositSelectAccountAndAmountScreen();
  }

  /// Navigate based on deposit type and origin
  void _navigateBasedOnDepositType() {
    final paymentNavigation = diContainer<PaymentNavigation>();

    switch (depositFlowConfig.depositType) {
      case DepositType.first:
        // First Deposit: Continue -> Remove all screens and navigate to origin (Hub)
        paymentNavigation.removeAllAndNavigateToHub();
        break;
      case DepositType.additional:
        // Additional Deposit: Continue -> Back to where started the flow from
        paymentNavigation.popUntilRoute(depositFlowConfig.origin);
        break;
    }
  }

  /// Navigate to Payment Options for "Make another deposit" or "Change payment method"
  void _navigateToPaymentOptions() {
    diContainer<PaymentNavigation>().popUntilRoute(
      PaymentRouteSchema.depositOptionsRoute.label,
    );
  }
}
