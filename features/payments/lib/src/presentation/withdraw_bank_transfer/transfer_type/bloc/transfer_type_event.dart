part of 'transfer_type_bloc.dart';

@freezed
sealed class TransferTypeEvent with _$TransferTypeEvent {
  const factory TransferTypeEvent.onChangeTransferType(String transferType) =
      _OnChangeTransferType;

  const factory TransferTypeEvent.onWithdrawalRequest(
    WithdrawFlowParams amountModel,
    BankAccount bankAccount,
  ) = _OnWithdrawalRequest;
  const factory TransferTypeEvent.onRequestClientProfileData() =
      _OnRequestClientProfileData;
  const factory TransferTypeEvent.resetProcessState() = _ResetProcessState;
  const factory TransferTypeEvent.onFeesChanged(
    num fees,
    WithdrawFlowParams amountModel,
  ) = _OnFeesChanged;
}
