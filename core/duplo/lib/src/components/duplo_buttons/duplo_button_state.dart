import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_base_button.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_button_theme.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_button_utils.dart';
import 'package:duplo/src/components/duplo_tap.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

/// State class for [DuploButton] that handles hover states and rendering of the button.
///
/// This class manages:
/// * Hover state tracking
/// * Button property calculations based on type and state
/// * Rendering of the button with appropriate styling
/// * Loading states with custom loading text
/// * Disabled states
/// * Focus states
/// * Error and success states
/// * Color customization for different states
/// * Text and icon styling
/// * Full width options
///
/// The button is composed of:
/// * An [InkWell] for tap and hover handling
/// * A [DuploButtonTheme] for consistent styling
/// * A [DuploBaseButton] for the content display
class DuploButtonState extends State<DuploButton> {
  /// Tracks whether the button is currently being hovered over
  bool isHovered = false;

  /// Updates the hover state when the pointer enters or exits the button
  void _onHover(bool value) {
    setState(() => isHovered = value);
  }

  /// Determines the background color based on button state (error, success, default)
  Color? _getBackgroundColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled) {
      return widget.disabledBackgroundColor ??
          buttonProperties.disabledBackgroundColor;
    }
    if (isHovered) {
      return widget.hoverBackgroundColor ??
          buttonProperties.hoverBackgroundColor;
    }

    if (widget.isError) {
      return widget.errorBackgroundColor ??
          buttonProperties.errorBackgroundColor;
    }
    if (widget.isSuccess) {
      return widget.successBackgroundColor ??
          buttonProperties.successBackgroundColor;
    }

    return widget.backgroundColor ?? buttonProperties.backgroundColor;
  }

  /// Determines the text color based on button state (disabled, hover, error, success)
  Color? _getTextColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled)
      return widget.disabledColor ?? buttonProperties.disabledColor;
    if (isHovered)
      return widget.hoverTextColor ?? buttonProperties.hoverTextColor;
    if (widget.isError)
      return widget.errorColor ?? buttonProperties.errorTextColor;
    if (widget.isSuccess)
      return widget.successColor ?? buttonProperties.successTextColor;
    return widget.textColor ?? buttonProperties.textColor;
  }

  /// Determines the icon color based on button state (disabled, hover)
  Color? _getIconColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled)
      return widget.disabledColor ?? buttonProperties.disabledColor;
    if (isHovered)
      return widget.hoverIconColor ?? buttonProperties.hoverIconColor;
    if (widget.isError)
      return widget.errorColor ?? buttonProperties.errorIconColor;
    if (widget.isSuccess)
      return widget.successColor ?? buttonProperties.successIconColor;
    return widget.iconColor ?? buttonProperties.iconColor;
  }

  Color? _getBorderColor(DuploButtonProperties buttonProperties) {
    if (widget.isDisabled)
      return widget.disabledBorderColor ?? buttonProperties.disabledBorderColor;
    if (isHovered)
      return widget.hoverBorderColor ?? buttonProperties.hoverBorderColor;
    if (widget.isError)
      return widget.errorBorderColor ?? buttonProperties.errorBorderColor;
    if (widget.isSuccess)
      return widget.successBorderColor ?? buttonProperties.successBorderColor;
    return widget.borderColor ?? buttonProperties.borderColor;
  }

  String _getTitle(DuploButtonProperties buttonProperties) {
    return ((buttonProperties.title ?? widget.title) ?? "Continue");
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    final buttonProperties = DuploButtonUtils.getButtonProperties(
      type: widget.type,
      context: context,
      isFocused: widget.isFocused,
      isPayButtonDarkMode: widget.isPayButtonDarkMode ?? false,
      applePayBorderEnabled: widget.applePayBorderEnabled ?? false,
    );

    return Semantics(
      identifier: widget.semanticsIdentifier,
      child: DuploTap(
        onTap: widget.isDisabled || widget.isLoading ? null : widget.onTap,
        useMaterial: widget.useMaterial,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onHover: _onHover,
        child: DuploButtonTheme(
          isDisabled: widget.isDisabled,
          isFocused: widget.isFocused,
          isShadowEnabled: buttonProperties.isShadowEnabled,
          padding: buttonProperties.padding,
          borderStrokeWidth: buttonProperties.borderStrokeWidth,
          isLinkButton: buttonProperties.isLinkButton,
          borderColor: _getBorderColor(buttonProperties),
          backgroundColor: _getBackgroundColor(buttonProperties),
          shadows: buttonProperties.shadows,
          isHovered: isHovered,
          child: DuploBaseButton(
            title: _getTitle(buttonProperties),
            leadingText: buttonProperties.leadingText,
            useAssetColor: widget.useAssetColor,
            isLoading: widget.isLoading,
            loadingText:
                widget.loadingText ?? localization.duplo_button_loading,
            iconSize: widget.iconSize ?? buttonProperties.iconSize,
            loadingSize: widget.loadingSize ?? buttonProperties.loadingSize,
            textColor: _getTextColor(buttonProperties),
            textFontWeight:
                widget.textFontWeight ?? buttonProperties.textFontWeight,
            textStyle: widget.textStyle ?? buttonProperties.textStyle,
            leadingIcon: widget.leadingIcon,
            trailingIcon: widget.trailingIcon,
            iconColor: _getIconColor(buttonProperties),
            useFullWidth: widget.useFullWidth,
          ),
        ),
      ),
    );
  }
}
