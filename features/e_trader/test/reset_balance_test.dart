// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'scenarios/reset_balance_success_scenario.dart';
import 'scenarios/reset_balance_failure_scenario.dart';
import 'package:e_trader/src/presentation/reset_balance/reset_balance_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import './step/i_enter_in_the_text_field.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Reset Balance''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Screenshot reset balance screen''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Screenshot reset balance screen''');
        await theAppIsRendered(
          tester,
          ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD'),
        );
        await screenshotVerified(tester, 'reset_balance');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Screenshot reset balance screen''', success);
      }
    });
    testGoldens('''Screenshot reset balance add amount''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Screenshot reset balance add amount''');
        await theAppIsRendered(
          tester,
          ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD'),
        );
        await iEnterInTheTextField(tester, "100", "reset_balance_amount_field");
        await screenshotVerified(tester, 'reset_balance_field_filled');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Screenshot reset balance add amount''', success);
      }
    });
    testGoldens('''Screenshot reset balance button tapped''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Screenshot reset balance button tapped''');
        await theAppIsRendered(
          tester,
          ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD'),
        );
        await iEnterInTheTextField(tester, "100", "reset_balance_amount_field");
        await iTapIdentifier(tester, "reset_balance_button");
        await screenshotVerified(tester, 'reset_balance_button_tapped');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Screenshot reset balance button tapped''', success);
      }
    });
  });
}
