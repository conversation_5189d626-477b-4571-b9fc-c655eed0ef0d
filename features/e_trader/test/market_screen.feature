import 'scenarios/market_success_scenario.dart';
import 'scenarios/categories_failure_scenario.dart';
import 'scenarios/linked_symbols_failure_scenario.dart';
import 'scenarios/product_hub_failure_scenario.dart';
import 'scenarios/product_hub_empty_scenario.dart';
import 'scenarios/linked_symbols_empty_scenario.dart';
import 'scenarios/categories_empty_scenario.dart';
import 'package:equiti_test/equiti_test.dart';
import 'scenarios/linked_symbols_descending_success_scenario.dart';
import 'package:e_trader/src/presentation/symbols/symbols_screen.dart';

Feature: Market Screen
  As a trader
  I want to view and interact with market symbols
  So that I can make trading decisions

  @testMethodName: testGoldens
  Scenario Outline: User views Market screen
    Given The {SymbolsScreen()} app is rendered <scenario>
 
    And screenshot verified <golden_file_name>
    Examples:
    | scenario                           | golden_file_name    |
    | scenarios: [marketSuccessScenario] | 'market_success'    |
    | scenarios: [categoriesFailureScenario] | 'categories_failure'    |
    | scenarios: [linkedSymbolsFailureScenario] | 'linked_symbols_failure'    |
    | scenarios: [productHubFailureScenario] | 'product_hub_failure'    |
    | scenarios: [productHubEmptyScenario] | 'product_hub_empty'    |
    | scenarios: [categoriesEmptyScenario] | 'categories_empty'    |
    | scenarios: [linkedSymbolsEmptyScenario] | 'linked_symbols_empty'    |
    | scenarios: [marketRESTAPISuccessScenario,productHubFailureScenario] | 'product_hub_shimmer'    |

  Scenario: User sort market watchlist items in descending order 
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [marketSuccessScenario]}
    And I wait
    And I tap {'Currencies'} text
    And I wait
    And I see {'AUDCAD'} text
    And I tap {'Sort'} text
    And I wait
    And I select {'Z - A'} sort option
    And The configuration is updated to {scenarios: [linkedSymbolsDescendingSuccessScenario]}
    And I tap {'Sort Markets'} text
    And I wait
    Then I see {'USDZAR'} text

  Scenario: User sort market watchlist items in descending order failed
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [marketSuccessScenario]}
    And I wait
    And I tap {'Currencies'} text
    And I wait
    And I see {'AUDCAD'} text
    And I tap {'Sort'} text
    And i Wait for {1} seconds
    And I select {'Z - A'} sort option
    And The configuration is updated to {scenarios: [linkedSymbolsFailureScenario]} 
    And I tap {'Sort Markets'} text
    And i Wait for {1} seconds
    Then I see {"Something went wrong"} text

  @testMethodName: testGoldens
  Scenario: Open search modal
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [marketSuccessScenario]}
    When I tap on the DuploSearchBox
    And screenshot verified {'open_search_modal'}

  @testMethodName: testGoldens
  Scenario: Search with minimum characters
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [marketSuccessScenario]}
    When I tap on the DuploSearchBox
    When I enter {"EU"} in the search field
    And I wait
    And screenshot verified {'search_with_minimum_characters'}

  @testMethodName: testGoldens
   Scenario: Faild loading symbols
     Given The {SymbolsScreen()} app runs with configuration {scenarios: [linkedSymbolsFailureScenario]}
     And I wait
     And screenshot verified {'failed_laoding_symbols'}     
 
  @testMethodName: testGoldens
  Scenario: Empty category
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [linkedSymbolsEmptyScenario]}
    When I tap on the DuploSearchBox
    When I enter {"CAD"} in the search field
    And I wait
    And screenshot verified {'empty_category'}    

  @testMethodName: testGoldens
  Scenario: Valid search
    Given The {SymbolsScreen()} app runs with configuration {scenarios: [marketSuccessScenario]}
    When I tap on the DuploSearchBox
    When I enter {"CAD"} in the search field
    And I wait
    Then I see {'AUDCAD'} text
    And screenshot verified {'valid_search'}   

   @testMethodName: testGoldens
   Scenario: Failed search
     Given The {SymbolsScreen()} app runs with configuration {scenarios: [linkedSymbolsFailureScenario]}
     When I tap on the DuploSearchBox
     When I enter {"CAD"} in the search field
     And I wait
     And screenshot verified {'failed_search'}     
 
   @testMethodName: testGoldens
   Scenario: Empty search
     Given The {SymbolsScreen()} app runs with configuration {scenarios: [linkedSymbolsEmptyScenario]}
     When I tap on the DuploSearchBox
     When I enter {"CAD"} in the search field
     And I wait
     And screenshot verified {'empty_search_screen'}   

