import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/usecase/payment_options_deposit_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:user_account/user_account.dart';

part 'deposit_payment_options_bloc.freezed.dart';
part 'deposit_payment_options_event.dart';
part 'deposit_payment_options_state.dart';

class DepositPaymentOptionsBloc
    extends Bloc<DepositPaymentOptionsEvent, DepositPaymentOptionsState> {
  final PaymentOptionsDepositUsecase _paymentOptionsUsecase;
  final PaymentNavigation _paymentNavigation;
  final ClientProfileUseCase _clientProfileUseCase;
  DepositPaymentOptionsBloc({
    required PaymentOptionsDepositUsecase paymentOptionsUsecase,
    required PaymentNavigation paymentNavigation,
    required ClientProfileUseCase clientProfileUseCase,
  }) : _paymentOptionsUsecase = paymentOptionsUsecase,
       _paymentNavigation = paymentNavigation,
       _clientProfileUseCase = clientProfileUseCase,
       super(_DepositPaymentOptionsState()) {
    on<_GetPaymentOptions>(_getPaymentOptions);
    on<_SetDepositFlowConfig>(_setDepositFlowConfig);
    on<_NavigateToSelectAccountAndAmount>(_navigateToSelectAccountAndAmount);
  }

  FutureOr<void> _getPaymentOptions(
    _GetPaymentOptions event,
    Emitter<DepositPaymentOptionsState> emit,
  ) async {
    final clientProfileResult = await _clientProfileUseCase().run();
    ClientProfileData? clientProfileData;
    clientProfileResult.fold(
      (l) {
        print("Error getting client profile $l");
      },
      (r) {
        clientProfileData = r;
      },
    );
    if (clientProfileData == null || clientProfileData?.countryCode == null) {
      emit(
        state.copyWith(
          paymentMethodsProccessState:
              const PaymentMethodsProccessState.error(),
        ),
      );
      addError(Exception('Client profile or Country not found'));
      return;
    }

    final paymentOption =
        await _paymentOptionsUsecase(
          countryCode: clientProfileData!.countryCode!,
        ).run();
    paymentOption.fold(
      (l) {
        emit(
          state.copyWith(
            paymentMethodsProccessState:
                const PaymentMethodsProccessState.error(),
          ),
        );
        addError(l);
      },
      (r) {
        emit(
          state.copyWith(
            paymentMethodsData: r,
            paymentMethodsProccessState:
                const PaymentMethodsProccessState.success(),
          ),
        );
      },
    );
  }

  void _navigateToSelectAccountAndAmount(
    _NavigateToSelectAccountAndAmount event,
    Emitter<DepositPaymentOptionsState> emit,
  ) {
    // Extract polling configuration from the payment methods data
    switch (event.paymentMethod.mop) {
      case DepositMop.card:
      case DepositMop.bridgerpay:
      case DepositMop.apple_pay:
      case DepositMop.google_pay:
        final pollingData = state.paymentMethodsData?.data;
        final maxPollingAttempts = pollingData?.maxPollingTime?.toInt();
        final pollingFrequencySeconds = pollingData?.pollingFrequency?.toInt();
        _paymentNavigation.goToDepositSelectAccountAndAmountScreen(
          event.paymentMethod,
          maxPollingAttempts: maxPollingAttempts,
          pollingFrequencySeconds: pollingFrequencySeconds,
          depositFlowConfig: state.depositFlowConfig!,
        );
        break;
      case DepositMop.bank:
        _paymentNavigation.goToDepositSelectBankScreen(
          event.paymentMethodGroup,
        );
        break;
      default:
        _paymentNavigation.goToPaymentNotAvailableYetPage();
        break;
    }
  }

  FutureOr<void> _setDepositFlowConfig(
    _SetDepositFlowConfig event,
    Emitter<DepositPaymentOptionsState> emit,
  ) {
    emit(state.copyWith(depositFlowConfig: event.depositFlowConfig));
  }
}
