import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hub/src/navigation/hub_navigation.dart';
import 'package:hub/src/navigation/hub_route_schema.dart';
import 'package:user_account/user_account.dart';

part 'product_hub_settings_event.dart';
part 'product_hub_settings_state.dart';
part 'product_hub_settings_bloc.freezed.dart';

class ProductHubSettingsBloc
    extends Bloc<ProductHubSettingsEvent, ProductHubSettingsState> {
  ProductHubSettingsBloc({
    required ClientProfileUseCase clientProfileUseCase,
    required HubNavigation hubNavigation,
  }) : _clientProfileUseCase = clientProfileUseCase,
       _hubNavigation = hubNavigation,
       super(ProductHubSettingsState()) {
    on<ProductHubSettingsEvent>(
      (event, emit) => switch (event) {
        _Started() => _fetchClientProfile(emit),
        _GoToDepositPaymentOptions() => _goToDepositPaymentOptions(),
        _GoToWithdrawPaymentOptions() => _goToWithdrawPaymentOptions(),
        _GoToTransferOptions() => _goToTransferOptions(),
        _GoToGold() => _goToGold(),
        _GoToWealth() => _goToWealth(),
        _GoToAccounts() => _goToAccounts(),
        _GoToWallets() => _goToWallets(),
        _GoToTrading() => _goToTrading(),
        _GoToProfile() => _goToProfile(),
      },
    );
    add(ProductHubSettingsEvent.started());
  }

  final ClientProfileUseCase _clientProfileUseCase;
  final HubNavigation _hubNavigation;

  Future<void> _fetchClientProfile(
    Emitter<ProductHubSettingsState> emit,
  ) async {
    final result = await _clientProfileUseCase().run();
    result.fold(
      (failure) {
        addError(failure);
        print(failure);
        emit(
          state.copyWith(
            processState: ProductHubSettingsProcessState.profileError(),
          ),
        );
      },
      (clientProfile) => emit(
        state.copyWith(
          processState: ProductHubSettingsProcessState.profileSuccess(),
          clientProfile: clientProfile,
        ),
      ),
    );
  }

  void _goToDepositPaymentOptions() {
    _hubNavigation.goToDepositPaymentOptions(
      DepositFlowConfig(
        depositType: DepositType.additional,
        origin: HubRouteSchema.hubSettingsRoute.url,
      ),
    );
  }

  void _goToWithdrawPaymentOptions() {
    _hubNavigation.goToWithdrawPaymentOptions();
  }

  void _goToTransferOptions() {
    _hubNavigation.goToTransferFundsScreen();
  }

  void _goToGold() {
    _hubNavigation.gotoGold();
  }

  void _goToWealth() {
    _hubNavigation.gotoWealth();
  }

  void _goToAccounts() {
    _hubNavigation.gotoBrokerage();
  }

  void _goToWallets() {
    _hubNavigation.gotoBrokerage();
  }

  void _goToTrading() {
    _hubNavigation.goToTrading();
  }

  void _goToProfile() {
    if (state.clientProfile case final clientProfile?)
      _hubNavigation.gotoProfile(clientProfile);
  }
}
