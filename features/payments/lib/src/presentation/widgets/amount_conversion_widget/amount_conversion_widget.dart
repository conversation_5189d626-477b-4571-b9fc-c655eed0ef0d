import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/domain/data/conversion_type.dart';
import 'package:payment/src/domain/data/payment_type.dart';

import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/bloc/amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/components/amount_conversion_loading_state.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/services/amount_validation_service.dart';
import 'package:prelude/prelude.dart';

import 'components/amount_fields.dart';
import 'components/suggested_amounts.dart';

/// Arguments for the AmountConversionWidget
typedef AmountConversionArgs =
    ({
      String transferCurrency,
      String transferCurrencyImage,
      List<CurrencyAmountDetail> currencyAmountDetails,
      List<String> currencies,
      bool showSuggestedAmounts,
      bool isStartWithConversionRate,
      String? targetCurrency,
      String? externalErrorMessage,
      PaymentType paymentType,
      ConversionType conversionType,
    });

/// Callback for amount changes
typedef AmountChangeCallback =
    void Function({
      required String amount,
      required bool isAmountValid,
      required String convertedAmount,
      required RatesModel? ratesModel,
      required ConversionRateModel? conversionRateData,
      String? conversionRateString,
      String? targetCurrency,
    });

/// A reusable widget for amount conversion functionality
class AmountConversionWidget extends StatefulWidget {
  const AmountConversionWidget({
    required this.args,
    required this.onAmountChange,
    super.key,
  });

  final AmountConversionArgs args;
  final AmountChangeCallback onAmountChange;

  @override
  State<AmountConversionWidget> createState() => _AmountConversionWidgetState();
}

class _AmountConversionWidgetState extends State<AmountConversionWidget> {
  late AmountConversionBloc _bloc;
  late TextEditingController transferAmountController;
  late TextEditingController convertedAmountController;

  @override
  void initState() {
    super.initState();
    _bloc = diContainer<AmountConversionBloc>();
    transferAmountController = TextEditingController();
    convertedAmountController = TextEditingController();

    // Initialize currency details
    _bloc.add(
      AmountConversionEvent.getCurrencyDetails(
        currencyAmountDetails: widget.args.currencyAmountDetails,
        accountCurrency: widget.args.transferCurrency,
        targetCurrency: widget.args.targetCurrency,
        isHomeCurrency: true,
        isStartWithConversionRate: widget.args.isStartWithConversionRate,
        paymentType: widget.args.paymentType,
      ),
    );
  }

  @override
  void didUpdateWidget(AmountConversionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if any of the arguments that affect conversion have changed
    if (_hasArgsChanged(oldWidget.args, widget.args)) {
      _bloc.add(
        AmountConversionEvent.getCurrencyDetails(
          currencyAmountDetails: widget.args.currencyAmountDetails,
          accountCurrency: widget.args.transferCurrency,
          targetCurrency: widget.args.targetCurrency,
          isHomeCurrency: true,
          isStartWithConversionRate: widget.args.isStartWithConversionRate,
          paymentType: widget.args.paymentType,
        ),
      );
    }
  }

  @override
  void dispose() {
    transferAmountController.dispose();
    convertedAmountController.dispose();
    _bloc.close();
    super.dispose();
  }

  /// Checks if any arguments that affect conversion have changed
  bool _hasArgsChanged(
    AmountConversionArgs oldArgs,
    AmountConversionArgs newArgs,
  ) {
    return oldArgs.transferCurrency != newArgs.transferCurrency ||
        oldArgs.targetCurrency != newArgs.targetCurrency ||
        oldArgs.isStartWithConversionRate != newArgs.isStartWithConversionRate;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: _AmountConversionContent(
        args: widget.args,
        onAmountChange: widget.onAmountChange,
        transferAmountController: transferAmountController,
        convertedAmountController: convertedAmountController,
      ),
    );
  }
}

class _AmountConversionContent extends StatelessWidget {
  const _AmountConversionContent({
    required this.args,
    required this.onAmountChange,
    required this.transferAmountController,
    required this.convertedAmountController,
  });

  final AmountConversionArgs args;
  final AmountChangeCallback onAmountChange;
  final TextEditingController transferAmountController;
  final TextEditingController convertedAmountController;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<AmountConversionBloc, AmountConversionState>(
      listener: (listenerContext, state) {
        onAmountChange(
          amount:
              args.conversionType.isReversedConversion
                  ? convertedAmountController.text.replaceAll(',', '')
                  : transferAmountController.text.replaceAll(',', ''),
          isAmountValid: state.isValidAmount ?? false,
          convertedAmount:
              args.conversionType.isReversedConversion
                  ? transferAmountController.text.replaceAll(',', '')
                  : convertedAmountController.text.replaceAll(',', ''),
          ratesModel: _getRateModel(
            conversionRateData: state.conversionRateData,
            targetCurrency: state.convertedCurrency,
            accountCurrency: args.transferCurrency,
          ),
          conversionRateData: state.conversionRateData,
          conversionRateString: _getConversionString(
            conversionRateData: state.conversionRateData,
            targetCurrency: state.convertedCurrency,
            accountCurrency: args.transferCurrency,
          ),
          targetCurrency: state.convertedCurrency,
        );
      },
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        switch (state.processState) {
          case AmountConversionLoadingProcessState():
            return AmountConversionLoadingState(
              args: args,
              homeCurrency: state.homeCurrency,
            );
          case AmountConversionErrorProcessState(:final errorMessage):
            return SizedBox(
              height: 200,
              child: Padding(
                padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                child: Center(
                  child: Text(errorMessage ?? 'Error', maxLines: 2),
                ),
              ),
            );
          case AmountConversionSuccessProcessState():
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploText(
                  text: localization.payments_amount,
                  style: duploTextStyles.textLg,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                SizedBox(height: DuploSpacing.spacing_md_8),
                DuploText(
                  text: _getTransactionLimitText(
                    localization,
                    locale,
                    args.currencyAmountDetails,
                    state.convertedCurrency ?? args.transferCurrency,
                  ),
                  style: duploTextStyles.textSm,
                  fontWeight: DuploFontWeight.regular,
                  color: theme.text.textSecondary,
                ),
                SizedBox(height: DuploSpacing.spacing_3xl_24),
                AmountFields(
                  key: const Key('amount_field'),
                  transferAmountController: transferAmountController,
                  convertedAmountController: convertedAmountController,
                  args: args,
                ),
                ((!(state.isValidAmount ?? true) &&
                            state.validationError !=
                                AmountValidationError.none) ||
                        args.externalErrorMessage != null)
                    ? Padding(
                      padding: const EdgeInsets.only(
                        top: DuploSpacing.spacing_sm_6,
                        left: DuploSpacing.spacing_md_8,
                        right: DuploSpacing.spacing_md_8,
                      ),
                      child: DuploText(
                        text:
                            args.externalErrorMessage ??
                            _getValidationErrorMessage(
                              state.validationError,
                              args.currencyAmountDetails,
                              state.convertedCurrency ?? args.transferCurrency,
                              localization,
                            ),
                        style: duploTextStyles.textXs,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textErrorPrimary,
                      ),
                    )
                    : state.doesNeedConversion
                    ? Padding(
                      padding: const EdgeInsets.only(
                        top: DuploSpacing.spacing_sm_6,
                        left: DuploSpacing.spacing_md_8,
                        right: DuploSpacing.spacing_md_8,
                      ),
                      child: DuploText(
                        text: _getConversionString(
                          conversionRateData: state.conversionRateData,
                          targetCurrency: state.convertedCurrency,
                          accountCurrency: args.transferCurrency,
                        ),
                        style: duploTextStyles.textXs,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textTertiary,
                      ),
                    )
                    : Container(),
                if (args.showSuggestedAmounts) ...[
                  SizedBox(height: DuploSpacing.spacing_lg_12),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DuploSpacing.spacing_md_8,
                    ),
                    child: SuggestedAmounts(
                      transferAmountController: transferAmountController,
                      convertedAmountController: convertedAmountController,
                      currencyAmountDetails: args.currencyAmountDetails,
                      targetCurrency:
                          state.convertedCurrency ?? args.transferCurrency,
                      args: args,
                    ),
                  ),
                ],
              ],
            );
        }
      },
    );
  }

  String _getTransactionLimitText(
    EquitiLocalization localization,
    String locale,
    List<CurrencyAmountDetail> currencyAmountDetails,
    String targetOrAccountCurrency,
  ) {
    final targetCurrencyDetail = AmountConversionUtils.getTargetMinAndMaxAmount(
      currencyAmountDetails: currencyAmountDetails,
      targetCurrency: targetOrAccountCurrency,
    );
    final minAmount = targetCurrencyDetail?.minAmount ?? 0;
    final maxAmount = targetCurrencyDetail?.maxAmount ?? 0;

    return '${localization.payments_transactionLimit}(${EquitiFormatter.formatNumber(value: minAmount, locale: locale)} - ${EquitiFormatter.formatNumber(value: maxAmount, locale: locale)}) $targetOrAccountCurrency';
  }

  String _getValidationErrorMessage(
    AmountValidationError validationError,
    List<CurrencyAmountDetail> currencyAmountDetails,
    String targetOrAccountCurrency,
    EquitiLocalization localization,
  ) {
    final targetOrAccountCurrencyDetail =
        AmountConversionUtils.getTargetMinAndMaxAmount(
          currencyAmountDetails: currencyAmountDetails,
          targetCurrency: targetOrAccountCurrency,
        );

    switch (validationError) {
      case AmountValidationError.belowMinimum:
        final minAmount = targetOrAccountCurrencyDetail?.minAmount ?? 0;
        return localization.payments_amount_validation_minimum(
          minAmount.toStringAsFixed(2),
          targetOrAccountCurrencyDetail?.currency ?? targetOrAccountCurrency,
        );
      case AmountValidationError.aboveMaximum:
        final maxAmount = targetOrAccountCurrencyDetail?.maxAmount ?? 0;
        return localization.payments_amount_validation_maximum(
          maxAmount.toStringAsFixed(2),
          targetOrAccountCurrencyDetail?.currency ?? targetOrAccountCurrency,
        );
      case AmountValidationError.none:
        return localization.payments_amount_validation_exceeded_limit;
    }
  }

  String _getConversionString({
    required ConversionRateModel? conversionRateData,
    String? targetCurrency,
    required String accountCurrency,
  }) {
    return args.conversionType.isReversedConversion
        ? AmountConversionUtils.getTargetConversionRateString(
          conversionRateData: conversionRateData,
          targetCurrency: targetCurrency ?? accountCurrency,
        )
        : AmountConversionUtils.getAccountConversionRateString(
          conversionRateData: conversionRateData,
          accountCurrency: accountCurrency,
        );
  }

  RatesModel? _getRateModel({
    ConversionRateModel? conversionRateData,
    String? targetCurrency,
    required String accountCurrency,
  }) {
    return args.conversionType.isReversedConversion
        ? AmountConversionUtils.getTargetCurrencyRate(
          conversionRateData: conversionRateData,
          targetCurrency: targetCurrency ?? accountCurrency,
        )
        : AmountConversionUtils.getAccountCurrencyRate(
          conversionRateData: conversionRateData,
          accountCurrency: accountCurrency,
        );
  }
}
