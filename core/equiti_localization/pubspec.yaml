name: equiti_localization
description: "A new Flutter package project."
version: 0.0.1
publish_to: "none"

environment:
  sdk: 3.8.1
  flutter: 3.32.6

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter 
  intl: 0.20.2
  lokalise_flutter_sdk: 2.0.0
  path: 1.9.1
  watcher: ^1.1.1

dev_dependencies:
  test: 1.26.0
  dependency_validator: 5.0.2
  equiti_lint:
    path: ../../utilities/equiti_lint
  dart_code_metrics_presets: 2.22.0

dependency_overrides:
  analyzer: ^6.3.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
