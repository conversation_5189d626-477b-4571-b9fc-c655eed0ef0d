// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_payment_options_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositPaymentOptionsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentOptionsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositPaymentOptionsEvent()';
}


}

/// @nodoc
class $DepositPaymentOptionsEventCopyWith<$Res>  {
$DepositPaymentOptionsEventCopyWith(DepositPaymentOptionsEvent _, $Res Function(DepositPaymentOptionsEvent) __);
}


/// @nodoc


class _GetPaymentOptions implements DepositPaymentOptionsEvent {
  const _GetPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositPaymentOptionsEvent.getPaymentOptions()';
}


}




/// @nodoc


class _SetDepositFlowConfig implements DepositPaymentOptionsEvent {
  const _SetDepositFlowConfig({required this.depositFlowConfig});
  

 final  DepositFlowConfig depositFlowConfig;

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetDepositFlowConfigCopyWith<_SetDepositFlowConfig> get copyWith => __$SetDepositFlowConfigCopyWithImpl<_SetDepositFlowConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetDepositFlowConfig&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig));
}


@override
int get hashCode => Object.hash(runtimeType,depositFlowConfig);

@override
String toString() {
  return 'DepositPaymentOptionsEvent.setDepositFlowConfig(depositFlowConfig: $depositFlowConfig)';
}


}

/// @nodoc
abstract mixin class _$SetDepositFlowConfigCopyWith<$Res> implements $DepositPaymentOptionsEventCopyWith<$Res> {
  factory _$SetDepositFlowConfigCopyWith(_SetDepositFlowConfig value, $Res Function(_SetDepositFlowConfig) _then) = __$SetDepositFlowConfigCopyWithImpl;
@useResult
$Res call({
 DepositFlowConfig depositFlowConfig
});


$DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class __$SetDepositFlowConfigCopyWithImpl<$Res>
    implements _$SetDepositFlowConfigCopyWith<$Res> {
  __$SetDepositFlowConfigCopyWithImpl(this._self, this._then);

  final _SetDepositFlowConfig _self;
  final $Res Function(_SetDepositFlowConfig) _then;

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? depositFlowConfig = null,}) {
  return _then(_SetDepositFlowConfig(
depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,
  ));
}

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

/// @nodoc


class _NavigateToSelectAccountAndAmount implements DepositPaymentOptionsEvent {
  const _NavigateToSelectAccountAndAmount({required this.paymentMethodGroup, required this.paymentMethod});
  

 final  DepositPaymentMethodGroup paymentMethodGroup;
 final  DepositPaymentMethod paymentMethod;

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NavigateToSelectAccountAndAmountCopyWith<_NavigateToSelectAccountAndAmount> get copyWith => __$NavigateToSelectAccountAndAmountCopyWithImpl<_NavigateToSelectAccountAndAmount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToSelectAccountAndAmount&&(identical(other.paymentMethodGroup, paymentMethodGroup) || other.paymentMethodGroup == paymentMethodGroup)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethodGroup,paymentMethod);

@override
String toString() {
  return 'DepositPaymentOptionsEvent.navigateToSelectAccountAndAmount(paymentMethodGroup: $paymentMethodGroup, paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class _$NavigateToSelectAccountAndAmountCopyWith<$Res> implements $DepositPaymentOptionsEventCopyWith<$Res> {
  factory _$NavigateToSelectAccountAndAmountCopyWith(_NavigateToSelectAccountAndAmount value, $Res Function(_NavigateToSelectAccountAndAmount) _then) = __$NavigateToSelectAccountAndAmountCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethodGroup paymentMethodGroup, DepositPaymentMethod paymentMethod
});


$DepositPaymentMethodGroupCopyWith<$Res> get paymentMethodGroup;$DepositPaymentMethodCopyWith<$Res> get paymentMethod;

}
/// @nodoc
class __$NavigateToSelectAccountAndAmountCopyWithImpl<$Res>
    implements _$NavigateToSelectAccountAndAmountCopyWith<$Res> {
  __$NavigateToSelectAccountAndAmountCopyWithImpl(this._self, this._then);

  final _NavigateToSelectAccountAndAmount _self;
  final $Res Function(_NavigateToSelectAccountAndAmount) _then;

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentMethodGroup = null,Object? paymentMethod = null,}) {
  return _then(_NavigateToSelectAccountAndAmount(
paymentMethodGroup: null == paymentMethodGroup ? _self.paymentMethodGroup : paymentMethodGroup // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodGroup,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,
  ));
}

/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodGroupCopyWith<$Res> get paymentMethodGroup {
  
  return $DepositPaymentMethodGroupCopyWith<$Res>(_self.paymentMethodGroup, (value) {
    return _then(_self.copyWith(paymentMethodGroup: value));
  });
}/// Create a copy of DepositPaymentOptionsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get paymentMethod {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.paymentMethod, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}
}

/// @nodoc
mixin _$DepositPaymentOptionsState {

 DepositPaymentMethodsModel? get paymentMethodsData; PaymentMethodsProccessState get paymentMethodsProccessState; DepositFlowConfig? get depositFlowConfig;
/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentOptionsStateCopyWith<DepositPaymentOptionsState> get copyWith => _$DepositPaymentOptionsStateCopyWithImpl<DepositPaymentOptionsState>(this as DepositPaymentOptionsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentOptionsState&&(identical(other.paymentMethodsData, paymentMethodsData) || other.paymentMethodsData == paymentMethodsData)&&(identical(other.paymentMethodsProccessState, paymentMethodsProccessState) || other.paymentMethodsProccessState == paymentMethodsProccessState)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethodsData,paymentMethodsProccessState,depositFlowConfig);

@override
String toString() {
  return 'DepositPaymentOptionsState(paymentMethodsData: $paymentMethodsData, paymentMethodsProccessState: $paymentMethodsProccessState, depositFlowConfig: $depositFlowConfig)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentOptionsStateCopyWith<$Res>  {
  factory $DepositPaymentOptionsStateCopyWith(DepositPaymentOptionsState value, $Res Function(DepositPaymentOptionsState) _then) = _$DepositPaymentOptionsStateCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethodsModel? paymentMethodsData, PaymentMethodsProccessState paymentMethodsProccessState, DepositFlowConfig? depositFlowConfig
});


$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData;$PaymentMethodsProccessStateCopyWith<$Res> get paymentMethodsProccessState;$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;

}
/// @nodoc
class _$DepositPaymentOptionsStateCopyWithImpl<$Res>
    implements $DepositPaymentOptionsStateCopyWith<$Res> {
  _$DepositPaymentOptionsStateCopyWithImpl(this._self, this._then);

  final DepositPaymentOptionsState _self;
  final $Res Function(DepositPaymentOptionsState) _then;

/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentMethodsData = freezed,Object? paymentMethodsProccessState = null,Object? depositFlowConfig = freezed,}) {
  return _then(_self.copyWith(
paymentMethodsData: freezed == paymentMethodsData ? _self.paymentMethodsData : paymentMethodsData // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsModel?,paymentMethodsProccessState: null == paymentMethodsProccessState ? _self.paymentMethodsProccessState : paymentMethodsProccessState // ignore: cast_nullable_to_non_nullable
as PaymentMethodsProccessState,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,
  ));
}
/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData {
    if (_self.paymentMethodsData == null) {
    return null;
  }

  return $DepositPaymentMethodsModelCopyWith<$Res>(_self.paymentMethodsData!, (value) {
    return _then(_self.copyWith(paymentMethodsData: value));
  });
}/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentMethodsProccessStateCopyWith<$Res> get paymentMethodsProccessState {
  
  return $PaymentMethodsProccessStateCopyWith<$Res>(_self.paymentMethodsProccessState, (value) {
    return _then(_self.copyWith(paymentMethodsProccessState: value));
  });
}/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}


/// @nodoc


class _DepositPaymentOptionsState implements DepositPaymentOptionsState {
  const _DepositPaymentOptionsState({this.paymentMethodsData, this.paymentMethodsProccessState = const PaymentMethodsProccessState.loading(), this.depositFlowConfig});
  

@override final  DepositPaymentMethodsModel? paymentMethodsData;
@override@JsonKey() final  PaymentMethodsProccessState paymentMethodsProccessState;
@override final  DepositFlowConfig? depositFlowConfig;

/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentOptionsStateCopyWith<_DepositPaymentOptionsState> get copyWith => __$DepositPaymentOptionsStateCopyWithImpl<_DepositPaymentOptionsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentOptionsState&&(identical(other.paymentMethodsData, paymentMethodsData) || other.paymentMethodsData == paymentMethodsData)&&(identical(other.paymentMethodsProccessState, paymentMethodsProccessState) || other.paymentMethodsProccessState == paymentMethodsProccessState)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethodsData,paymentMethodsProccessState,depositFlowConfig);

@override
String toString() {
  return 'DepositPaymentOptionsState(paymentMethodsData: $paymentMethodsData, paymentMethodsProccessState: $paymentMethodsProccessState, depositFlowConfig: $depositFlowConfig)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentOptionsStateCopyWith<$Res> implements $DepositPaymentOptionsStateCopyWith<$Res> {
  factory _$DepositPaymentOptionsStateCopyWith(_DepositPaymentOptionsState value, $Res Function(_DepositPaymentOptionsState) _then) = __$DepositPaymentOptionsStateCopyWithImpl;
@override @useResult
$Res call({
 DepositPaymentMethodsModel? paymentMethodsData, PaymentMethodsProccessState paymentMethodsProccessState, DepositFlowConfig? depositFlowConfig
});


@override $DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData;@override $PaymentMethodsProccessStateCopyWith<$Res> get paymentMethodsProccessState;@override $DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;

}
/// @nodoc
class __$DepositPaymentOptionsStateCopyWithImpl<$Res>
    implements _$DepositPaymentOptionsStateCopyWith<$Res> {
  __$DepositPaymentOptionsStateCopyWithImpl(this._self, this._then);

  final _DepositPaymentOptionsState _self;
  final $Res Function(_DepositPaymentOptionsState) _then;

/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentMethodsData = freezed,Object? paymentMethodsProccessState = null,Object? depositFlowConfig = freezed,}) {
  return _then(_DepositPaymentOptionsState(
paymentMethodsData: freezed == paymentMethodsData ? _self.paymentMethodsData : paymentMethodsData // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsModel?,paymentMethodsProccessState: null == paymentMethodsProccessState ? _self.paymentMethodsProccessState : paymentMethodsProccessState // ignore: cast_nullable_to_non_nullable
as PaymentMethodsProccessState,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,
  ));
}

/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData {
    if (_self.paymentMethodsData == null) {
    return null;
  }

  return $DepositPaymentMethodsModelCopyWith<$Res>(_self.paymentMethodsData!, (value) {
    return _then(_self.copyWith(paymentMethodsData: value));
  });
}/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentMethodsProccessStateCopyWith<$Res> get paymentMethodsProccessState {
  
  return $PaymentMethodsProccessStateCopyWith<$Res>(_self.paymentMethodsProccessState, (value) {
    return _then(_self.copyWith(paymentMethodsProccessState: value));
  });
}/// Create a copy of DepositPaymentOptionsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

/// @nodoc
mixin _$PaymentMethodsProccessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodsProccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentMethodsProccessState()';
}


}

/// @nodoc
class $PaymentMethodsProccessStateCopyWith<$Res>  {
$PaymentMethodsProccessStateCopyWith(PaymentMethodsProccessState _, $Res Function(PaymentMethodsProccessState) __);
}


/// @nodoc


class PaymentMethodsProccessStateLoading implements PaymentMethodsProccessState {
  const PaymentMethodsProccessStateLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodsProccessStateLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentMethodsProccessState.loading()';
}


}




/// @nodoc


class PaymentMethodsProccessStateSuccess implements PaymentMethodsProccessState {
  const PaymentMethodsProccessStateSuccess();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodsProccessStateSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentMethodsProccessState.success()';
}


}




/// @nodoc


class PaymentMethodsProccessStateError implements PaymentMethodsProccessState {
  const PaymentMethodsProccessStateError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodsProccessStateError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentMethodsProccessState.error()';
}


}




// dart format on
