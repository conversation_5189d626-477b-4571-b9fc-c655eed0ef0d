import 'package:equiti_test/equiti_test.dart';
import 'scenarios/reset_balance_success_scenario.dart';
import 'scenarios/reset_balance_failure_scenario.dart';
import 'package:e_trader/src/presentation/reset_balance/reset_balance_screen.dart';

Feature: Reset Balance
    
    @testMethodName: testGoldens
    Scenario: Screenshot reset balance screen
        Given The {ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD',)} app is rendered
        I enter {"100"} in the {"reset_balance_amount_field"} text field
        Then screenshot verified {'reset_balance'}

    @testMethodName: testGoldens
    Scenario: Screenshot reset balance add amount
        Given The {ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD',)} app is rendered
        Then I enter {"100"} in the {"reset_balance_amount_field"} text field
        Then screenshot verified {'reset_balance_field_filled'}


    @testMethodName: testGoldens
    Scenario: Screenshot reset balance button tapped
        Given The {ResetBalanceScreen(accountNumber: '12345', accountCurrency: 'USD',)} app is rendered
        Then I enter {"100"} in the {"reset_balance_amount_field"} text field
        Then I tap {"reset_balance_button"} identifier
        Then screenshot verified {'reset_balance_button_tapped'}