part of 'change_leverage_bloc.dart';

@freezed
sealed class ChangeLeverageState with _$ChangeLeverageState {
  factory ChangeLeverageState({
    @Default(ChangeLeverageProcessState.loading())
    ChangeLeverageProcessState processState,
    @Default(ChangeLeverageProcessState.initial())
    ChangeLeverageProcessState changeLeverage,
    @Default([]) List<int> leverages,
    int? selectedLeverage,
  }) = _ChangeLeverageState;
}

@freezed
sealed class ChangeLeverageProcessState with _$ChangeLeverageProcessState {
  const factory ChangeLeverageProcessState.initial() = ChangeLeverageInitial;
  const factory ChangeLeverageProcessState.loading() = ChangeLeverageLoading;
  const factory ChangeLeverageProcessState.success() = ChangeLeverageSuccess;
  const factory ChangeLeverageProcessState.error({required String message}) =
      ChangeLeverageError;
}
