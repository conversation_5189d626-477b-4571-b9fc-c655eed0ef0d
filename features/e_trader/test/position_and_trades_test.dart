// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';

import 'position_and_trades_test_data.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/order_list_tile.dart';
import 'package:e_trader/src/domain/model/entry_order_type.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Position and Trades''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Position Header Collapsed''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Position Header Collapsed''');
        await theAppIsRendered(tester, PositionAndTradesTestData());
        await iWait(tester);
        await screenshotVerified(tester, 'position_tile_colapsed');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Position Header Collapsed''', success);
      }
    });
    testGoldens('''Position header Expanded''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Position header Expanded''');
        await theAppIsRendered(tester, PositionAndTradesTestData());
        await iTapIdentifier(tester, "hedged_button_positions");
        await iWait(tester);
        await screenshotVerified(tester, 'position_tile_expanded');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Position header Expanded''', success);
      }
    });
    testGoldens('''Position Header Single''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Position Header Single''');
        await theAppIsRendered(tester, PositionHeaderWidget());
        await screenshotVerified(tester, 'position_header_single');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Position Header Single''', success);
      }
    });
    testGoldens('''Trade Tile Single''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Trade Tile Single''');
        await theAppIsRendered(tester, TradeTileWidget());
        await screenshotVerified(tester, 'trade_tile_signle');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Trade Tile Single''', success);
      }
    });
    testGoldens('''Order list tile''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Order list tile''');
        await theAppIsRendered(
          tester,
          OrderListTile(
            slValue: 0.03,
            tpValue: 0.02,
            digits: 5,
            orderPrice: 099.987,
            lots: 0.04,
            currentPrice: 101.064,
            tradeType: TradeType.buy,
            productIconURL: '',
            productName: 'EURUSD',
            entryOrderType: EntryOrderType.buyLimit,
            priceChange: 12.23,
          ),
        );
        await screenshotVerified(tester, 'order_list_tile');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Order list tile''', success);
      }
    });
  });
}
