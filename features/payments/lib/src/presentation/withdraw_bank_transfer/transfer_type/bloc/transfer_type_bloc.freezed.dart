// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_type_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TransferTypeEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferTypeEvent()';
}


}

/// @nodoc
class $TransferTypeEventCopyWith<$Res>  {
$TransferTypeEventCopyWith(TransferTypeEvent _, $Res Function(TransferTypeEvent) __);
}


/// @nodoc


class _OnChangeTransferType implements TransferTypeEvent {
  const _OnChangeTransferType(this.transferType);
  

 final  String transferType;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnChangeTransferTypeCopyWith<_OnChangeTransferType> get copyWith => __$OnChangeTransferTypeCopyWithImpl<_OnChangeTransferType>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChangeTransferType&&(identical(other.transferType, transferType) || other.transferType == transferType));
}


@override
int get hashCode => Object.hash(runtimeType,transferType);

@override
String toString() {
  return 'TransferTypeEvent.onChangeTransferType(transferType: $transferType)';
}


}

/// @nodoc
abstract mixin class _$OnChangeTransferTypeCopyWith<$Res> implements $TransferTypeEventCopyWith<$Res> {
  factory _$OnChangeTransferTypeCopyWith(_OnChangeTransferType value, $Res Function(_OnChangeTransferType) _then) = __$OnChangeTransferTypeCopyWithImpl;
@useResult
$Res call({
 String transferType
});




}
/// @nodoc
class __$OnChangeTransferTypeCopyWithImpl<$Res>
    implements _$OnChangeTransferTypeCopyWith<$Res> {
  __$OnChangeTransferTypeCopyWithImpl(this._self, this._then);

  final _OnChangeTransferType _self;
  final $Res Function(_OnChangeTransferType) _then;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? transferType = null,}) {
  return _then(_OnChangeTransferType(
null == transferType ? _self.transferType : transferType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnWithdrawalRequest implements TransferTypeEvent {
  const _OnWithdrawalRequest(this.amountModel, this.bankAccount);
  

 final  WithdrawFlowParams amountModel;
 final  BankAccount bankAccount;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnWithdrawalRequestCopyWith<_OnWithdrawalRequest> get copyWith => __$OnWithdrawalRequestCopyWithImpl<_OnWithdrawalRequest>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnWithdrawalRequest&&(identical(other.amountModel, amountModel) || other.amountModel == amountModel)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount));
}


@override
int get hashCode => Object.hash(runtimeType,amountModel,bankAccount);

@override
String toString() {
  return 'TransferTypeEvent.onWithdrawalRequest(amountModel: $amountModel, bankAccount: $bankAccount)';
}


}

/// @nodoc
abstract mixin class _$OnWithdrawalRequestCopyWith<$Res> implements $TransferTypeEventCopyWith<$Res> {
  factory _$OnWithdrawalRequestCopyWith(_OnWithdrawalRequest value, $Res Function(_OnWithdrawalRequest) _then) = __$OnWithdrawalRequestCopyWithImpl;
@useResult
$Res call({
 WithdrawFlowParams amountModel, BankAccount bankAccount
});


$WithdrawFlowParamsCopyWith<$Res> get amountModel;$BankAccountCopyWith<$Res> get bankAccount;

}
/// @nodoc
class __$OnWithdrawalRequestCopyWithImpl<$Res>
    implements _$OnWithdrawalRequestCopyWith<$Res> {
  __$OnWithdrawalRequestCopyWithImpl(this._self, this._then);

  final _OnWithdrawalRequest _self;
  final $Res Function(_OnWithdrawalRequest) _then;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? amountModel = null,Object? bankAccount = null,}) {
  return _then(_OnWithdrawalRequest(
null == amountModel ? _self.amountModel : amountModel // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,null == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as BankAccount,
  ));
}

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get amountModel {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.amountModel, (value) {
    return _then(_self.copyWith(amountModel: value));
  });
}/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BankAccountCopyWith<$Res> get bankAccount {
  
  return $BankAccountCopyWith<$Res>(_self.bankAccount, (value) {
    return _then(_self.copyWith(bankAccount: value));
  });
}
}

/// @nodoc


class _OnRequestClientProfileData implements TransferTypeEvent {
  const _OnRequestClientProfileData();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnRequestClientProfileData);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferTypeEvent.onRequestClientProfileData()';
}


}




/// @nodoc


class _ResetProcessState implements TransferTypeEvent {
  const _ResetProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResetProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferTypeEvent.resetProcessState()';
}


}




/// @nodoc


class _OnFeesChanged implements TransferTypeEvent {
  const _OnFeesChanged(this.fees, this.amountModel);
  

 final  num fees;
 final  WithdrawFlowParams amountModel;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnFeesChangedCopyWith<_OnFeesChanged> get copyWith => __$OnFeesChangedCopyWithImpl<_OnFeesChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnFeesChanged&&(identical(other.fees, fees) || other.fees == fees)&&(identical(other.amountModel, amountModel) || other.amountModel == amountModel));
}


@override
int get hashCode => Object.hash(runtimeType,fees,amountModel);

@override
String toString() {
  return 'TransferTypeEvent.onFeesChanged(fees: $fees, amountModel: $amountModel)';
}


}

/// @nodoc
abstract mixin class _$OnFeesChangedCopyWith<$Res> implements $TransferTypeEventCopyWith<$Res> {
  factory _$OnFeesChangedCopyWith(_OnFeesChanged value, $Res Function(_OnFeesChanged) _then) = __$OnFeesChangedCopyWithImpl;
@useResult
$Res call({
 num fees, WithdrawFlowParams amountModel
});


$WithdrawFlowParamsCopyWith<$Res> get amountModel;

}
/// @nodoc
class __$OnFeesChangedCopyWithImpl<$Res>
    implements _$OnFeesChangedCopyWith<$Res> {
  __$OnFeesChangedCopyWithImpl(this._self, this._then);

  final _OnFeesChanged _self;
  final $Res Function(_OnFeesChanged) _then;

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? fees = null,Object? amountModel = null,}) {
  return _then(_OnFeesChanged(
null == fees ? _self.fees : fees // ignore: cast_nullable_to_non_nullable
as num,null == amountModel ? _self.amountModel : amountModel // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of TransferTypeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get amountModel {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.amountModel, (value) {
    return _then(_self.copyWith(amountModel: value));
  });
}
}

/// @nodoc
mixin _$TransferTypeState {

 String? get transferType; String? get clientAccountId; TransferTypeProcessState get processState; bool get isRequestWithdrawButtonLoading; bool get hasInsufficientFunds;// Add validation fields
 String get insufficientFundsMessage; num? get withdrawalFees;// Store current fees for validation
// Store validation data for localized error message generation
 num? get validationWithdrawalAmount; num? get validationAccountBalance; num? get validationWithdrawalFees; String? get validationCurrency;
/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferTypeStateCopyWith<TransferTypeState> get copyWith => _$TransferTypeStateCopyWithImpl<TransferTypeState>(this as TransferTypeState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeState&&(identical(other.transferType, transferType) || other.transferType == transferType)&&(identical(other.clientAccountId, clientAccountId) || other.clientAccountId == clientAccountId)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.isRequestWithdrawButtonLoading, isRequestWithdrawButtonLoading) || other.isRequestWithdrawButtonLoading == isRequestWithdrawButtonLoading)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.withdrawalFees, withdrawalFees) || other.withdrawalFees == withdrawalFees)&&(identical(other.validationWithdrawalAmount, validationWithdrawalAmount) || other.validationWithdrawalAmount == validationWithdrawalAmount)&&(identical(other.validationAccountBalance, validationAccountBalance) || other.validationAccountBalance == validationAccountBalance)&&(identical(other.validationWithdrawalFees, validationWithdrawalFees) || other.validationWithdrawalFees == validationWithdrawalFees)&&(identical(other.validationCurrency, validationCurrency) || other.validationCurrency == validationCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,transferType,clientAccountId,processState,isRequestWithdrawButtonLoading,hasInsufficientFunds,insufficientFundsMessage,withdrawalFees,validationWithdrawalAmount,validationAccountBalance,validationWithdrawalFees,validationCurrency);

@override
String toString() {
  return 'TransferTypeState(transferType: $transferType, clientAccountId: $clientAccountId, processState: $processState, isRequestWithdrawButtonLoading: $isRequestWithdrawButtonLoading, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, withdrawalFees: $withdrawalFees, validationWithdrawalAmount: $validationWithdrawalAmount, validationAccountBalance: $validationAccountBalance, validationWithdrawalFees: $validationWithdrawalFees, validationCurrency: $validationCurrency)';
}


}

/// @nodoc
abstract mixin class $TransferTypeStateCopyWith<$Res>  {
  factory $TransferTypeStateCopyWith(TransferTypeState value, $Res Function(TransferTypeState) _then) = _$TransferTypeStateCopyWithImpl;
@useResult
$Res call({
 String? transferType, String? clientAccountId, TransferTypeProcessState processState, bool isRequestWithdrawButtonLoading, bool hasInsufficientFunds, String insufficientFundsMessage, num? withdrawalFees, num? validationWithdrawalAmount, num? validationAccountBalance, num? validationWithdrawalFees, String? validationCurrency
});


$TransferTypeProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$TransferTypeStateCopyWithImpl<$Res>
    implements $TransferTypeStateCopyWith<$Res> {
  _$TransferTypeStateCopyWithImpl(this._self, this._then);

  final TransferTypeState _self;
  final $Res Function(TransferTypeState) _then;

/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? transferType = freezed,Object? clientAccountId = freezed,Object? processState = null,Object? isRequestWithdrawButtonLoading = null,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? withdrawalFees = freezed,Object? validationWithdrawalAmount = freezed,Object? validationAccountBalance = freezed,Object? validationWithdrawalFees = freezed,Object? validationCurrency = freezed,}) {
  return _then(_self.copyWith(
transferType: freezed == transferType ? _self.transferType : transferType // ignore: cast_nullable_to_non_nullable
as String?,clientAccountId: freezed == clientAccountId ? _self.clientAccountId : clientAccountId // ignore: cast_nullable_to_non_nullable
as String?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TransferTypeProcessState,isRequestWithdrawButtonLoading: null == isRequestWithdrawButtonLoading ? _self.isRequestWithdrawButtonLoading : isRequestWithdrawButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,withdrawalFees: freezed == withdrawalFees ? _self.withdrawalFees : withdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalAmount: freezed == validationWithdrawalAmount ? _self.validationWithdrawalAmount : validationWithdrawalAmount // ignore: cast_nullable_to_non_nullable
as num?,validationAccountBalance: freezed == validationAccountBalance ? _self.validationAccountBalance : validationAccountBalance // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalFees: freezed == validationWithdrawalFees ? _self.validationWithdrawalFees : validationWithdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationCurrency: freezed == validationCurrency ? _self.validationCurrency : validationCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransferTypeProcessStateCopyWith<$Res> get processState {
  
  return $TransferTypeProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _TransferTypeState implements TransferTypeState {
  const _TransferTypeState({this.transferType, this.clientAccountId, this.processState = const TransferTypeProcessState.loaded(), this.isRequestWithdrawButtonLoading = false, this.hasInsufficientFunds = false, this.insufficientFundsMessage = '', this.withdrawalFees, this.validationWithdrawalAmount, this.validationAccountBalance, this.validationWithdrawalFees, this.validationCurrency});
  

@override final  String? transferType;
@override final  String? clientAccountId;
@override@JsonKey() final  TransferTypeProcessState processState;
@override@JsonKey() final  bool isRequestWithdrawButtonLoading;
@override@JsonKey() final  bool hasInsufficientFunds;
// Add validation fields
@override@JsonKey() final  String insufficientFundsMessage;
@override final  num? withdrawalFees;
// Store current fees for validation
// Store validation data for localized error message generation
@override final  num? validationWithdrawalAmount;
@override final  num? validationAccountBalance;
@override final  num? validationWithdrawalFees;
@override final  String? validationCurrency;

/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransferTypeStateCopyWith<_TransferTypeState> get copyWith => __$TransferTypeStateCopyWithImpl<_TransferTypeState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransferTypeState&&(identical(other.transferType, transferType) || other.transferType == transferType)&&(identical(other.clientAccountId, clientAccountId) || other.clientAccountId == clientAccountId)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.isRequestWithdrawButtonLoading, isRequestWithdrawButtonLoading) || other.isRequestWithdrawButtonLoading == isRequestWithdrawButtonLoading)&&(identical(other.hasInsufficientFunds, hasInsufficientFunds) || other.hasInsufficientFunds == hasInsufficientFunds)&&(identical(other.insufficientFundsMessage, insufficientFundsMessage) || other.insufficientFundsMessage == insufficientFundsMessage)&&(identical(other.withdrawalFees, withdrawalFees) || other.withdrawalFees == withdrawalFees)&&(identical(other.validationWithdrawalAmount, validationWithdrawalAmount) || other.validationWithdrawalAmount == validationWithdrawalAmount)&&(identical(other.validationAccountBalance, validationAccountBalance) || other.validationAccountBalance == validationAccountBalance)&&(identical(other.validationWithdrawalFees, validationWithdrawalFees) || other.validationWithdrawalFees == validationWithdrawalFees)&&(identical(other.validationCurrency, validationCurrency) || other.validationCurrency == validationCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,transferType,clientAccountId,processState,isRequestWithdrawButtonLoading,hasInsufficientFunds,insufficientFundsMessage,withdrawalFees,validationWithdrawalAmount,validationAccountBalance,validationWithdrawalFees,validationCurrency);

@override
String toString() {
  return 'TransferTypeState(transferType: $transferType, clientAccountId: $clientAccountId, processState: $processState, isRequestWithdrawButtonLoading: $isRequestWithdrawButtonLoading, hasInsufficientFunds: $hasInsufficientFunds, insufficientFundsMessage: $insufficientFundsMessage, withdrawalFees: $withdrawalFees, validationWithdrawalAmount: $validationWithdrawalAmount, validationAccountBalance: $validationAccountBalance, validationWithdrawalFees: $validationWithdrawalFees, validationCurrency: $validationCurrency)';
}


}

/// @nodoc
abstract mixin class _$TransferTypeStateCopyWith<$Res> implements $TransferTypeStateCopyWith<$Res> {
  factory _$TransferTypeStateCopyWith(_TransferTypeState value, $Res Function(_TransferTypeState) _then) = __$TransferTypeStateCopyWithImpl;
@override @useResult
$Res call({
 String? transferType, String? clientAccountId, TransferTypeProcessState processState, bool isRequestWithdrawButtonLoading, bool hasInsufficientFunds, String insufficientFundsMessage, num? withdrawalFees, num? validationWithdrawalAmount, num? validationAccountBalance, num? validationWithdrawalFees, String? validationCurrency
});


@override $TransferTypeProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$TransferTypeStateCopyWithImpl<$Res>
    implements _$TransferTypeStateCopyWith<$Res> {
  __$TransferTypeStateCopyWithImpl(this._self, this._then);

  final _TransferTypeState _self;
  final $Res Function(_TransferTypeState) _then;

/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? transferType = freezed,Object? clientAccountId = freezed,Object? processState = null,Object? isRequestWithdrawButtonLoading = null,Object? hasInsufficientFunds = null,Object? insufficientFundsMessage = null,Object? withdrawalFees = freezed,Object? validationWithdrawalAmount = freezed,Object? validationAccountBalance = freezed,Object? validationWithdrawalFees = freezed,Object? validationCurrency = freezed,}) {
  return _then(_TransferTypeState(
transferType: freezed == transferType ? _self.transferType : transferType // ignore: cast_nullable_to_non_nullable
as String?,clientAccountId: freezed == clientAccountId ? _self.clientAccountId : clientAccountId // ignore: cast_nullable_to_non_nullable
as String?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TransferTypeProcessState,isRequestWithdrawButtonLoading: null == isRequestWithdrawButtonLoading ? _self.isRequestWithdrawButtonLoading : isRequestWithdrawButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,hasInsufficientFunds: null == hasInsufficientFunds ? _self.hasInsufficientFunds : hasInsufficientFunds // ignore: cast_nullable_to_non_nullable
as bool,insufficientFundsMessage: null == insufficientFundsMessage ? _self.insufficientFundsMessage : insufficientFundsMessage // ignore: cast_nullable_to_non_nullable
as String,withdrawalFees: freezed == withdrawalFees ? _self.withdrawalFees : withdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalAmount: freezed == validationWithdrawalAmount ? _self.validationWithdrawalAmount : validationWithdrawalAmount // ignore: cast_nullable_to_non_nullable
as num?,validationAccountBalance: freezed == validationAccountBalance ? _self.validationAccountBalance : validationAccountBalance // ignore: cast_nullable_to_non_nullable
as num?,validationWithdrawalFees: freezed == validationWithdrawalFees ? _self.validationWithdrawalFees : validationWithdrawalFees // ignore: cast_nullable_to_non_nullable
as num?,validationCurrency: freezed == validationCurrency ? _self.validationCurrency : validationCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of TransferTypeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransferTypeProcessStateCopyWith<$Res> get processState {
  
  return $TransferTypeProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$TransferTypeProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferTypeProcessState()';
}


}

/// @nodoc
class $TransferTypeProcessStateCopyWith<$Res>  {
$TransferTypeProcessStateCopyWith(TransferTypeProcessState _, $Res Function(TransferTypeProcessState) __);
}


/// @nodoc


class TransferTypeLoadedState implements TransferTypeProcessState {
  const TransferTypeLoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeLoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferTypeProcessState.loaded()';
}


}




/// @nodoc


class TransferTypeSuccessState implements TransferTypeProcessState {
  const TransferTypeSuccessState({required this.status, this.operationId});
  

 final  WithdrawStatusType status;
 final  String? operationId;

/// Create a copy of TransferTypeProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferTypeSuccessStateCopyWith<TransferTypeSuccessState> get copyWith => _$TransferTypeSuccessStateCopyWithImpl<TransferTypeSuccessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeSuccessState&&(identical(other.status, status) || other.status == status)&&(identical(other.operationId, operationId) || other.operationId == operationId));
}


@override
int get hashCode => Object.hash(runtimeType,status,operationId);

@override
String toString() {
  return 'TransferTypeProcessState.success(status: $status, operationId: $operationId)';
}


}

/// @nodoc
abstract mixin class $TransferTypeSuccessStateCopyWith<$Res> implements $TransferTypeProcessStateCopyWith<$Res> {
  factory $TransferTypeSuccessStateCopyWith(TransferTypeSuccessState value, $Res Function(TransferTypeSuccessState) _then) = _$TransferTypeSuccessStateCopyWithImpl;
@useResult
$Res call({
 WithdrawStatusType status, String? operationId
});




}
/// @nodoc
class _$TransferTypeSuccessStateCopyWithImpl<$Res>
    implements $TransferTypeSuccessStateCopyWith<$Res> {
  _$TransferTypeSuccessStateCopyWithImpl(this._self, this._then);

  final TransferTypeSuccessState _self;
  final $Res Function(TransferTypeSuccessState) _then;

/// Create a copy of TransferTypeProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? status = null,Object? operationId = freezed,}) {
  return _then(TransferTypeSuccessState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as WithdrawStatusType,operationId: freezed == operationId ? _self.operationId : operationId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class TransferTypeErrorState implements TransferTypeProcessState {
  const TransferTypeErrorState({required this.message});
  

 final  String message;

/// Create a copy of TransferTypeProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferTypeErrorStateCopyWith<TransferTypeErrorState> get copyWith => _$TransferTypeErrorStateCopyWithImpl<TransferTypeErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferTypeErrorState&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'TransferTypeProcessState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $TransferTypeErrorStateCopyWith<$Res> implements $TransferTypeProcessStateCopyWith<$Res> {
  factory $TransferTypeErrorStateCopyWith(TransferTypeErrorState value, $Res Function(TransferTypeErrorState) _then) = _$TransferTypeErrorStateCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$TransferTypeErrorStateCopyWithImpl<$Res>
    implements $TransferTypeErrorStateCopyWith<$Res> {
  _$TransferTypeErrorStateCopyWithImpl(this._self, this._then);

  final TransferTypeErrorState _self;
  final $Res Function(TransferTypeErrorState) _then;

/// Create a copy of TransferTypeProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(TransferTypeErrorState(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
