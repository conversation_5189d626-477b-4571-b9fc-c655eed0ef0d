// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'modify_pending_order_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ModifyPendingOrderEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderEvent()';
}


}

/// @nodoc
class $ModifyPendingOrderEventCopyWith<$Res>  {
$ModifyPendingOrderEventCopyWith(ModifyPendingOrderEvent _, $Res Function(ModifyPendingOrderEvent) __);
}


/// @nodoc


class _Initialize implements ModifyPendingOrderEvent {
  const _Initialize();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Initialize);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderEvent.initialize()';
}


}




/// @nodoc


class _ModifyPendingOrderPriceChanged implements ModifyPendingOrderEvent {
  const _ModifyPendingOrderPriceChanged(this.state);
  

 final  TradeComponentState<double, OrderPriceErrorCode> state;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyPendingOrderPriceChangedCopyWith<_ModifyPendingOrderPriceChanged> get copyWith => __$ModifyPendingOrderPriceChangedCopyWithImpl<_ModifyPendingOrderPriceChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyPendingOrderPriceChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'ModifyPendingOrderEvent.priceChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$ModifyPendingOrderPriceChangedCopyWith<$Res> implements $ModifyPendingOrderEventCopyWith<$Res> {
  factory _$ModifyPendingOrderPriceChangedCopyWith(_ModifyPendingOrderPriceChanged value, $Res Function(_ModifyPendingOrderPriceChanged) _then) = __$ModifyPendingOrderPriceChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<double, OrderPriceErrorCode> state
});


$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get state;

}
/// @nodoc
class __$ModifyPendingOrderPriceChangedCopyWithImpl<$Res>
    implements _$ModifyPendingOrderPriceChangedCopyWith<$Res> {
  __$ModifyPendingOrderPriceChangedCopyWithImpl(this._self, this._then);

  final _ModifyPendingOrderPriceChanged _self;
  final $Res Function(_ModifyPendingOrderPriceChanged) _then;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_ModifyPendingOrderPriceChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,
  ));
}

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ModifyPendingOrderTakeProfitChanged implements ModifyPendingOrderEvent {
  const _ModifyPendingOrderTakeProfitChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyPendingOrderTakeProfitChangedCopyWith<_ModifyPendingOrderTakeProfitChanged> get copyWith => __$ModifyPendingOrderTakeProfitChangedCopyWithImpl<_ModifyPendingOrderTakeProfitChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyPendingOrderTakeProfitChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'ModifyPendingOrderEvent.takeProfitChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$ModifyPendingOrderTakeProfitChangedCopyWith<$Res> implements $ModifyPendingOrderEventCopyWith<$Res> {
  factory _$ModifyPendingOrderTakeProfitChangedCopyWith(_ModifyPendingOrderTakeProfitChanged value, $Res Function(_ModifyPendingOrderTakeProfitChanged) _then) = __$ModifyPendingOrderTakeProfitChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$ModifyPendingOrderTakeProfitChangedCopyWithImpl<$Res>
    implements _$ModifyPendingOrderTakeProfitChangedCopyWith<$Res> {
  __$ModifyPendingOrderTakeProfitChangedCopyWithImpl(this._self, this._then);

  final _ModifyPendingOrderTakeProfitChanged _self;
  final $Res Function(_ModifyPendingOrderTakeProfitChanged) _then;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_ModifyPendingOrderTakeProfitChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ModifyPendingOrderStopLossChanged implements ModifyPendingOrderEvent {
  const _ModifyPendingOrderStopLossChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyPendingOrderStopLossChangedCopyWith<_ModifyPendingOrderStopLossChanged> get copyWith => __$ModifyPendingOrderStopLossChangedCopyWithImpl<_ModifyPendingOrderStopLossChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyPendingOrderStopLossChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'ModifyPendingOrderEvent.stopLossChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$ModifyPendingOrderStopLossChangedCopyWith<$Res> implements $ModifyPendingOrderEventCopyWith<$Res> {
  factory _$ModifyPendingOrderStopLossChangedCopyWith(_ModifyPendingOrderStopLossChanged value, $Res Function(_ModifyPendingOrderStopLossChanged) _then) = __$ModifyPendingOrderStopLossChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$ModifyPendingOrderStopLossChangedCopyWithImpl<$Res>
    implements _$ModifyPendingOrderStopLossChangedCopyWith<$Res> {
  __$ModifyPendingOrderStopLossChangedCopyWithImpl(this._self, this._then);

  final _ModifyPendingOrderStopLossChanged _self;
  final $Res Function(_ModifyPendingOrderStopLossChanged) _then;

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_ModifyPendingOrderStopLossChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of ModifyPendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ModifyPendingOrderSubmit implements ModifyPendingOrderEvent {
  const _ModifyPendingOrderSubmit();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyPendingOrderSubmit);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderEvent.submit()';
}


}




/// @nodoc


class _ModifyPendingOrderDeleteOrder implements ModifyPendingOrderEvent {
  const _ModifyPendingOrderDeleteOrder();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyPendingOrderDeleteOrder);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderEvent.deleteOrder()';
}


}




/// @nodoc
mixin _$ModifyPendingOrderState {

 OrderModel get orderModel; set orderModel(OrderModel value); MarginInformationModel? get marginInformation; set marginInformation(MarginInformationModel? value); String? get accountNumber; set accountNumber(String? value); TradeComponentState<double, OrderPriceErrorCode> get orderPriceState; set orderPriceState(TradeComponentState<double, OrderPriceErrorCode> value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get takeProfitState; set takeProfitState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get stopLossState; set stopLossState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); ModifyPendingOrderProcessState get currentState; set currentState(ModifyPendingOrderProcessState value);
/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ModifyPendingOrderStateCopyWith<ModifyPendingOrderState> get copyWith => _$ModifyPendingOrderStateCopyWithImpl<ModifyPendingOrderState>(this as ModifyPendingOrderState, _$identity);





@override
String toString() {
  return 'ModifyPendingOrderState(orderModel: $orderModel, marginInformation: $marginInformation, accountNumber: $accountNumber, orderPriceState: $orderPriceState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $ModifyPendingOrderStateCopyWith<$Res>  {
  factory $ModifyPendingOrderStateCopyWith(ModifyPendingOrderState value, $Res Function(ModifyPendingOrderState) _then) = _$ModifyPendingOrderStateCopyWithImpl;
@useResult
$Res call({
 OrderModel orderModel, MarginInformationModel? marginInformation, String? accountNumber, TradeComponentState<double, OrderPriceErrorCode> orderPriceState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, ModifyPendingOrderProcessState currentState
});


$OrderModelCopyWith<$Res> get orderModel;$MarginInformationModelCopyWith<$Res>? get marginInformation;$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;$ModifyPendingOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$ModifyPendingOrderStateCopyWithImpl<$Res>
    implements $ModifyPendingOrderStateCopyWith<$Res> {
  _$ModifyPendingOrderStateCopyWithImpl(this._self, this._then);

  final ModifyPendingOrderState _self;
  final $Res Function(ModifyPendingOrderState) _then;

/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? orderModel = null,Object? marginInformation = freezed,Object? accountNumber = freezed,Object? orderPriceState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
orderModel: null == orderModel ? _self.orderModel : orderModel // ignore: cast_nullable_to_non_nullable
as OrderModel,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,orderPriceState: null == orderPriceState ? _self.orderPriceState : orderPriceState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ModifyPendingOrderProcessState,
  ));
}
/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderModelCopyWith<$Res> get orderModel {
  
  return $OrderModelCopyWith<$Res>(_self.orderModel, (value) {
    return _then(_self.copyWith(orderModel: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.orderPriceState, (value) {
    return _then(_self.copyWith(orderPriceState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModifyPendingOrderProcessStateCopyWith<$Res> get currentState {
  
  return $ModifyPendingOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _ModifyPendingOrderState extends ModifyPendingOrderState {
   _ModifyPendingOrderState({required this.orderModel, this.marginInformation, this.accountNumber, this.orderPriceState = const TradeComponentState<double, OrderPriceErrorCode>.loading(), this.takeProfitState = const TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.inactive(), this.stopLossState = const TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.inactive(), this.currentState = const ModifyPendingOrderProcessState.loading()}): super._();
  

@override  OrderModel orderModel;
@override  MarginInformationModel? marginInformation;
@override  String? accountNumber;
@override@JsonKey()  TradeComponentState<double, OrderPriceErrorCode> orderPriceState;
@override@JsonKey()  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState;
@override@JsonKey()  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState;
@override@JsonKey()  ModifyPendingOrderProcessState currentState;

/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyPendingOrderStateCopyWith<_ModifyPendingOrderState> get copyWith => __$ModifyPendingOrderStateCopyWithImpl<_ModifyPendingOrderState>(this, _$identity);





@override
String toString() {
  return 'ModifyPendingOrderState(orderModel: $orderModel, marginInformation: $marginInformation, accountNumber: $accountNumber, orderPriceState: $orderPriceState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$ModifyPendingOrderStateCopyWith<$Res> implements $ModifyPendingOrderStateCopyWith<$Res> {
  factory _$ModifyPendingOrderStateCopyWith(_ModifyPendingOrderState value, $Res Function(_ModifyPendingOrderState) _then) = __$ModifyPendingOrderStateCopyWithImpl;
@override @useResult
$Res call({
 OrderModel orderModel, MarginInformationModel? marginInformation, String? accountNumber, TradeComponentState<double, OrderPriceErrorCode> orderPriceState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, ModifyPendingOrderProcessState currentState
});


@override $OrderModelCopyWith<$Res> get orderModel;@override $MarginInformationModelCopyWith<$Res>? get marginInformation;@override $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;@override $ModifyPendingOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$ModifyPendingOrderStateCopyWithImpl<$Res>
    implements _$ModifyPendingOrderStateCopyWith<$Res> {
  __$ModifyPendingOrderStateCopyWithImpl(this._self, this._then);

  final _ModifyPendingOrderState _self;
  final $Res Function(_ModifyPendingOrderState) _then;

/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? orderModel = null,Object? marginInformation = freezed,Object? accountNumber = freezed,Object? orderPriceState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? currentState = null,}) {
  return _then(_ModifyPendingOrderState(
orderModel: null == orderModel ? _self.orderModel : orderModel // ignore: cast_nullable_to_non_nullable
as OrderModel,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,orderPriceState: null == orderPriceState ? _self.orderPriceState : orderPriceState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ModifyPendingOrderProcessState,
  ));
}

/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderModelCopyWith<$Res> get orderModel {
  
  return $OrderModelCopyWith<$Res>(_self.orderModel, (value) {
    return _then(_self.copyWith(orderModel: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.orderPriceState, (value) {
    return _then(_self.copyWith(orderPriceState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of ModifyPendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModifyPendingOrderProcessStateCopyWith<$Res> get currentState {
  
  return $ModifyPendingOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$ModifyPendingOrderProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState()';
}


}

/// @nodoc
class $ModifyPendingOrderProcessStateCopyWith<$Res>  {
$ModifyPendingOrderProcessStateCopyWith(ModifyPendingOrderProcessState _, $Res Function(ModifyPendingOrderProcessState) __);
}


/// @nodoc


class ModifyPendingOrderLoadingProcessState implements ModifyPendingOrderProcessState {
  const ModifyPendingOrderLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.loading()';
}


}




/// @nodoc


class ModifyPendingOrderConnectedProcessState implements ModifyPendingOrderProcessState {
  const ModifyPendingOrderConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.connected()';
}


}




/// @nodoc


class ModifyPendingOrderDisconnectedProcessState implements ModifyPendingOrderProcessState {
  const ModifyPendingOrderDisconnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderDisconnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.disconnected()';
}


}




/// @nodoc


class ModifyPlacingPendingOrderProcessState implements ModifyPendingOrderProcessState {
  const ModifyPlacingPendingOrderProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPlacingPendingOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.placingOrder()';
}


}




/// @nodoc


class ModifyPendingOrderSuccessProcessState implements ModifyPendingOrderProcessState {
  const ModifyPendingOrderSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.orderSuccess()';
}


}




/// @nodoc


class ModifyPendingOrderErrorProcessState implements ModifyPendingOrderProcessState {
  const ModifyPendingOrderErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPendingOrderErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.orderError()';
}


}




/// @nodoc


class DeletingOrderProcessState implements ModifyPendingOrderProcessState {
  const DeletingOrderProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeletingOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.deletingOrder()';
}


}




/// @nodoc


class OrderDeletionSuccessProcessState implements ModifyPendingOrderProcessState {
  const OrderDeletionSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderDeletionSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.orderDeletionSuccess()';
}


}




/// @nodoc


class OrderDeletionFailureProcessState implements ModifyPendingOrderProcessState {
  const OrderDeletionFailureProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderDeletionFailureProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.orderDeletionFailure()';
}


}




/// @nodoc


class OrderDeletionMarketClosedProcessState implements ModifyPendingOrderProcessState {
  const OrderDeletionMarketClosedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderDeletionMarketClosedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyPendingOrderProcessState.marketClosed()';
}


}




// dart format on
