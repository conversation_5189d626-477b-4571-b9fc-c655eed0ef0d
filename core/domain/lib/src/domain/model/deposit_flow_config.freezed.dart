// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_flow_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositFlowConfig {

 DepositType get depositType; String get origin;
/// Create a copy of DepositFlowConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<DepositFlowConfig> get copyWith => _$DepositFlowConfigCopyWithImpl<DepositFlowConfig>(this as DepositFlowConfig, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositFlowConfig&&(identical(other.depositType, depositType) || other.depositType == depositType)&&(identical(other.origin, origin) || other.origin == origin));
}


@override
int get hashCode => Object.hash(runtimeType,depositType,origin);

@override
String toString() {
  return 'DepositFlowConfig(depositType: $depositType, origin: $origin)';
}


}

/// @nodoc
abstract mixin class $DepositFlowConfigCopyWith<$Res>  {
  factory $DepositFlowConfigCopyWith(DepositFlowConfig value, $Res Function(DepositFlowConfig) _then) = _$DepositFlowConfigCopyWithImpl;
@useResult
$Res call({
 DepositType depositType, String origin
});




}
/// @nodoc
class _$DepositFlowConfigCopyWithImpl<$Res>
    implements $DepositFlowConfigCopyWith<$Res> {
  _$DepositFlowConfigCopyWithImpl(this._self, this._then);

  final DepositFlowConfig _self;
  final $Res Function(DepositFlowConfig) _then;

/// Create a copy of DepositFlowConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? depositType = null,Object? origin = null,}) {
  return _then(_self.copyWith(
depositType: null == depositType ? _self.depositType : depositType // ignore: cast_nullable_to_non_nullable
as DepositType,origin: null == origin ? _self.origin : origin // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _DepositFlowConfig implements DepositFlowConfig {
  const _DepositFlowConfig({required this.depositType, required this.origin});
  

@override final  DepositType depositType;
@override final  String origin;

/// Create a copy of DepositFlowConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositFlowConfigCopyWith<_DepositFlowConfig> get copyWith => __$DepositFlowConfigCopyWithImpl<_DepositFlowConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositFlowConfig&&(identical(other.depositType, depositType) || other.depositType == depositType)&&(identical(other.origin, origin) || other.origin == origin));
}


@override
int get hashCode => Object.hash(runtimeType,depositType,origin);

@override
String toString() {
  return 'DepositFlowConfig(depositType: $depositType, origin: $origin)';
}


}

/// @nodoc
abstract mixin class _$DepositFlowConfigCopyWith<$Res> implements $DepositFlowConfigCopyWith<$Res> {
  factory _$DepositFlowConfigCopyWith(_DepositFlowConfig value, $Res Function(_DepositFlowConfig) _then) = __$DepositFlowConfigCopyWithImpl;
@override @useResult
$Res call({
 DepositType depositType, String origin
});




}
/// @nodoc
class __$DepositFlowConfigCopyWithImpl<$Res>
    implements _$DepositFlowConfigCopyWith<$Res> {
  __$DepositFlowConfigCopyWithImpl(this._self, this._then);

  final _DepositFlowConfig _self;
  final $Res Function(_DepositFlowConfig) _then;

/// Create a copy of DepositFlowConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? depositType = null,Object? origin = null,}) {
  return _then(_DepositFlowConfig(
depositType: null == depositType ? _self.depositType : depositType // ignore: cast_nullable_to_non_nullable
as DepositType,origin: null == origin ? _self.origin : origin // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
