// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_method_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositPaymentMethodArgs {

 DepositPaymentMethod get method; num? get maxPollingAttempts; num? get pollingFrequencySeconds; DepositFlowConfig get depositFlowConfig;
/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentMethodArgsCopyWith<DepositPaymentMethodArgs> get copyWith => _$DepositPaymentMethodArgsCopyWithImpl<DepositPaymentMethodArgs>(this as DepositPaymentMethodArgs, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentMethodArgs&&(identical(other.method, method) || other.method == method)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig));
}


@override
int get hashCode => Object.hash(runtimeType,method,maxPollingAttempts,pollingFrequencySeconds,depositFlowConfig);

@override
String toString() {
  return 'DepositPaymentMethodArgs(method: $method, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds, depositFlowConfig: $depositFlowConfig)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentMethodArgsCopyWith<$Res>  {
  factory $DepositPaymentMethodArgsCopyWith(DepositPaymentMethodArgs value, $Res Function(DepositPaymentMethodArgs) _then) = _$DepositPaymentMethodArgsCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod method, num? maxPollingAttempts, num? pollingFrequencySeconds, DepositFlowConfig depositFlowConfig
});


$DepositPaymentMethodCopyWith<$Res> get method;$DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class _$DepositPaymentMethodArgsCopyWithImpl<$Res>
    implements $DepositPaymentMethodArgsCopyWith<$Res> {
  _$DepositPaymentMethodArgsCopyWithImpl(this._self, this._then);

  final DepositPaymentMethodArgs _self;
  final $Res Function(DepositPaymentMethodArgs) _then;

/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? method = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,Object? depositFlowConfig = null,}) {
  return _then(_self.copyWith(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,
  ));
}
/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get method {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.method, (value) {
    return _then(_self.copyWith(method: value));
  });
}/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}


/// @nodoc


class _DepositPaymentMethodArgs implements DepositPaymentMethodArgs {
  const _DepositPaymentMethodArgs({required this.method, this.maxPollingAttempts, this.pollingFrequencySeconds, required this.depositFlowConfig});
  

@override final  DepositPaymentMethod method;
@override final  num? maxPollingAttempts;
@override final  num? pollingFrequencySeconds;
@override final  DepositFlowConfig depositFlowConfig;

/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentMethodArgsCopyWith<_DepositPaymentMethodArgs> get copyWith => __$DepositPaymentMethodArgsCopyWithImpl<_DepositPaymentMethodArgs>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentMethodArgs&&(identical(other.method, method) || other.method == method)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig));
}


@override
int get hashCode => Object.hash(runtimeType,method,maxPollingAttempts,pollingFrequencySeconds,depositFlowConfig);

@override
String toString() {
  return 'DepositPaymentMethodArgs(method: $method, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds, depositFlowConfig: $depositFlowConfig)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentMethodArgsCopyWith<$Res> implements $DepositPaymentMethodArgsCopyWith<$Res> {
  factory _$DepositPaymentMethodArgsCopyWith(_DepositPaymentMethodArgs value, $Res Function(_DepositPaymentMethodArgs) _then) = __$DepositPaymentMethodArgsCopyWithImpl;
@override @useResult
$Res call({
 DepositPaymentMethod method, num? maxPollingAttempts, num? pollingFrequencySeconds, DepositFlowConfig depositFlowConfig
});


@override $DepositPaymentMethodCopyWith<$Res> get method;@override $DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class __$DepositPaymentMethodArgsCopyWithImpl<$Res>
    implements _$DepositPaymentMethodArgsCopyWith<$Res> {
  __$DepositPaymentMethodArgsCopyWithImpl(this._self, this._then);

  final _DepositPaymentMethodArgs _self;
  final $Res Function(_DepositPaymentMethodArgs) _then;

/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? method = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,Object? depositFlowConfig = null,}) {
  return _then(_DepositPaymentMethodArgs(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,
  ));
}

/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get method {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.method, (value) {
    return _then(_self.copyWith(method: value));
  });
}/// Create a copy of DepositPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

/// @nodoc
mixin _$WithdrawalPaymentMethodArgs {

 WithdrawalPaymentMethod get method;
/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodArgsCopyWith<WithdrawalPaymentMethodArgs> get copyWith => _$WithdrawalPaymentMethodArgsCopyWithImpl<WithdrawalPaymentMethodArgs>(this as WithdrawalPaymentMethodArgs, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawalPaymentMethodArgs&&(identical(other.method, method) || other.method == method));
}


@override
int get hashCode => Object.hash(runtimeType,method);

@override
String toString() {
  return 'WithdrawalPaymentMethodArgs(method: $method)';
}


}

/// @nodoc
abstract mixin class $WithdrawalPaymentMethodArgsCopyWith<$Res>  {
  factory $WithdrawalPaymentMethodArgsCopyWith(WithdrawalPaymentMethodArgs value, $Res Function(WithdrawalPaymentMethodArgs) _then) = _$WithdrawalPaymentMethodArgsCopyWithImpl;
@useResult
$Res call({
 WithdrawalPaymentMethod method
});


$WithdrawalPaymentMethodCopyWith<$Res> get method;

}
/// @nodoc
class _$WithdrawalPaymentMethodArgsCopyWithImpl<$Res>
    implements $WithdrawalPaymentMethodArgsCopyWith<$Res> {
  _$WithdrawalPaymentMethodArgsCopyWithImpl(this._self, this._then);

  final WithdrawalPaymentMethodArgs _self;
  final $Res Function(WithdrawalPaymentMethodArgs) _then;

/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? method = null,}) {
  return _then(_self.copyWith(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as WithdrawalPaymentMethod,
  ));
}
/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodCopyWith<$Res> get method {
  
  return $WithdrawalPaymentMethodCopyWith<$Res>(_self.method, (value) {
    return _then(_self.copyWith(method: value));
  });
}
}


/// @nodoc


class _WithdrawalPaymentMethodArgs implements WithdrawalPaymentMethodArgs {
  const _WithdrawalPaymentMethodArgs({required this.method});
  

@override final  WithdrawalPaymentMethod method;

/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawalPaymentMethodArgsCopyWith<_WithdrawalPaymentMethodArgs> get copyWith => __$WithdrawalPaymentMethodArgsCopyWithImpl<_WithdrawalPaymentMethodArgs>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawalPaymentMethodArgs&&(identical(other.method, method) || other.method == method));
}


@override
int get hashCode => Object.hash(runtimeType,method);

@override
String toString() {
  return 'WithdrawalPaymentMethodArgs(method: $method)';
}


}

/// @nodoc
abstract mixin class _$WithdrawalPaymentMethodArgsCopyWith<$Res> implements $WithdrawalPaymentMethodArgsCopyWith<$Res> {
  factory _$WithdrawalPaymentMethodArgsCopyWith(_WithdrawalPaymentMethodArgs value, $Res Function(_WithdrawalPaymentMethodArgs) _then) = __$WithdrawalPaymentMethodArgsCopyWithImpl;
@override @useResult
$Res call({
 WithdrawalPaymentMethod method
});


@override $WithdrawalPaymentMethodCopyWith<$Res> get method;

}
/// @nodoc
class __$WithdrawalPaymentMethodArgsCopyWithImpl<$Res>
    implements _$WithdrawalPaymentMethodArgsCopyWith<$Res> {
  __$WithdrawalPaymentMethodArgsCopyWithImpl(this._self, this._then);

  final _WithdrawalPaymentMethodArgs _self;
  final $Res Function(_WithdrawalPaymentMethodArgs) _then;

/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? method = null,}) {
  return _then(_WithdrawalPaymentMethodArgs(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as WithdrawalPaymentMethod,
  ));
}

/// Create a copy of WithdrawalPaymentMethodArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodCopyWith<$Res> get method {
  
  return $WithdrawalPaymentMethodCopyWith<$Res>(_self.method, (value) {
    return _then(_self.copyWith(method: value));
  });
}
}

// dart format on
