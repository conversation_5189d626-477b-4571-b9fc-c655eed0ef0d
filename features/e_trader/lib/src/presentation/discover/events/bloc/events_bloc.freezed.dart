// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'events_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$EventsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsEvent()';
}


}

/// @nodoc
class $EventsEventCopyWith<$Res>  {
$EventsEventCopyWith(EventsEvent _, $Res Function(EventsEvent) __);
}


/// @nodoc


class FetchEvents implements EventsEvent {
  const FetchEvents({this.query = '', this.loadMore = false});
  

@JsonKey() final  String query;
@JsonKey() final  bool loadMore;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FetchEventsCopyWith<FetchEvents> get copyWith => _$FetchEventsCopyWithImpl<FetchEvents>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FetchEvents&&(identical(other.query, query) || other.query == query)&&(identical(other.loadMore, loadMore) || other.loadMore == loadMore));
}


@override
int get hashCode => Object.hash(runtimeType,query,loadMore);

@override
String toString() {
  return 'EventsEvent.fetchEvents(query: $query, loadMore: $loadMore)';
}


}

/// @nodoc
abstract mixin class $FetchEventsCopyWith<$Res> implements $EventsEventCopyWith<$Res> {
  factory $FetchEventsCopyWith(FetchEvents value, $Res Function(FetchEvents) _then) = _$FetchEventsCopyWithImpl;
@useResult
$Res call({
 String query, bool loadMore
});




}
/// @nodoc
class _$FetchEventsCopyWithImpl<$Res>
    implements $FetchEventsCopyWith<$Res> {
  _$FetchEventsCopyWithImpl(this._self, this._then);

  final FetchEvents _self;
  final $Res Function(FetchEvents) _then;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = null,Object? loadMore = null,}) {
  return _then(FetchEvents(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,loadMore: null == loadMore ? _self.loadMore : loadMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class SearchEvents implements EventsEvent {
  const SearchEvents({required this.query, this.loadMore = false});
  

 final  String query;
@JsonKey() final  bool loadMore;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchEventsCopyWith<SearchEvents> get copyWith => _$SearchEventsCopyWithImpl<SearchEvents>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchEvents&&(identical(other.query, query) || other.query == query)&&(identical(other.loadMore, loadMore) || other.loadMore == loadMore));
}


@override
int get hashCode => Object.hash(runtimeType,query,loadMore);

@override
String toString() {
  return 'EventsEvent.searchEvents(query: $query, loadMore: $loadMore)';
}


}

/// @nodoc
abstract mixin class $SearchEventsCopyWith<$Res> implements $EventsEventCopyWith<$Res> {
  factory $SearchEventsCopyWith(SearchEvents value, $Res Function(SearchEvents) _then) = _$SearchEventsCopyWithImpl;
@useResult
$Res call({
 String query, bool loadMore
});




}
/// @nodoc
class _$SearchEventsCopyWithImpl<$Res>
    implements $SearchEventsCopyWith<$Res> {
  _$SearchEventsCopyWithImpl(this._self, this._then);

  final SearchEvents _self;
  final $Res Function(SearchEvents) _then;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = null,Object? loadMore = null,}) {
  return _then(SearchEvents(
query: null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,loadMore: null == loadMore ? _self.loadMore : loadMore // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class UpdateDateLabel implements EventsEvent {
  const UpdateDateLabel({required this.dateLabel});
  

 final  String dateLabel;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateDateLabelCopyWith<UpdateDateLabel> get copyWith => _$UpdateDateLabelCopyWithImpl<UpdateDateLabel>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateDateLabel&&(identical(other.dateLabel, dateLabel) || other.dateLabel == dateLabel));
}


@override
int get hashCode => Object.hash(runtimeType,dateLabel);

@override
String toString() {
  return 'EventsEvent.updateDateLabel(dateLabel: $dateLabel)';
}


}

/// @nodoc
abstract mixin class $UpdateDateLabelCopyWith<$Res> implements $EventsEventCopyWith<$Res> {
  factory $UpdateDateLabelCopyWith(UpdateDateLabel value, $Res Function(UpdateDateLabel) _then) = _$UpdateDateLabelCopyWithImpl;
@useResult
$Res call({
 String dateLabel
});




}
/// @nodoc
class _$UpdateDateLabelCopyWithImpl<$Res>
    implements $UpdateDateLabelCopyWith<$Res> {
  _$UpdateDateLabelCopyWithImpl(this._self, this._then);

  final UpdateDateLabel _self;
  final $Res Function(UpdateDateLabel) _then;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? dateLabel = null,}) {
  return _then(UpdateDateLabel(
dateLabel: null == dateLabel ? _self.dateLabel : dateLabel // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ChangeSortOrder implements EventsEvent {
  const _ChangeSortOrder(this.sortOrder);
  

 final  NewsEventsSortOrder sortOrder;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeSortOrderCopyWith<_ChangeSortOrder> get copyWith => __$ChangeSortOrderCopyWithImpl<_ChangeSortOrder>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeSortOrder&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder);

@override
String toString() {
  return 'EventsEvent.changeSortOrder(sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class _$ChangeSortOrderCopyWith<$Res> implements $EventsEventCopyWith<$Res> {
  factory _$ChangeSortOrderCopyWith(_ChangeSortOrder value, $Res Function(_ChangeSortOrder) _then) = __$ChangeSortOrderCopyWithImpl;
@useResult
$Res call({
 NewsEventsSortOrder sortOrder
});




}
/// @nodoc
class __$ChangeSortOrderCopyWithImpl<$Res>
    implements _$ChangeSortOrderCopyWith<$Res> {
  __$ChangeSortOrderCopyWithImpl(this._self, this._then);

  final _ChangeSortOrder _self;
  final $Res Function(_ChangeSortOrder) _then;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sortOrder = null,}) {
  return _then(_ChangeSortOrder(
null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as NewsEventsSortOrder,
  ));
}


}

/// @nodoc


class _AddEventToCalendar implements EventsEvent {
  const _AddEventToCalendar({required this.title, required this.subtitle, required this.date, required this.eventId, required this.successMessage, required this.errorMessage, required this.permissionDeniedMessage, required this.noCalendarFoundMessage, required this.operationFailedMessage});
  

 final  String title;
 final  String subtitle;
 final  DateTime date;
 final  String eventId;
 final  String successMessage;
 final  String errorMessage;
 final  String permissionDeniedMessage;
 final  String noCalendarFoundMessage;
 final  String operationFailedMessage;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AddEventToCalendarCopyWith<_AddEventToCalendar> get copyWith => __$AddEventToCalendarCopyWithImpl<_AddEventToCalendar>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AddEventToCalendar&&(identical(other.title, title) || other.title == title)&&(identical(other.subtitle, subtitle) || other.subtitle == subtitle)&&(identical(other.date, date) || other.date == date)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.successMessage, successMessage) || other.successMessage == successMessage)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.permissionDeniedMessage, permissionDeniedMessage) || other.permissionDeniedMessage == permissionDeniedMessage)&&(identical(other.noCalendarFoundMessage, noCalendarFoundMessage) || other.noCalendarFoundMessage == noCalendarFoundMessage)&&(identical(other.operationFailedMessage, operationFailedMessage) || other.operationFailedMessage == operationFailedMessage));
}


@override
int get hashCode => Object.hash(runtimeType,title,subtitle,date,eventId,successMessage,errorMessage,permissionDeniedMessage,noCalendarFoundMessage,operationFailedMessage);

@override
String toString() {
  return 'EventsEvent.addEventToCalendar(title: $title, subtitle: $subtitle, date: $date, eventId: $eventId, successMessage: $successMessage, errorMessage: $errorMessage, permissionDeniedMessage: $permissionDeniedMessage, noCalendarFoundMessage: $noCalendarFoundMessage, operationFailedMessage: $operationFailedMessage)';
}


}

/// @nodoc
abstract mixin class _$AddEventToCalendarCopyWith<$Res> implements $EventsEventCopyWith<$Res> {
  factory _$AddEventToCalendarCopyWith(_AddEventToCalendar value, $Res Function(_AddEventToCalendar) _then) = __$AddEventToCalendarCopyWithImpl;
@useResult
$Res call({
 String title, String subtitle, DateTime date, String eventId, String successMessage, String errorMessage, String permissionDeniedMessage, String noCalendarFoundMessage, String operationFailedMessage
});




}
/// @nodoc
class __$AddEventToCalendarCopyWithImpl<$Res>
    implements _$AddEventToCalendarCopyWith<$Res> {
  __$AddEventToCalendarCopyWithImpl(this._self, this._then);

  final _AddEventToCalendar _self;
  final $Res Function(_AddEventToCalendar) _then;

/// Create a copy of EventsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? title = null,Object? subtitle = null,Object? date = null,Object? eventId = null,Object? successMessage = null,Object? errorMessage = null,Object? permissionDeniedMessage = null,Object? noCalendarFoundMessage = null,Object? operationFailedMessage = null,}) {
  return _then(_AddEventToCalendar(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,subtitle: null == subtitle ? _self.subtitle : subtitle // ignore: cast_nullable_to_non_nullable
as String,date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as String,successMessage: null == successMessage ? _self.successMessage : successMessage // ignore: cast_nullable_to_non_nullable
as String,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,permissionDeniedMessage: null == permissionDeniedMessage ? _self.permissionDeniedMessage : permissionDeniedMessage // ignore: cast_nullable_to_non_nullable
as String,noCalendarFoundMessage: null == noCalendarFoundMessage ? _self.noCalendarFoundMessage : noCalendarFoundMessage // ignore: cast_nullable_to_non_nullable
as String,operationFailedMessage: null == operationFailedMessage ? _self.operationFailedMessage : operationFailedMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$EventsState {

 EventsProcessState get processState; List<EventItem> get allEventItems; List<Object?> get groupedEvents; bool get hasReachedMax; int get pageSize; int get currentPage; int get count; String get dateLabel; NewsEventsSortOrder get sortOrder;
/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventsStateCopyWith<EventsState> get copyWith => _$EventsStateCopyWithImpl<EventsState>(this as EventsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other.allEventItems, allEventItems)&&const DeepCollectionEquality().equals(other.groupedEvents, groupedEvents)&&(identical(other.hasReachedMax, hasReachedMax) || other.hasReachedMax == hasReachedMax)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.count, count) || other.count == count)&&(identical(other.dateLabel, dateLabel) || other.dateLabel == dateLabel)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(allEventItems),const DeepCollectionEquality().hash(groupedEvents),hasReachedMax,pageSize,currentPage,count,dateLabel,sortOrder);

@override
String toString() {
  return 'EventsState(processState: $processState, allEventItems: $allEventItems, groupedEvents: $groupedEvents, hasReachedMax: $hasReachedMax, pageSize: $pageSize, currentPage: $currentPage, count: $count, dateLabel: $dateLabel, sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class $EventsStateCopyWith<$Res>  {
  factory $EventsStateCopyWith(EventsState value, $Res Function(EventsState) _then) = _$EventsStateCopyWithImpl;
@useResult
$Res call({
 EventsProcessState processState, List<EventItem> allEventItems, List<Object?> groupedEvents, bool hasReachedMax, int pageSize, int currentPage, int count, String dateLabel, NewsEventsSortOrder sortOrder
});


$EventsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$EventsStateCopyWithImpl<$Res>
    implements $EventsStateCopyWith<$Res> {
  _$EventsStateCopyWithImpl(this._self, this._then);

  final EventsState _self;
  final $Res Function(EventsState) _then;

/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? allEventItems = null,Object? groupedEvents = null,Object? hasReachedMax = null,Object? pageSize = null,Object? currentPage = null,Object? count = null,Object? dateLabel = null,Object? sortOrder = null,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as EventsProcessState,allEventItems: null == allEventItems ? _self.allEventItems : allEventItems // ignore: cast_nullable_to_non_nullable
as List<EventItem>,groupedEvents: null == groupedEvents ? _self.groupedEvents : groupedEvents // ignore: cast_nullable_to_non_nullable
as List<Object?>,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,dateLabel: null == dateLabel ? _self.dateLabel : dateLabel // ignore: cast_nullable_to_non_nullable
as String,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as NewsEventsSortOrder,
  ));
}
/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EventsProcessStateCopyWith<$Res> get processState {
  
  return $EventsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _EventsState implements EventsState {
  const _EventsState({required this.processState, final  List<EventItem> allEventItems = const [], final  List<Object?> groupedEvents = const [], this.hasReachedMax = false, this.pageSize = 10, this.currentPage = 0, this.count = 0, this.dateLabel = '', required this.sortOrder}): _allEventItems = allEventItems,_groupedEvents = groupedEvents;
  

@override final  EventsProcessState processState;
 final  List<EventItem> _allEventItems;
@override@JsonKey() List<EventItem> get allEventItems {
  if (_allEventItems is EqualUnmodifiableListView) return _allEventItems;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allEventItems);
}

 final  List<Object?> _groupedEvents;
@override@JsonKey() List<Object?> get groupedEvents {
  if (_groupedEvents is EqualUnmodifiableListView) return _groupedEvents;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_groupedEvents);
}

@override@JsonKey() final  bool hasReachedMax;
@override@JsonKey() final  int pageSize;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  int count;
@override@JsonKey() final  String dateLabel;
@override final  NewsEventsSortOrder sortOrder;

/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventsStateCopyWith<_EventsState> get copyWith => __$EventsStateCopyWithImpl<_EventsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventsState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other._allEventItems, _allEventItems)&&const DeepCollectionEquality().equals(other._groupedEvents, _groupedEvents)&&(identical(other.hasReachedMax, hasReachedMax) || other.hasReachedMax == hasReachedMax)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.count, count) || other.count == count)&&(identical(other.dateLabel, dateLabel) || other.dateLabel == dateLabel)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(_allEventItems),const DeepCollectionEquality().hash(_groupedEvents),hasReachedMax,pageSize,currentPage,count,dateLabel,sortOrder);

@override
String toString() {
  return 'EventsState(processState: $processState, allEventItems: $allEventItems, groupedEvents: $groupedEvents, hasReachedMax: $hasReachedMax, pageSize: $pageSize, currentPage: $currentPage, count: $count, dateLabel: $dateLabel, sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class _$EventsStateCopyWith<$Res> implements $EventsStateCopyWith<$Res> {
  factory _$EventsStateCopyWith(_EventsState value, $Res Function(_EventsState) _then) = __$EventsStateCopyWithImpl;
@override @useResult
$Res call({
 EventsProcessState processState, List<EventItem> allEventItems, List<Object?> groupedEvents, bool hasReachedMax, int pageSize, int currentPage, int count, String dateLabel, NewsEventsSortOrder sortOrder
});


@override $EventsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$EventsStateCopyWithImpl<$Res>
    implements _$EventsStateCopyWith<$Res> {
  __$EventsStateCopyWithImpl(this._self, this._then);

  final _EventsState _self;
  final $Res Function(_EventsState) _then;

/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? allEventItems = null,Object? groupedEvents = null,Object? hasReachedMax = null,Object? pageSize = null,Object? currentPage = null,Object? count = null,Object? dateLabel = null,Object? sortOrder = null,}) {
  return _then(_EventsState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as EventsProcessState,allEventItems: null == allEventItems ? _self._allEventItems : allEventItems // ignore: cast_nullable_to_non_nullable
as List<EventItem>,groupedEvents: null == groupedEvents ? _self._groupedEvents : groupedEvents // ignore: cast_nullable_to_non_nullable
as List<Object?>,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,count: null == count ? _self.count : count // ignore: cast_nullable_to_non_nullable
as int,dateLabel: null == dateLabel ? _self.dateLabel : dateLabel // ignore: cast_nullable_to_non_nullable
as String,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as NewsEventsSortOrder,
  ));
}

/// Create a copy of EventsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EventsProcessStateCopyWith<$Res> get processState {
  
  return $EventsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$EventsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsProcessState()';
}


}

/// @nodoc
class $EventsProcessStateCopyWith<$Res>  {
$EventsProcessStateCopyWith(EventsProcessState _, $Res Function(EventsProcessState) __);
}


/// @nodoc


class EventsInitial implements EventsProcessState {
  const EventsInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsProcessState.initial()';
}


}




/// @nodoc


class EventsLoading implements EventsProcessState {
  const EventsLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsProcessState.loading()';
}


}




/// @nodoc


class EventsSuccess implements EventsProcessState {
  const EventsSuccess();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsProcessState.success()';
}


}




/// @nodoc


class EventsLoadingMore implements EventsProcessState {
  const EventsLoadingMore();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsLoadingMore);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EventsProcessState.loadingMore()';
}


}




/// @nodoc


class EventsError implements EventsProcessState {
  const EventsError(this.message);
  

 final  String message;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventsErrorCopyWith<EventsError> get copyWith => _$EventsErrorCopyWithImpl<EventsError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'EventsProcessState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $EventsErrorCopyWith<$Res> implements $EventsProcessStateCopyWith<$Res> {
  factory $EventsErrorCopyWith(EventsError value, $Res Function(EventsError) _then) = _$EventsErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$EventsErrorCopyWithImpl<$Res>
    implements $EventsErrorCopyWith<$Res> {
  _$EventsErrorCopyWithImpl(this._self, this._then);

  final EventsError _self;
  final $Res Function(EventsError) _then;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(EventsError(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class CalendarError implements EventsProcessState {
  const CalendarError({required this.message, required this.title, required this.eventId});
  

 final  String message;
 final  String title;
 final  String eventId;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CalendarErrorCopyWith<CalendarError> get copyWith => _$CalendarErrorCopyWithImpl<CalendarError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CalendarError&&(identical(other.message, message) || other.message == message)&&(identical(other.title, title) || other.title == title)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}


@override
int get hashCode => Object.hash(runtimeType,message,title,eventId);

@override
String toString() {
  return 'EventsProcessState.calendarError(message: $message, title: $title, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $CalendarErrorCopyWith<$Res> implements $EventsProcessStateCopyWith<$Res> {
  factory $CalendarErrorCopyWith(CalendarError value, $Res Function(CalendarError) _then) = _$CalendarErrorCopyWithImpl;
@useResult
$Res call({
 String message, String title, String eventId
});




}
/// @nodoc
class _$CalendarErrorCopyWithImpl<$Res>
    implements $CalendarErrorCopyWith<$Res> {
  _$CalendarErrorCopyWithImpl(this._self, this._then);

  final CalendarError _self;
  final $Res Function(CalendarError) _then;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? title = null,Object? eventId = null,}) {
  return _then(CalendarError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class EventsCalendarMessage implements EventsProcessState {
  const EventsCalendarMessage({required this.message, required this.eventId});
  

 final  String message;
 final  String eventId;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventsCalendarMessageCopyWith<EventsCalendarMessage> get copyWith => _$EventsCalendarMessageCopyWithImpl<EventsCalendarMessage>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventsCalendarMessage&&(identical(other.message, message) || other.message == message)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}


@override
int get hashCode => Object.hash(runtimeType,message,eventId);

@override
String toString() {
  return 'EventsProcessState.calendarMessage(message: $message, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $EventsCalendarMessageCopyWith<$Res> implements $EventsProcessStateCopyWith<$Res> {
  factory $EventsCalendarMessageCopyWith(EventsCalendarMessage value, $Res Function(EventsCalendarMessage) _then) = _$EventsCalendarMessageCopyWithImpl;
@useResult
$Res call({
 String message, String eventId
});




}
/// @nodoc
class _$EventsCalendarMessageCopyWithImpl<$Res>
    implements $EventsCalendarMessageCopyWith<$Res> {
  _$EventsCalendarMessageCopyWithImpl(this._self, this._then);

  final EventsCalendarMessage _self;
  final $Res Function(EventsCalendarMessage) _then;

/// Create a copy of EventsProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? eventId = null,}) {
  return _then(EventsCalendarMessage(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
