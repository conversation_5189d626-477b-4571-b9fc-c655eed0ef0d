// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WithdrawRequestModel {

@JsonKey(name: "paymentType") String get paymentType;@JsonKey(name: "accountInfo") AccountInfo get accountInfo;@JsonKey(name: "amount") Amount get amount;@JsonKey(name: "metadata") Metadata get metadata;
/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawRequestModelCopyWith<WithdrawRequestModel> get copyWith => _$WithdrawRequestModelCopyWithImpl<WithdrawRequestModel>(this as WithdrawRequestModel, _$identity);

  /// Serializes this WithdrawRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawRequestModel&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.accountInfo, accountInfo) || other.accountInfo == accountInfo)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentType,accountInfo,amount,metadata);

@override
String toString() {
  return 'WithdrawRequestModel(paymentType: $paymentType, accountInfo: $accountInfo, amount: $amount, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $WithdrawRequestModelCopyWith<$Res>  {
  factory $WithdrawRequestModelCopyWith(WithdrawRequestModel value, $Res Function(WithdrawRequestModel) _then) = _$WithdrawRequestModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "paymentType") String paymentType,@JsonKey(name: "accountInfo") AccountInfo accountInfo,@JsonKey(name: "amount") Amount amount,@JsonKey(name: "metadata") Metadata metadata
});


$AccountInfoCopyWith<$Res> get accountInfo;$AmountCopyWith<$Res> get amount;$MetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class _$WithdrawRequestModelCopyWithImpl<$Res>
    implements $WithdrawRequestModelCopyWith<$Res> {
  _$WithdrawRequestModelCopyWithImpl(this._self, this._then);

  final WithdrawRequestModel _self;
  final $Res Function(WithdrawRequestModel) _then;

/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentType = null,Object? accountInfo = null,Object? amount = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as String,accountInfo: null == accountInfo ? _self.accountInfo : accountInfo // ignore: cast_nullable_to_non_nullable
as AccountInfo,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as Amount,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Metadata,
  ));
}
/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountInfoCopyWith<$Res> get accountInfo {
  
  return $AccountInfoCopyWith<$Res>(_self.accountInfo, (value) {
    return _then(_self.copyWith(accountInfo: value));
  });
}/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AmountCopyWith<$Res> get amount {
  
  return $AmountCopyWith<$Res>(_self.amount, (value) {
    return _then(_self.copyWith(amount: value));
  });
}/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MetadataCopyWith<$Res> get metadata {
  
  return $MetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _WithdrawRequestModel implements WithdrawRequestModel {
  const _WithdrawRequestModel({@JsonKey(name: "paymentType") required this.paymentType, @JsonKey(name: "accountInfo") required this.accountInfo, @JsonKey(name: "amount") required this.amount, @JsonKey(name: "metadata") required this.metadata});
  factory _WithdrawRequestModel.fromJson(Map<String, dynamic> json) => _$WithdrawRequestModelFromJson(json);

@override@JsonKey(name: "paymentType") final  String paymentType;
@override@JsonKey(name: "accountInfo") final  AccountInfo accountInfo;
@override@JsonKey(name: "amount") final  Amount amount;
@override@JsonKey(name: "metadata") final  Metadata metadata;

/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawRequestModelCopyWith<_WithdrawRequestModel> get copyWith => __$WithdrawRequestModelCopyWithImpl<_WithdrawRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawRequestModel&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.accountInfo, accountInfo) || other.accountInfo == accountInfo)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,paymentType,accountInfo,amount,metadata);

@override
String toString() {
  return 'WithdrawRequestModel(paymentType: $paymentType, accountInfo: $accountInfo, amount: $amount, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$WithdrawRequestModelCopyWith<$Res> implements $WithdrawRequestModelCopyWith<$Res> {
  factory _$WithdrawRequestModelCopyWith(_WithdrawRequestModel value, $Res Function(_WithdrawRequestModel) _then) = __$WithdrawRequestModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "paymentType") String paymentType,@JsonKey(name: "accountInfo") AccountInfo accountInfo,@JsonKey(name: "amount") Amount amount,@JsonKey(name: "metadata") Metadata metadata
});


@override $AccountInfoCopyWith<$Res> get accountInfo;@override $AmountCopyWith<$Res> get amount;@override $MetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class __$WithdrawRequestModelCopyWithImpl<$Res>
    implements _$WithdrawRequestModelCopyWith<$Res> {
  __$WithdrawRequestModelCopyWithImpl(this._self, this._then);

  final _WithdrawRequestModel _self;
  final $Res Function(_WithdrawRequestModel) _then;

/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentType = null,Object? accountInfo = null,Object? amount = null,Object? metadata = null,}) {
  return _then(_WithdrawRequestModel(
paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as String,accountInfo: null == accountInfo ? _self.accountInfo : accountInfo // ignore: cast_nullable_to_non_nullable
as AccountInfo,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as Amount,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Metadata,
  ));
}

/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountInfoCopyWith<$Res> get accountInfo {
  
  return $AccountInfoCopyWith<$Res>(_self.accountInfo, (value) {
    return _then(_self.copyWith(accountInfo: value));
  });
}/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AmountCopyWith<$Res> get amount {
  
  return $AmountCopyWith<$Res>(_self.amount, (value) {
    return _then(_self.copyWith(amount: value));
  });
}/// Create a copy of WithdrawRequestModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MetadataCopyWith<$Res> get metadata {
  
  return $MetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
mixin _$AccountInfo {

@JsonKey(name: "tradingAccountId") String get tradingAccountId;@JsonKey(name: "accountCurrency") String get accountCurrency;
/// Create a copy of AccountInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountInfoCopyWith<AccountInfo> get copyWith => _$AccountInfoCopyWithImpl<AccountInfo>(this as AccountInfo, _$identity);

  /// Serializes this AccountInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountInfo&&(identical(other.tradingAccountId, tradingAccountId) || other.tradingAccountId == tradingAccountId)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tradingAccountId,accountCurrency);

@override
String toString() {
  return 'AccountInfo(tradingAccountId: $tradingAccountId, accountCurrency: $accountCurrency)';
}


}

/// @nodoc
abstract mixin class $AccountInfoCopyWith<$Res>  {
  factory $AccountInfoCopyWith(AccountInfo value, $Res Function(AccountInfo) _then) = _$AccountInfoCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "tradingAccountId") String tradingAccountId,@JsonKey(name: "accountCurrency") String accountCurrency
});




}
/// @nodoc
class _$AccountInfoCopyWithImpl<$Res>
    implements $AccountInfoCopyWith<$Res> {
  _$AccountInfoCopyWithImpl(this._self, this._then);

  final AccountInfo _self;
  final $Res Function(AccountInfo) _then;

/// Create a copy of AccountInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tradingAccountId = null,Object? accountCurrency = null,}) {
  return _then(_self.copyWith(
tradingAccountId: null == tradingAccountId ? _self.tradingAccountId : tradingAccountId // ignore: cast_nullable_to_non_nullable
as String,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AccountInfo implements AccountInfo {
  const _AccountInfo({@JsonKey(name: "tradingAccountId") required this.tradingAccountId, @JsonKey(name: "accountCurrency") required this.accountCurrency});
  factory _AccountInfo.fromJson(Map<String, dynamic> json) => _$AccountInfoFromJson(json);

@override@JsonKey(name: "tradingAccountId") final  String tradingAccountId;
@override@JsonKey(name: "accountCurrency") final  String accountCurrency;

/// Create a copy of AccountInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountInfoCopyWith<_AccountInfo> get copyWith => __$AccountInfoCopyWithImpl<_AccountInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountInfo&&(identical(other.tradingAccountId, tradingAccountId) || other.tradingAccountId == tradingAccountId)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tradingAccountId,accountCurrency);

@override
String toString() {
  return 'AccountInfo(tradingAccountId: $tradingAccountId, accountCurrency: $accountCurrency)';
}


}

/// @nodoc
abstract mixin class _$AccountInfoCopyWith<$Res> implements $AccountInfoCopyWith<$Res> {
  factory _$AccountInfoCopyWith(_AccountInfo value, $Res Function(_AccountInfo) _then) = __$AccountInfoCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "tradingAccountId") String tradingAccountId,@JsonKey(name: "accountCurrency") String accountCurrency
});




}
/// @nodoc
class __$AccountInfoCopyWithImpl<$Res>
    implements _$AccountInfoCopyWith<$Res> {
  __$AccountInfoCopyWithImpl(this._self, this._then);

  final _AccountInfo _self;
  final $Res Function(_AccountInfo) _then;

/// Create a copy of AccountInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tradingAccountId = null,Object? accountCurrency = null,}) {
  return _then(_AccountInfo(
tradingAccountId: null == tradingAccountId ? _self.tradingAccountId : tradingAccountId // ignore: cast_nullable_to_non_nullable
as String,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Amount {

@JsonKey(name: "currency") String get currency;@JsonKey(name: "conversionRateString") String get conversionRateString;@JsonKey(name: "amount") num get amount;@JsonKey(name: "convertedAmount") num get convertedAmount;@JsonKey(name: "conversionRate") num get conversionRate;
/// Create a copy of Amount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AmountCopyWith<Amount> get copyWith => _$AmountCopyWithImpl<Amount>(this as Amount, _$identity);

  /// Serializes this Amount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Amount&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currency,conversionRateString,amount,convertedAmount,conversionRate);

@override
String toString() {
  return 'Amount(currency: $currency, conversionRateString: $conversionRateString, amount: $amount, convertedAmount: $convertedAmount, conversionRate: $conversionRate)';
}


}

/// @nodoc
abstract mixin class $AmountCopyWith<$Res>  {
  factory $AmountCopyWith(Amount value, $Res Function(Amount) _then) = _$AmountCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "currency") String currency,@JsonKey(name: "conversionRateString") String conversionRateString,@JsonKey(name: "amount") num amount,@JsonKey(name: "convertedAmount") num convertedAmount,@JsonKey(name: "conversionRate") num conversionRate
});




}
/// @nodoc
class _$AmountCopyWithImpl<$Res>
    implements $AmountCopyWith<$Res> {
  _$AmountCopyWithImpl(this._self, this._then);

  final Amount _self;
  final $Res Function(Amount) _then;

/// Create a copy of Amount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currency = null,Object? conversionRateString = null,Object? amount = null,Object? convertedAmount = null,Object? conversionRate = null,}) {
  return _then(_self.copyWith(
currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as num,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as num,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as num,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Amount implements Amount {
  const _Amount({@JsonKey(name: "currency") required this.currency, @JsonKey(name: "conversionRateString") required this.conversionRateString, @JsonKey(name: "amount") required this.amount, @JsonKey(name: "convertedAmount") required this.convertedAmount, @JsonKey(name: "conversionRate") required this.conversionRate});
  factory _Amount.fromJson(Map<String, dynamic> json) => _$AmountFromJson(json);

@override@JsonKey(name: "currency") final  String currency;
@override@JsonKey(name: "conversionRateString") final  String conversionRateString;
@override@JsonKey(name: "amount") final  num amount;
@override@JsonKey(name: "convertedAmount") final  num convertedAmount;
@override@JsonKey(name: "conversionRate") final  num conversionRate;

/// Create a copy of Amount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AmountCopyWith<_Amount> get copyWith => __$AmountCopyWithImpl<_Amount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AmountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Amount&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,currency,conversionRateString,amount,convertedAmount,conversionRate);

@override
String toString() {
  return 'Amount(currency: $currency, conversionRateString: $conversionRateString, amount: $amount, convertedAmount: $convertedAmount, conversionRate: $conversionRate)';
}


}

/// @nodoc
abstract mixin class _$AmountCopyWith<$Res> implements $AmountCopyWith<$Res> {
  factory _$AmountCopyWith(_Amount value, $Res Function(_Amount) _then) = __$AmountCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "currency") String currency,@JsonKey(name: "conversionRateString") String conversionRateString,@JsonKey(name: "amount") num amount,@JsonKey(name: "convertedAmount") num convertedAmount,@JsonKey(name: "conversionRate") num conversionRate
});




}
/// @nodoc
class __$AmountCopyWithImpl<$Res>
    implements _$AmountCopyWith<$Res> {
  __$AmountCopyWithImpl(this._self, this._then);

  final _Amount _self;
  final $Res Function(_Amount) _then;

/// Create a copy of Amount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currency = null,Object? conversionRateString = null,Object? amount = null,Object? convertedAmount = null,Object? conversionRate = null,}) {
  return _then(_Amount(
currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as num,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as num,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as num,
  ));
}


}

Metadata _$MetadataFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'card':
          return CardMetadata.fromJson(
            json
          );
                case 'skrill':
          return SkrillMetadata.fromJson(
            json
          );
                case 'neteller':
          return NetellerMetadata.fromJson(
            json
          );
                case 'bank':
          return BankMetadata.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'Metadata',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$Metadata {



  /// Serializes this Metadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Metadata);
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Metadata()';
}


}

/// @nodoc
class $MetadataCopyWith<$Res>  {
$MetadataCopyWith(Metadata _, $Res Function(Metadata) __);
}


/// @nodoc
@JsonSerializable()

class CardMetadata implements Metadata {
  const CardMetadata({@JsonKey(name: "card") required this.card, final  String? $type}): $type = $type ?? 'card';
  factory CardMetadata.fromJson(Map<String, dynamic> json) => _$CardMetadataFromJson(json);

@JsonKey(name: "card") final  Card card;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CardMetadataCopyWith<CardMetadata> get copyWith => _$CardMetadataCopyWithImpl<CardMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CardMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CardMetadata&&(identical(other.card, card) || other.card == card));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,card);

@override
String toString() {
  return 'Metadata.card(card: $card)';
}


}

/// @nodoc
abstract mixin class $CardMetadataCopyWith<$Res> implements $MetadataCopyWith<$Res> {
  factory $CardMetadataCopyWith(CardMetadata value, $Res Function(CardMetadata) _then) = _$CardMetadataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "card") Card card
});


$CardCopyWith<$Res> get card;

}
/// @nodoc
class _$CardMetadataCopyWithImpl<$Res>
    implements $CardMetadataCopyWith<$Res> {
  _$CardMetadataCopyWithImpl(this._self, this._then);

  final CardMetadata _self;
  final $Res Function(CardMetadata) _then;

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? card = null,}) {
  return _then(CardMetadata(
card: null == card ? _self.card : card // ignore: cast_nullable_to_non_nullable
as Card,
  ));
}

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CardCopyWith<$Res> get card {
  
  return $CardCopyWith<$Res>(_self.card, (value) {
    return _then(_self.copyWith(card: value));
  });
}
}

/// @nodoc
@JsonSerializable()

class SkrillMetadata implements Metadata {
  const SkrillMetadata({@JsonKey(name: "skrill") required this.skrill, final  String? $type}): $type = $type ?? 'skrill';
  factory SkrillMetadata.fromJson(Map<String, dynamic> json) => _$SkrillMetadataFromJson(json);

@JsonKey(name: "skrill") final  Skrill skrill;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SkrillMetadataCopyWith<SkrillMetadata> get copyWith => _$SkrillMetadataCopyWithImpl<SkrillMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SkrillMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SkrillMetadata&&(identical(other.skrill, skrill) || other.skrill == skrill));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,skrill);

@override
String toString() {
  return 'Metadata.skrill(skrill: $skrill)';
}


}

/// @nodoc
abstract mixin class $SkrillMetadataCopyWith<$Res> implements $MetadataCopyWith<$Res> {
  factory $SkrillMetadataCopyWith(SkrillMetadata value, $Res Function(SkrillMetadata) _then) = _$SkrillMetadataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "skrill") Skrill skrill
});


$SkrillCopyWith<$Res> get skrill;

}
/// @nodoc
class _$SkrillMetadataCopyWithImpl<$Res>
    implements $SkrillMetadataCopyWith<$Res> {
  _$SkrillMetadataCopyWithImpl(this._self, this._then);

  final SkrillMetadata _self;
  final $Res Function(SkrillMetadata) _then;

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? skrill = null,}) {
  return _then(SkrillMetadata(
skrill: null == skrill ? _self.skrill : skrill // ignore: cast_nullable_to_non_nullable
as Skrill,
  ));
}

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SkrillCopyWith<$Res> get skrill {
  
  return $SkrillCopyWith<$Res>(_self.skrill, (value) {
    return _then(_self.copyWith(skrill: value));
  });
}
}

/// @nodoc
@JsonSerializable()

class NetellerMetadata implements Metadata {
  const NetellerMetadata({@JsonKey(name: "neteller") required this.neteller, final  String? $type}): $type = $type ?? 'neteller';
  factory NetellerMetadata.fromJson(Map<String, dynamic> json) => _$NetellerMetadataFromJson(json);

@JsonKey(name: "neteller") final  Neteller neteller;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetellerMetadataCopyWith<NetellerMetadata> get copyWith => _$NetellerMetadataCopyWithImpl<NetellerMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetellerMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetellerMetadata&&(identical(other.neteller, neteller) || other.neteller == neteller));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,neteller);

@override
String toString() {
  return 'Metadata.neteller(neteller: $neteller)';
}


}

/// @nodoc
abstract mixin class $NetellerMetadataCopyWith<$Res> implements $MetadataCopyWith<$Res> {
  factory $NetellerMetadataCopyWith(NetellerMetadata value, $Res Function(NetellerMetadata) _then) = _$NetellerMetadataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "neteller") Neteller neteller
});


$NetellerCopyWith<$Res> get neteller;

}
/// @nodoc
class _$NetellerMetadataCopyWithImpl<$Res>
    implements $NetellerMetadataCopyWith<$Res> {
  _$NetellerMetadataCopyWithImpl(this._self, this._then);

  final NetellerMetadata _self;
  final $Res Function(NetellerMetadata) _then;

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? neteller = null,}) {
  return _then(NetellerMetadata(
neteller: null == neteller ? _self.neteller : neteller // ignore: cast_nullable_to_non_nullable
as Neteller,
  ));
}

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$NetellerCopyWith<$Res> get neteller {
  
  return $NetellerCopyWith<$Res>(_self.neteller, (value) {
    return _then(_self.copyWith(neteller: value));
  });
}
}

/// @nodoc
@JsonSerializable()

class BankMetadata implements Metadata {
  const BankMetadata({@JsonKey(name: "transferType") required this.transferType, @JsonKey(name: "bankAccount") required this.bankAccount, final  String? $type}): $type = $type ?? 'bank';
  factory BankMetadata.fromJson(Map<String, dynamic> json) => _$BankMetadataFromJson(json);

@JsonKey(name: "transferType") final  String transferType;
@JsonKey(name: "bankAccount") final  BankAccount bankAccount;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BankMetadataCopyWith<BankMetadata> get copyWith => _$BankMetadataCopyWithImpl<BankMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BankMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BankMetadata&&(identical(other.transferType, transferType) || other.transferType == transferType)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,transferType,bankAccount);

@override
String toString() {
  return 'Metadata.bank(transferType: $transferType, bankAccount: $bankAccount)';
}


}

/// @nodoc
abstract mixin class $BankMetadataCopyWith<$Res> implements $MetadataCopyWith<$Res> {
  factory $BankMetadataCopyWith(BankMetadata value, $Res Function(BankMetadata) _then) = _$BankMetadataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "transferType") String transferType,@JsonKey(name: "bankAccount") BankAccount bankAccount
});


$BankAccountCopyWith<$Res> get bankAccount;

}
/// @nodoc
class _$BankMetadataCopyWithImpl<$Res>
    implements $BankMetadataCopyWith<$Res> {
  _$BankMetadataCopyWithImpl(this._self, this._then);

  final BankMetadata _self;
  final $Res Function(BankMetadata) _then;

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? transferType = null,Object? bankAccount = null,}) {
  return _then(BankMetadata(
transferType: null == transferType ? _self.transferType : transferType // ignore: cast_nullable_to_non_nullable
as String,bankAccount: null == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as BankAccount,
  ));
}

/// Create a copy of Metadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BankAccountCopyWith<$Res> get bankAccount {
  
  return $BankAccountCopyWith<$Res>(_self.bankAccount, (value) {
    return _then(_self.copyWith(bankAccount: value));
  });
}
}


/// @nodoc
mixin _$BankAccount {

@JsonKey(name: "id", includeIfNull: false) String? get id;@JsonKey(name: "country") String get country;@JsonKey(name: "accountHolder") String get accountHolder;@JsonKey(name: "bankName") String get bankName;@JsonKey(name: "accountNickname") String get accountNickname;@JsonKey(name: "branchName") String get branchName;@JsonKey(name: "swiftBic") String get swiftBic;@JsonKey(name: "iban") String get iban;
/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BankAccountCopyWith<BankAccount> get copyWith => _$BankAccountCopyWithImpl<BankAccount>(this as BankAccount, _$identity);

  /// Serializes this BankAccount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BankAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.country, country) || other.country == country)&&(identical(other.accountHolder, accountHolder) || other.accountHolder == accountHolder)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountNickname, accountNickname) || other.accountNickname == accountNickname)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.swiftBic, swiftBic) || other.swiftBic == swiftBic)&&(identical(other.iban, iban) || other.iban == iban));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,country,accountHolder,bankName,accountNickname,branchName,swiftBic,iban);

@override
String toString() {
  return 'BankAccount(id: $id, country: $country, accountHolder: $accountHolder, bankName: $bankName, accountNickname: $accountNickname, branchName: $branchName, swiftBic: $swiftBic, iban: $iban)';
}


}

/// @nodoc
abstract mixin class $BankAccountCopyWith<$Res>  {
  factory $BankAccountCopyWith(BankAccount value, $Res Function(BankAccount) _then) = _$BankAccountCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "id", includeIfNull: false) String? id,@JsonKey(name: "country") String country,@JsonKey(name: "accountHolder") String accountHolder,@JsonKey(name: "bankName") String bankName,@JsonKey(name: "accountNickname") String accountNickname,@JsonKey(name: "branchName") String branchName,@JsonKey(name: "swiftBic") String swiftBic,@JsonKey(name: "iban") String iban
});




}
/// @nodoc
class _$BankAccountCopyWithImpl<$Res>
    implements $BankAccountCopyWith<$Res> {
  _$BankAccountCopyWithImpl(this._self, this._then);

  final BankAccount _self;
  final $Res Function(BankAccount) _then;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? country = null,Object? accountHolder = null,Object? bankName = null,Object? accountNickname = null,Object? branchName = null,Object? swiftBic = null,Object? iban = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,accountHolder: null == accountHolder ? _self.accountHolder : accountHolder // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,accountNickname: null == accountNickname ? _self.accountNickname : accountNickname // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,swiftBic: null == swiftBic ? _self.swiftBic : swiftBic // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _BankAccount implements BankAccount {
  const _BankAccount({@JsonKey(name: "id", includeIfNull: false) this.id, @JsonKey(name: "country") required this.country, @JsonKey(name: "accountHolder") required this.accountHolder, @JsonKey(name: "bankName") required this.bankName, @JsonKey(name: "accountNickname") required this.accountNickname, @JsonKey(name: "branchName") required this.branchName, @JsonKey(name: "swiftBic") required this.swiftBic, @JsonKey(name: "iban") required this.iban});
  factory _BankAccount.fromJson(Map<String, dynamic> json) => _$BankAccountFromJson(json);

@override@JsonKey(name: "id", includeIfNull: false) final  String? id;
@override@JsonKey(name: "country") final  String country;
@override@JsonKey(name: "accountHolder") final  String accountHolder;
@override@JsonKey(name: "bankName") final  String bankName;
@override@JsonKey(name: "accountNickname") final  String accountNickname;
@override@JsonKey(name: "branchName") final  String branchName;
@override@JsonKey(name: "swiftBic") final  String swiftBic;
@override@JsonKey(name: "iban") final  String iban;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BankAccountCopyWith<_BankAccount> get copyWith => __$BankAccountCopyWithImpl<_BankAccount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BankAccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BankAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.country, country) || other.country == country)&&(identical(other.accountHolder, accountHolder) || other.accountHolder == accountHolder)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountNickname, accountNickname) || other.accountNickname == accountNickname)&&(identical(other.branchName, branchName) || other.branchName == branchName)&&(identical(other.swiftBic, swiftBic) || other.swiftBic == swiftBic)&&(identical(other.iban, iban) || other.iban == iban));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,country,accountHolder,bankName,accountNickname,branchName,swiftBic,iban);

@override
String toString() {
  return 'BankAccount(id: $id, country: $country, accountHolder: $accountHolder, bankName: $bankName, accountNickname: $accountNickname, branchName: $branchName, swiftBic: $swiftBic, iban: $iban)';
}


}

/// @nodoc
abstract mixin class _$BankAccountCopyWith<$Res> implements $BankAccountCopyWith<$Res> {
  factory _$BankAccountCopyWith(_BankAccount value, $Res Function(_BankAccount) _then) = __$BankAccountCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "id", includeIfNull: false) String? id,@JsonKey(name: "country") String country,@JsonKey(name: "accountHolder") String accountHolder,@JsonKey(name: "bankName") String bankName,@JsonKey(name: "accountNickname") String accountNickname,@JsonKey(name: "branchName") String branchName,@JsonKey(name: "swiftBic") String swiftBic,@JsonKey(name: "iban") String iban
});




}
/// @nodoc
class __$BankAccountCopyWithImpl<$Res>
    implements _$BankAccountCopyWith<$Res> {
  __$BankAccountCopyWithImpl(this._self, this._then);

  final _BankAccount _self;
  final $Res Function(_BankAccount) _then;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? country = null,Object? accountHolder = null,Object? bankName = null,Object? accountNickname = null,Object? branchName = null,Object? swiftBic = null,Object? iban = null,}) {
  return _then(_BankAccount(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,accountHolder: null == accountHolder ? _self.accountHolder : accountHolder // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,accountNickname: null == accountNickname ? _self.accountNickname : accountNickname // ignore: cast_nullable_to_non_nullable
as String,branchName: null == branchName ? _self.branchName : branchName // ignore: cast_nullable_to_non_nullable
as String,swiftBic: null == swiftBic ? _self.swiftBic : swiftBic // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Card {

@JsonKey(name: "id") String get id;
/// Create a copy of Card
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CardCopyWith<Card> get copyWith => _$CardCopyWithImpl<Card>(this as Card, _$identity);

  /// Serializes this Card to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Card&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'Card(id: $id)';
}


}

/// @nodoc
abstract mixin class $CardCopyWith<$Res>  {
  factory $CardCopyWith(Card value, $Res Function(Card) _then) = _$CardCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "id") String id
});




}
/// @nodoc
class _$CardCopyWithImpl<$Res>
    implements $CardCopyWith<$Res> {
  _$CardCopyWithImpl(this._self, this._then);

  final Card _self;
  final $Res Function(Card) _then;

/// Create a copy of Card
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Card implements Card {
  const _Card({@JsonKey(name: "id") required this.id});
  factory _Card.fromJson(Map<String, dynamic> json) => _$CardFromJson(json);

@override@JsonKey(name: "id") final  String id;

/// Create a copy of Card
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CardCopyWith<_Card> get copyWith => __$CardCopyWithImpl<_Card>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CardToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Card&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'Card(id: $id)';
}


}

/// @nodoc
abstract mixin class _$CardCopyWith<$Res> implements $CardCopyWith<$Res> {
  factory _$CardCopyWith(_Card value, $Res Function(_Card) _then) = __$CardCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "id") String id
});




}
/// @nodoc
class __$CardCopyWithImpl<$Res>
    implements _$CardCopyWith<$Res> {
  __$CardCopyWithImpl(this._self, this._then);

  final _Card _self;
  final $Res Function(_Card) _then;

/// Create a copy of Card
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(_Card(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Skrill {

@JsonKey(name: "email") String get email;
/// Create a copy of Skrill
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SkrillCopyWith<Skrill> get copyWith => _$SkrillCopyWithImpl<Skrill>(this as Skrill, _$identity);

  /// Serializes this Skrill to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Skrill&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'Skrill(email: $email)';
}


}

/// @nodoc
abstract mixin class $SkrillCopyWith<$Res>  {
  factory $SkrillCopyWith(Skrill value, $Res Function(Skrill) _then) = _$SkrillCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "email") String email
});




}
/// @nodoc
class _$SkrillCopyWithImpl<$Res>
    implements $SkrillCopyWith<$Res> {
  _$SkrillCopyWithImpl(this._self, this._then);

  final Skrill _self;
  final $Res Function(Skrill) _then;

/// Create a copy of Skrill
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Skrill implements Skrill {
  const _Skrill({@JsonKey(name: "email") required this.email});
  factory _Skrill.fromJson(Map<String, dynamic> json) => _$SkrillFromJson(json);

@override@JsonKey(name: "email") final  String email;

/// Create a copy of Skrill
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SkrillCopyWith<_Skrill> get copyWith => __$SkrillCopyWithImpl<_Skrill>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SkrillToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Skrill&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'Skrill(email: $email)';
}


}

/// @nodoc
abstract mixin class _$SkrillCopyWith<$Res> implements $SkrillCopyWith<$Res> {
  factory _$SkrillCopyWith(_Skrill value, $Res Function(_Skrill) _then) = __$SkrillCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "email") String email
});




}
/// @nodoc
class __$SkrillCopyWithImpl<$Res>
    implements _$SkrillCopyWith<$Res> {
  __$SkrillCopyWithImpl(this._self, this._then);

  final _Skrill _self;
  final $Res Function(_Skrill) _then;

/// Create a copy of Skrill
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,}) {
  return _then(_Skrill(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Neteller {

@JsonKey(name: "email") String get email;
/// Create a copy of Neteller
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetellerCopyWith<Neteller> get copyWith => _$NetellerCopyWithImpl<Neteller>(this as Neteller, _$identity);

  /// Serializes this Neteller to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Neteller&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'Neteller(email: $email)';
}


}

/// @nodoc
abstract mixin class $NetellerCopyWith<$Res>  {
  factory $NetellerCopyWith(Neteller value, $Res Function(Neteller) _then) = _$NetellerCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "email") String email
});




}
/// @nodoc
class _$NetellerCopyWithImpl<$Res>
    implements $NetellerCopyWith<$Res> {
  _$NetellerCopyWithImpl(this._self, this._then);

  final Neteller _self;
  final $Res Function(Neteller) _then;

/// Create a copy of Neteller
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Neteller implements Neteller {
  const _Neteller({@JsonKey(name: "email") required this.email});
  factory _Neteller.fromJson(Map<String, dynamic> json) => _$NetellerFromJson(json);

@override@JsonKey(name: "email") final  String email;

/// Create a copy of Neteller
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetellerCopyWith<_Neteller> get copyWith => __$NetellerCopyWithImpl<_Neteller>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NetellerToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Neteller&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'Neteller(email: $email)';
}


}

/// @nodoc
abstract mixin class _$NetellerCopyWith<$Res> implements $NetellerCopyWith<$Res> {
  factory _$NetellerCopyWith(_Neteller value, $Res Function(_Neteller) _then) = __$NetellerCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "email") String email
});




}
/// @nodoc
class __$NetellerCopyWithImpl<$Res>
    implements _$NetellerCopyWith<$Res> {
  __$NetellerCopyWithImpl(this._self, this._then);

  final _Neteller _self;
  final $Res Function(_Neteller) _then;

/// Create a copy of Neteller
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,}) {
  return _then(_Neteller(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
