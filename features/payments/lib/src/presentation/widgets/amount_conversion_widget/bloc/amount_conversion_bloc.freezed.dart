// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'amount_conversion_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AmountConversionEvent {

 String get accountCurrency; String? get targetCurrency; Object? get amount; List<CurrencyAmountDetail> get currencyAmountDetails;
/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AmountConversionEventCopyWith<AmountConversionEvent> get copyWith => _$AmountConversionEventCopyWithImpl<AmountConversionEvent>(this as AmountConversionEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionEvent&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&const DeepCollectionEquality().equals(other.amount, amount)&&const DeepCollectionEquality().equals(other.currencyAmountDetails, currencyAmountDetails));
}


@override
int get hashCode => Object.hash(runtimeType,accountCurrency,targetCurrency,const DeepCollectionEquality().hash(amount),const DeepCollectionEquality().hash(currencyAmountDetails));

@override
String toString() {
  return 'AmountConversionEvent(accountCurrency: $accountCurrency, targetCurrency: $targetCurrency, amount: $amount, currencyAmountDetails: $currencyAmountDetails)';
}


}

/// @nodoc
abstract mixin class $AmountConversionEventCopyWith<$Res>  {
  factory $AmountConversionEventCopyWith(AmountConversionEvent value, $Res Function(AmountConversionEvent) _then) = _$AmountConversionEventCopyWithImpl;
@useResult
$Res call({
 String accountCurrency, String targetCurrency, List<CurrencyAmountDetail> currencyAmountDetails
});




}
/// @nodoc
class _$AmountConversionEventCopyWithImpl<$Res>
    implements $AmountConversionEventCopyWith<$Res> {
  _$AmountConversionEventCopyWithImpl(this._self, this._then);

  final AmountConversionEvent _self;
  final $Res Function(AmountConversionEvent) _then;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountCurrency = null,Object? targetCurrency = null,Object? currencyAmountDetails = null,}) {
  return _then(_self.copyWith(
accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency! : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,currencyAmountDetails: null == currencyAmountDetails ? _self.currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>,
  ));
}

}


/// @nodoc


class _GetConversionRate implements AmountConversionEvent {
  const _GetConversionRate({required this.accountCurrency, required this.targetCurrency, required this.paymentType, required this.amount, required this.conversionType, this.amountCallback, required final  List<CurrencyAmountDetail> currencyAmountDetails}): _currencyAmountDetails = currencyAmountDetails;
  

@override final  String accountCurrency;
@override final  String targetCurrency;
 final  PaymentType paymentType;
@override final  String amount;
 final  ConversionType conversionType;
 final  void Function(double amount)? amountCallback;
 final  List<CurrencyAmountDetail> _currencyAmountDetails;
@override List<CurrencyAmountDetail> get currencyAmountDetails {
  if (_currencyAmountDetails is EqualUnmodifiableListView) return _currencyAmountDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencyAmountDetails);
}


/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetConversionRateCopyWith<_GetConversionRate> get copyWith => __$GetConversionRateCopyWithImpl<_GetConversionRate>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetConversionRate&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.conversionType, conversionType) || other.conversionType == conversionType)&&(identical(other.amountCallback, amountCallback) || other.amountCallback == amountCallback)&&const DeepCollectionEquality().equals(other._currencyAmountDetails, _currencyAmountDetails));
}


@override
int get hashCode => Object.hash(runtimeType,accountCurrency,targetCurrency,paymentType,amount,conversionType,amountCallback,const DeepCollectionEquality().hash(_currencyAmountDetails));

@override
String toString() {
  return 'AmountConversionEvent.getConversionRate(accountCurrency: $accountCurrency, targetCurrency: $targetCurrency, paymentType: $paymentType, amount: $amount, conversionType: $conversionType, amountCallback: $amountCallback, currencyAmountDetails: $currencyAmountDetails)';
}


}

/// @nodoc
abstract mixin class _$GetConversionRateCopyWith<$Res> implements $AmountConversionEventCopyWith<$Res> {
  factory _$GetConversionRateCopyWith(_GetConversionRate value, $Res Function(_GetConversionRate) _then) = __$GetConversionRateCopyWithImpl;
@override @useResult
$Res call({
 String accountCurrency, String targetCurrency, PaymentType paymentType, String amount, ConversionType conversionType, void Function(double amount)? amountCallback, List<CurrencyAmountDetail> currencyAmountDetails
});




}
/// @nodoc
class __$GetConversionRateCopyWithImpl<$Res>
    implements _$GetConversionRateCopyWith<$Res> {
  __$GetConversionRateCopyWithImpl(this._self, this._then);

  final _GetConversionRate _self;
  final $Res Function(_GetConversionRate) _then;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountCurrency = null,Object? targetCurrency = null,Object? paymentType = null,Object? amount = null,Object? conversionType = null,Object? amountCallback = freezed,Object? currencyAmountDetails = null,}) {
  return _then(_GetConversionRate(
accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as PaymentType,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,conversionType: null == conversionType ? _self.conversionType : conversionType // ignore: cast_nullable_to_non_nullable
as ConversionType,amountCallback: freezed == amountCallback ? _self.amountCallback : amountCallback // ignore: cast_nullable_to_non_nullable
as void Function(double amount)?,currencyAmountDetails: null == currencyAmountDetails ? _self._currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>,
  ));
}


}

/// @nodoc


class _GetCurrencyDetails implements AmountConversionEvent {
  const _GetCurrencyDetails({required final  List<CurrencyAmountDetail> currencyAmountDetails, required this.accountCurrency, required this.isHomeCurrency, required this.isStartWithConversionRate, required this.paymentType, this.targetCurrency, this.amount}): _currencyAmountDetails = currencyAmountDetails;
  

 final  List<CurrencyAmountDetail> _currencyAmountDetails;
@override List<CurrencyAmountDetail> get currencyAmountDetails {
  if (_currencyAmountDetails is EqualUnmodifiableListView) return _currencyAmountDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencyAmountDetails);
}

@override final  String accountCurrency;
 final  bool isHomeCurrency;
 final  bool isStartWithConversionRate;
 final  PaymentType paymentType;
@override final  String? targetCurrency;
@override final  String? amount;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetCurrencyDetailsCopyWith<_GetCurrencyDetails> get copyWith => __$GetCurrencyDetailsCopyWithImpl<_GetCurrencyDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetCurrencyDetails&&const DeepCollectionEquality().equals(other._currencyAmountDetails, _currencyAmountDetails)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.isHomeCurrency, isHomeCurrency) || other.isHomeCurrency == isHomeCurrency)&&(identical(other.isStartWithConversionRate, isStartWithConversionRate) || other.isStartWithConversionRate == isStartWithConversionRate)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.amount, amount) || other.amount == amount));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_currencyAmountDetails),accountCurrency,isHomeCurrency,isStartWithConversionRate,paymentType,targetCurrency,amount);

@override
String toString() {
  return 'AmountConversionEvent.getCurrencyDetails(currencyAmountDetails: $currencyAmountDetails, accountCurrency: $accountCurrency, isHomeCurrency: $isHomeCurrency, isStartWithConversionRate: $isStartWithConversionRate, paymentType: $paymentType, targetCurrency: $targetCurrency, amount: $amount)';
}


}

/// @nodoc
abstract mixin class _$GetCurrencyDetailsCopyWith<$Res> implements $AmountConversionEventCopyWith<$Res> {
  factory _$GetCurrencyDetailsCopyWith(_GetCurrencyDetails value, $Res Function(_GetCurrencyDetails) _then) = __$GetCurrencyDetailsCopyWithImpl;
@override @useResult
$Res call({
 List<CurrencyAmountDetail> currencyAmountDetails, String accountCurrency, bool isHomeCurrency, bool isStartWithConversionRate, PaymentType paymentType, String? targetCurrency, String? amount
});




}
/// @nodoc
class __$GetCurrencyDetailsCopyWithImpl<$Res>
    implements _$GetCurrencyDetailsCopyWith<$Res> {
  __$GetCurrencyDetailsCopyWithImpl(this._self, this._then);

  final _GetCurrencyDetails _self;
  final $Res Function(_GetCurrencyDetails) _then;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currencyAmountDetails = null,Object? accountCurrency = null,Object? isHomeCurrency = null,Object? isStartWithConversionRate = null,Object? paymentType = null,Object? targetCurrency = freezed,Object? amount = freezed,}) {
  return _then(_GetCurrencyDetails(
currencyAmountDetails: null == currencyAmountDetails ? _self._currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,isHomeCurrency: null == isHomeCurrency ? _self.isHomeCurrency : isHomeCurrency // ignore: cast_nullable_to_non_nullable
as bool,isStartWithConversionRate: null == isStartWithConversionRate ? _self.isStartWithConversionRate : isStartWithConversionRate // ignore: cast_nullable_to_non_nullable
as bool,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as PaymentType,targetCurrency: freezed == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String?,amount: freezed == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _UpdateTransferAmount implements AmountConversionEvent {
  const _UpdateTransferAmount({required this.amount, this.isEmptyField = false, this.amountCallback, this.conversionRateData, required this.conversionType, required this.accountCurrency, required this.targetCurrency, required final  List<CurrencyAmountDetail> currencyAmountDetails}): _currencyAmountDetails = currencyAmountDetails;
  

@override final  double amount;
@JsonKey() final  bool isEmptyField;
 final  void Function(double amount)? amountCallback;
 final  ConversionRateModel? conversionRateData;
 final  ConversionType conversionType;
@override final  String accountCurrency;
@override final  String targetCurrency;
 final  List<CurrencyAmountDetail> _currencyAmountDetails;
@override List<CurrencyAmountDetail> get currencyAmountDetails {
  if (_currencyAmountDetails is EqualUnmodifiableListView) return _currencyAmountDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencyAmountDetails);
}


/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateTransferAmountCopyWith<_UpdateTransferAmount> get copyWith => __$UpdateTransferAmountCopyWithImpl<_UpdateTransferAmount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateTransferAmount&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.isEmptyField, isEmptyField) || other.isEmptyField == isEmptyField)&&(identical(other.amountCallback, amountCallback) || other.amountCallback == amountCallback)&&(identical(other.conversionRateData, conversionRateData) || other.conversionRateData == conversionRateData)&&(identical(other.conversionType, conversionType) || other.conversionType == conversionType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&const DeepCollectionEquality().equals(other._currencyAmountDetails, _currencyAmountDetails));
}


@override
int get hashCode => Object.hash(runtimeType,amount,isEmptyField,amountCallback,conversionRateData,conversionType,accountCurrency,targetCurrency,const DeepCollectionEquality().hash(_currencyAmountDetails));

@override
String toString() {
  return 'AmountConversionEvent.updateTransferAmount(amount: $amount, isEmptyField: $isEmptyField, amountCallback: $amountCallback, conversionRateData: $conversionRateData, conversionType: $conversionType, accountCurrency: $accountCurrency, targetCurrency: $targetCurrency, currencyAmountDetails: $currencyAmountDetails)';
}


}

/// @nodoc
abstract mixin class _$UpdateTransferAmountCopyWith<$Res> implements $AmountConversionEventCopyWith<$Res> {
  factory _$UpdateTransferAmountCopyWith(_UpdateTransferAmount value, $Res Function(_UpdateTransferAmount) _then) = __$UpdateTransferAmountCopyWithImpl;
@override @useResult
$Res call({
 double amount, bool isEmptyField, void Function(double amount)? amountCallback, ConversionRateModel? conversionRateData, ConversionType conversionType, String accountCurrency, String targetCurrency, List<CurrencyAmountDetail> currencyAmountDetails
});


$ConversionRateModelCopyWith<$Res>? get conversionRateData;

}
/// @nodoc
class __$UpdateTransferAmountCopyWithImpl<$Res>
    implements _$UpdateTransferAmountCopyWith<$Res> {
  __$UpdateTransferAmountCopyWithImpl(this._self, this._then);

  final _UpdateTransferAmount _self;
  final $Res Function(_UpdateTransferAmount) _then;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? isEmptyField = null,Object? amountCallback = freezed,Object? conversionRateData = freezed,Object? conversionType = null,Object? accountCurrency = null,Object? targetCurrency = null,Object? currencyAmountDetails = null,}) {
  return _then(_UpdateTransferAmount(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,isEmptyField: null == isEmptyField ? _self.isEmptyField : isEmptyField // ignore: cast_nullable_to_non_nullable
as bool,amountCallback: freezed == amountCallback ? _self.amountCallback : amountCallback // ignore: cast_nullable_to_non_nullable
as void Function(double amount)?,conversionRateData: freezed == conversionRateData ? _self.conversionRateData : conversionRateData // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,conversionType: null == conversionType ? _self.conversionType : conversionType // ignore: cast_nullable_to_non_nullable
as ConversionType,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,currencyAmountDetails: null == currencyAmountDetails ? _self._currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>,
  ));
}

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateData {
    if (_self.conversionRateData == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateData!, (value) {
    return _then(_self.copyWith(conversionRateData: value));
  });
}
}

/// @nodoc


class _UpdateConvertedAmount implements AmountConversionEvent {
  const _UpdateConvertedAmount({required this.amount, this.isEmptyField = false, this.amountCallback, this.conversionRateData, required this.conversionType, required this.accountCurrency, required this.targetCurrency, required final  List<CurrencyAmountDetail> currencyAmountDetails}): _currencyAmountDetails = currencyAmountDetails;
  

@override final  double amount;
@JsonKey() final  bool isEmptyField;
 final  void Function(double amount)? amountCallback;
 final  ConversionRateModel? conversionRateData;
 final  ConversionType conversionType;
@override final  String accountCurrency;
@override final  String targetCurrency;
 final  List<CurrencyAmountDetail> _currencyAmountDetails;
@override List<CurrencyAmountDetail> get currencyAmountDetails {
  if (_currencyAmountDetails is EqualUnmodifiableListView) return _currencyAmountDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_currencyAmountDetails);
}


/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateConvertedAmountCopyWith<_UpdateConvertedAmount> get copyWith => __$UpdateConvertedAmountCopyWithImpl<_UpdateConvertedAmount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateConvertedAmount&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.isEmptyField, isEmptyField) || other.isEmptyField == isEmptyField)&&(identical(other.amountCallback, amountCallback) || other.amountCallback == amountCallback)&&(identical(other.conversionRateData, conversionRateData) || other.conversionRateData == conversionRateData)&&(identical(other.conversionType, conversionType) || other.conversionType == conversionType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&const DeepCollectionEquality().equals(other._currencyAmountDetails, _currencyAmountDetails));
}


@override
int get hashCode => Object.hash(runtimeType,amount,isEmptyField,amountCallback,conversionRateData,conversionType,accountCurrency,targetCurrency,const DeepCollectionEquality().hash(_currencyAmountDetails));

@override
String toString() {
  return 'AmountConversionEvent.updateConvertedAmount(amount: $amount, isEmptyField: $isEmptyField, amountCallback: $amountCallback, conversionRateData: $conversionRateData, conversionType: $conversionType, accountCurrency: $accountCurrency, targetCurrency: $targetCurrency, currencyAmountDetails: $currencyAmountDetails)';
}


}

/// @nodoc
abstract mixin class _$UpdateConvertedAmountCopyWith<$Res> implements $AmountConversionEventCopyWith<$Res> {
  factory _$UpdateConvertedAmountCopyWith(_UpdateConvertedAmount value, $Res Function(_UpdateConvertedAmount) _then) = __$UpdateConvertedAmountCopyWithImpl;
@override @useResult
$Res call({
 double amount, bool isEmptyField, void Function(double amount)? amountCallback, ConversionRateModel? conversionRateData, ConversionType conversionType, String accountCurrency, String targetCurrency, List<CurrencyAmountDetail> currencyAmountDetails
});


$ConversionRateModelCopyWith<$Res>? get conversionRateData;

}
/// @nodoc
class __$UpdateConvertedAmountCopyWithImpl<$Res>
    implements _$UpdateConvertedAmountCopyWith<$Res> {
  __$UpdateConvertedAmountCopyWithImpl(this._self, this._then);

  final _UpdateConvertedAmount _self;
  final $Res Function(_UpdateConvertedAmount) _then;

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? isEmptyField = null,Object? amountCallback = freezed,Object? conversionRateData = freezed,Object? conversionType = null,Object? accountCurrency = null,Object? targetCurrency = null,Object? currencyAmountDetails = null,}) {
  return _then(_UpdateConvertedAmount(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,isEmptyField: null == isEmptyField ? _self.isEmptyField : isEmptyField // ignore: cast_nullable_to_non_nullable
as bool,amountCallback: freezed == amountCallback ? _self.amountCallback : amountCallback // ignore: cast_nullable_to_non_nullable
as void Function(double amount)?,conversionRateData: freezed == conversionRateData ? _self.conversionRateData : conversionRateData // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,conversionType: null == conversionType ? _self.conversionType : conversionType // ignore: cast_nullable_to_non_nullable
as ConversionType,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,currencyAmountDetails: null == currencyAmountDetails ? _self._currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>,
  ));
}

/// Create a copy of AmountConversionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateData {
    if (_self.conversionRateData == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateData!, (value) {
    return _then(_self.copyWith(conversionRateData: value));
  });
}
}

/// @nodoc
mixin _$AmountConversionState {

 AmountConversionProcessState get processState; ConversionRateModel? get conversionRateData; String? get errorMessage; bool? get isValidAmount; AmountValidationError get validationError; bool get doesNeedConversion; String? get convertedCurrency; CurrencyAmountDetail? get homeCurrency; double? get transferAmount; double? get convertedAmount; PaymentType? get paymentType; String? get conversionRateString;
/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AmountConversionStateCopyWith<AmountConversionState> get copyWith => _$AmountConversionStateCopyWithImpl<AmountConversionState>(this as AmountConversionState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.conversionRateData, conversionRateData) || other.conversionRateData == conversionRateData)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isValidAmount, isValidAmount) || other.isValidAmount == isValidAmount)&&(identical(other.validationError, validationError) || other.validationError == validationError)&&(identical(other.doesNeedConversion, doesNeedConversion) || other.doesNeedConversion == doesNeedConversion)&&(identical(other.convertedCurrency, convertedCurrency) || other.convertedCurrency == convertedCurrency)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.transferAmount, transferAmount) || other.transferAmount == transferAmount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString));
}


@override
int get hashCode => Object.hash(runtimeType,processState,conversionRateData,errorMessage,isValidAmount,validationError,doesNeedConversion,convertedCurrency,homeCurrency,transferAmount,convertedAmount,paymentType,conversionRateString);

@override
String toString() {
  return 'AmountConversionState(processState: $processState, conversionRateData: $conversionRateData, errorMessage: $errorMessage, isValidAmount: $isValidAmount, validationError: $validationError, doesNeedConversion: $doesNeedConversion, convertedCurrency: $convertedCurrency, homeCurrency: $homeCurrency, transferAmount: $transferAmount, convertedAmount: $convertedAmount, paymentType: $paymentType, conversionRateString: $conversionRateString)';
}


}

/// @nodoc
abstract mixin class $AmountConversionStateCopyWith<$Res>  {
  factory $AmountConversionStateCopyWith(AmountConversionState value, $Res Function(AmountConversionState) _then) = _$AmountConversionStateCopyWithImpl;
@useResult
$Res call({
 AmountConversionProcessState processState, ConversionRateModel? conversionRateData, String? errorMessage, bool? isValidAmount, AmountValidationError validationError, bool doesNeedConversion, String? convertedCurrency, CurrencyAmountDetail? homeCurrency, double? transferAmount, double? convertedAmount, PaymentType? paymentType, String? conversionRateString
});


$AmountConversionProcessStateCopyWith<$Res> get processState;$ConversionRateModelCopyWith<$Res>? get conversionRateData;$CurrencyAmountDetailCopyWith<$Res>? get homeCurrency;

}
/// @nodoc
class _$AmountConversionStateCopyWithImpl<$Res>
    implements $AmountConversionStateCopyWith<$Res> {
  _$AmountConversionStateCopyWithImpl(this._self, this._then);

  final AmountConversionState _self;
  final $Res Function(AmountConversionState) _then;

/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? conversionRateData = freezed,Object? errorMessage = freezed,Object? isValidAmount = freezed,Object? validationError = null,Object? doesNeedConversion = null,Object? convertedCurrency = freezed,Object? homeCurrency = freezed,Object? transferAmount = freezed,Object? convertedAmount = freezed,Object? paymentType = freezed,Object? conversionRateString = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AmountConversionProcessState,conversionRateData: freezed == conversionRateData ? _self.conversionRateData : conversionRateData // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isValidAmount: freezed == isValidAmount ? _self.isValidAmount : isValidAmount // ignore: cast_nullable_to_non_nullable
as bool?,validationError: null == validationError ? _self.validationError : validationError // ignore: cast_nullable_to_non_nullable
as AmountValidationError,doesNeedConversion: null == doesNeedConversion ? _self.doesNeedConversion : doesNeedConversion // ignore: cast_nullable_to_non_nullable
as bool,convertedCurrency: freezed == convertedCurrency ? _self.convertedCurrency : convertedCurrency // ignore: cast_nullable_to_non_nullable
as String?,homeCurrency: freezed == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as CurrencyAmountDetail?,transferAmount: freezed == transferAmount ? _self.transferAmount : transferAmount // ignore: cast_nullable_to_non_nullable
as double?,convertedAmount: freezed == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as double?,paymentType: freezed == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as PaymentType?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AmountConversionProcessStateCopyWith<$Res> get processState {
  
  return $AmountConversionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateData {
    if (_self.conversionRateData == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateData!, (value) {
    return _then(_self.copyWith(conversionRateData: value));
  });
}/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CurrencyAmountDetailCopyWith<$Res>? get homeCurrency {
    if (_self.homeCurrency == null) {
    return null;
  }

  return $CurrencyAmountDetailCopyWith<$Res>(_self.homeCurrency!, (value) {
    return _then(_self.copyWith(homeCurrency: value));
  });
}
}


/// @nodoc


class _AmountConversionState implements AmountConversionState {
  const _AmountConversionState({this.processState = const AmountConversionProcessState.loading(), this.conversionRateData, this.errorMessage, this.isValidAmount, this.validationError = AmountValidationError.none, this.doesNeedConversion = false, this.convertedCurrency, this.homeCurrency, this.transferAmount, this.convertedAmount, this.paymentType, this.conversionRateString});
  

@override@JsonKey() final  AmountConversionProcessState processState;
@override final  ConversionRateModel? conversionRateData;
@override final  String? errorMessage;
@override final  bool? isValidAmount;
@override@JsonKey() final  AmountValidationError validationError;
@override@JsonKey() final  bool doesNeedConversion;
@override final  String? convertedCurrency;
@override final  CurrencyAmountDetail? homeCurrency;
@override final  double? transferAmount;
@override final  double? convertedAmount;
@override final  PaymentType? paymentType;
@override final  String? conversionRateString;

/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AmountConversionStateCopyWith<_AmountConversionState> get copyWith => __$AmountConversionStateCopyWithImpl<_AmountConversionState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AmountConversionState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.conversionRateData, conversionRateData) || other.conversionRateData == conversionRateData)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isValidAmount, isValidAmount) || other.isValidAmount == isValidAmount)&&(identical(other.validationError, validationError) || other.validationError == validationError)&&(identical(other.doesNeedConversion, doesNeedConversion) || other.doesNeedConversion == doesNeedConversion)&&(identical(other.convertedCurrency, convertedCurrency) || other.convertedCurrency == convertedCurrency)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.transferAmount, transferAmount) || other.transferAmount == transferAmount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString));
}


@override
int get hashCode => Object.hash(runtimeType,processState,conversionRateData,errorMessage,isValidAmount,validationError,doesNeedConversion,convertedCurrency,homeCurrency,transferAmount,convertedAmount,paymentType,conversionRateString);

@override
String toString() {
  return 'AmountConversionState(processState: $processState, conversionRateData: $conversionRateData, errorMessage: $errorMessage, isValidAmount: $isValidAmount, validationError: $validationError, doesNeedConversion: $doesNeedConversion, convertedCurrency: $convertedCurrency, homeCurrency: $homeCurrency, transferAmount: $transferAmount, convertedAmount: $convertedAmount, paymentType: $paymentType, conversionRateString: $conversionRateString)';
}


}

/// @nodoc
abstract mixin class _$AmountConversionStateCopyWith<$Res> implements $AmountConversionStateCopyWith<$Res> {
  factory _$AmountConversionStateCopyWith(_AmountConversionState value, $Res Function(_AmountConversionState) _then) = __$AmountConversionStateCopyWithImpl;
@override @useResult
$Res call({
 AmountConversionProcessState processState, ConversionRateModel? conversionRateData, String? errorMessage, bool? isValidAmount, AmountValidationError validationError, bool doesNeedConversion, String? convertedCurrency, CurrencyAmountDetail? homeCurrency, double? transferAmount, double? convertedAmount, PaymentType? paymentType, String? conversionRateString
});


@override $AmountConversionProcessStateCopyWith<$Res> get processState;@override $ConversionRateModelCopyWith<$Res>? get conversionRateData;@override $CurrencyAmountDetailCopyWith<$Res>? get homeCurrency;

}
/// @nodoc
class __$AmountConversionStateCopyWithImpl<$Res>
    implements _$AmountConversionStateCopyWith<$Res> {
  __$AmountConversionStateCopyWithImpl(this._self, this._then);

  final _AmountConversionState _self;
  final $Res Function(_AmountConversionState) _then;

/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? conversionRateData = freezed,Object? errorMessage = freezed,Object? isValidAmount = freezed,Object? validationError = null,Object? doesNeedConversion = null,Object? convertedCurrency = freezed,Object? homeCurrency = freezed,Object? transferAmount = freezed,Object? convertedAmount = freezed,Object? paymentType = freezed,Object? conversionRateString = freezed,}) {
  return _then(_AmountConversionState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AmountConversionProcessState,conversionRateData: freezed == conversionRateData ? _self.conversionRateData : conversionRateData // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isValidAmount: freezed == isValidAmount ? _self.isValidAmount : isValidAmount // ignore: cast_nullable_to_non_nullable
as bool?,validationError: null == validationError ? _self.validationError : validationError // ignore: cast_nullable_to_non_nullable
as AmountValidationError,doesNeedConversion: null == doesNeedConversion ? _self.doesNeedConversion : doesNeedConversion // ignore: cast_nullable_to_non_nullable
as bool,convertedCurrency: freezed == convertedCurrency ? _self.convertedCurrency : convertedCurrency // ignore: cast_nullable_to_non_nullable
as String?,homeCurrency: freezed == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as CurrencyAmountDetail?,transferAmount: freezed == transferAmount ? _self.transferAmount : transferAmount // ignore: cast_nullable_to_non_nullable
as double?,convertedAmount: freezed == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as double?,paymentType: freezed == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as PaymentType?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AmountConversionProcessStateCopyWith<$Res> get processState {
  
  return $AmountConversionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateData {
    if (_self.conversionRateData == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateData!, (value) {
    return _then(_self.copyWith(conversionRateData: value));
  });
}/// Create a copy of AmountConversionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CurrencyAmountDetailCopyWith<$Res>? get homeCurrency {
    if (_self.homeCurrency == null) {
    return null;
  }

  return $CurrencyAmountDetailCopyWith<$Res>(_self.homeCurrency!, (value) {
    return _then(_self.copyWith(homeCurrency: value));
  });
}
}

/// @nodoc
mixin _$AmountConversionProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AmountConversionProcessState()';
}


}

/// @nodoc
class $AmountConversionProcessStateCopyWith<$Res>  {
$AmountConversionProcessStateCopyWith(AmountConversionProcessState _, $Res Function(AmountConversionProcessState) __);
}


/// @nodoc


class AmountConversionLoadingProcessState implements AmountConversionProcessState {
  const AmountConversionLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AmountConversionProcessState.loading()';
}


}




/// @nodoc


class AmountConversionSuccessProcessState implements AmountConversionProcessState {
  const AmountConversionSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AmountConversionProcessState.success()';
}


}




/// @nodoc


class AmountConversionErrorProcessState implements AmountConversionProcessState {
  const AmountConversionErrorProcessState({this.errorMessage});
  

 final  String? errorMessage;

/// Create a copy of AmountConversionProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AmountConversionErrorProcessStateCopyWith<AmountConversionErrorProcessState> get copyWith => _$AmountConversionErrorProcessStateCopyWithImpl<AmountConversionErrorProcessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AmountConversionErrorProcessState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'AmountConversionProcessState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $AmountConversionErrorProcessStateCopyWith<$Res> implements $AmountConversionProcessStateCopyWith<$Res> {
  factory $AmountConversionErrorProcessStateCopyWith(AmountConversionErrorProcessState value, $Res Function(AmountConversionErrorProcessState) _then) = _$AmountConversionErrorProcessStateCopyWithImpl;
@useResult
$Res call({
 String? errorMessage
});




}
/// @nodoc
class _$AmountConversionErrorProcessStateCopyWithImpl<$Res>
    implements $AmountConversionErrorProcessStateCopyWith<$Res> {
  _$AmountConversionErrorProcessStateCopyWithImpl(this._self, this._then);

  final AmountConversionErrorProcessState _self;
  final $Res Function(AmountConversionErrorProcessState) _then;

/// Create a copy of AmountConversionProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = freezed,}) {
  return _then(AmountConversionErrorProcessState(
errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
