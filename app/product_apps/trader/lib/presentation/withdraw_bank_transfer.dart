import 'package:api_client/api_client.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';

import '../di/di_initializer.dart';

DisplayableComponent withdrawBankTransfer() {
  return DisplayableComponent(
    title: 'withdraw Bank Transfer',
    children: [
      DisplayableComponent(
        title: 'withdraw Bank Transfer',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/v1/client-profiles/trader_user_registration_id?brokerId=trader_broker_id':
                  [
                    MockResponse(
                      bodyFilePath:
                          'resources/mocks/client_profile/success.json',
                    ),
                  ],
              'api/v1/client/bankAccounts': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/withdraw_bank_transfer/success.json',
                ),
              ],
              'api/v1/client/bankAccount/e988722f-8375-12b8-02db-684ab039d85d':
                  [
                    MockResponse(
                      bodyFilePath:
                          'resources/mocks/withdraw_bank_transfer/success.json',
                    ),
                  ],
            });
          return WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000, // Add test account balance
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'withdraw Bank Transfer Failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/v1/client/bankAccounts': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/withdraw_bank_transfer/failure.json',
                ),
              ],
            });
          return WithdrawBankTransfer(
            tradingAccountId: '112344',
            bankTransferAmountModel: WithdrawFlowParams(
              tradingAccountId: '1234',
              paymentType: WithdrawalMop.bank,
              accountCurrency: 'Usd',
              currency: 'Usd',
              amount: 233,
              convertedAmount: 233,
              conversionRate: 32,
              conversionRateString: '32',
              accountBalance: 1000, // Add test account balance
            ),
          );
        },
      ),
    ],
  );
}
