// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/market_success_scenario.dart';
import 'scenarios/categories_failure_scenario.dart';
import 'scenarios/linked_symbols_failure_scenario.dart';
import 'scenarios/product_hub_failure_scenario.dart';
import 'scenarios/product_hub_empty_scenario.dart';
import 'scenarios/linked_symbols_empty_scenario.dart';
import 'scenarios/categories_empty_scenario.dart';
import 'package:equiti_test/equiti_test.dart';
import 'scenarios/linked_symbols_descending_success_scenario.dart';
import 'package:e_trader/src/presentation/symbols/symbols_screen.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';
import './step/the_app_runs_with_configuration.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_widget_test/step/i_tap_text.dart';
import 'package:bdd_widget_test/step/i_see_text.dart';
import './step/i_select_sort_option.dart';
import './step/the_configuration_is_updated_to.dart';
import './step/i_wait_for_seconds.dart';
import './step/i_tap_on_the_duplosearchbox.dart';
import './step/i_enter_in_the_search_field.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Market Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: User views Market screen (scenarios: [marketSuccessScenario], 'market_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [marketSuccessScenario], 'market_success')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [marketSuccessScenario],
          );
          await screenshotVerified(tester, 'market_success');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [marketSuccessScenario], 'market_success')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [categoriesFailureScenario], 'categories_failure')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [categoriesFailureScenario], 'categories_failure')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [categoriesFailureScenario],
          );
          await screenshotVerified(tester, 'categories_failure');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [categoriesFailureScenario], 'categories_failure')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [linkedSymbolsFailureScenario], 'linked_symbols_failure')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [linkedSymbolsFailureScenario], 'linked_symbols_failure')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [linkedSymbolsFailureScenario],
          );
          await screenshotVerified(tester, 'linked_symbols_failure');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [linkedSymbolsFailureScenario], 'linked_symbols_failure')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [productHubFailureScenario], 'product_hub_failure')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [productHubFailureScenario], 'product_hub_failure')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [productHubFailureScenario],
          );
          await screenshotVerified(tester, 'product_hub_failure');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [productHubFailureScenario], 'product_hub_failure')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [productHubEmptyScenario], 'product_hub_empty')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [productHubEmptyScenario], 'product_hub_empty')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [productHubEmptyScenario],
          );
          await screenshotVerified(tester, 'product_hub_empty');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [productHubEmptyScenario], 'product_hub_empty')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [categoriesEmptyScenario], 'categories_empty')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [categoriesEmptyScenario], 'categories_empty')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [categoriesEmptyScenario],
          );
          await screenshotVerified(tester, 'categories_empty');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [categoriesEmptyScenario], 'categories_empty')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [linkedSymbolsEmptyScenario], 'linked_symbols_empty')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [linkedSymbolsEmptyScenario], 'linked_symbols_empty')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [linkedSymbolsEmptyScenario],
          );
          await screenshotVerified(tester, 'linked_symbols_empty');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [linkedSymbolsEmptyScenario], 'linked_symbols_empty')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Market screen (scenarios: [marketRESTAPISuccessScenario,productHubFailureScenario], 'product_hub_shimmer')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Market screen (scenarios: [marketRESTAPISuccessScenario,productHubFailureScenario], 'product_hub_shimmer')''',
          );
          await theAppIsRendered(
            tester,
            SymbolsScreen(),
            scenarios: [
              marketRESTAPISuccessScenario,
              productHubFailureScenario,
            ],
          );
          await screenshotVerified(tester, 'product_hub_shimmer');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Market screen (scenarios: [marketRESTAPISuccessScenario,productHubFailureScenario], 'product_hub_shimmer')''',
            success,
          );
        }
      },
    );
    testWidgets('''User sort market watchlist items in descending order''', (
      tester,
    ) async {
      var success = true;
      try {
        await beforeEach(
          '''User sort market watchlist items in descending order''',
        );
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [marketSuccessScenario],
        );
        await iWait(tester);
        await iTapText(tester, 'Currencies');
        await iWait(tester);
        await iSeeText(tester, 'AUDCAD');
        await iTapText(tester, 'Sort');
        await iWait(tester);
        await iSelectSortOption(tester, 'Z - A');
        await theConfigurationIsUpdatedTo(
          tester,
          scenarios: [linkedSymbolsDescendingSuccessScenario],
        );
        await iTapText(tester, 'Sort Markets');
        await iWait(tester);
        await iSeeText(tester, 'USDZAR');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach(
          '''User sort market watchlist items in descending order''',
          success,
        );
      }
    });
    testWidgets(
      '''User sort market watchlist items in descending order failed''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''User sort market watchlist items in descending order failed''',
          );
          await theAppRunsWithConfiguration(
            tester,
            SymbolsScreen(),
            scenarios: [marketSuccessScenario],
          );
          await iWait(tester);
          await iTapText(tester, 'Currencies');
          await iWait(tester);
          await iSeeText(tester, 'AUDCAD');
          await iTapText(tester, 'Sort');
          await iWaitForSeconds(tester, 1);
          await iSelectSortOption(tester, 'Z - A');
          await theConfigurationIsUpdatedTo(
            tester,
            scenarios: [linkedSymbolsFailureScenario],
          );
          await iTapText(tester, 'Sort Markets');
          await iWaitForSeconds(tester, 1);
          await iSeeText(tester, "Something went wrong");
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''User sort market watchlist items in descending order failed''',
            success,
          );
        }
      },
    );
    testGoldens('''Open search modal''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Open search modal''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [marketSuccessScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await screenshotVerified(tester, 'open_search_modal');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Open search modal''', success);
      }
    });
    testGoldens('''Search with minimum characters''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Search with minimum characters''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [marketSuccessScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "EU");
        await iWait(tester);
        await screenshotVerified(tester, 'search_with_minimum_characters');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Search with minimum characters''', success);
      }
    });
    testGoldens('''Faild loading symbols''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Faild loading symbols''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [linkedSymbolsFailureScenario],
        );
        await iWait(tester);
        await screenshotVerified(tester, 'failed_laoding_symbols');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Faild loading symbols''', success);
      }
    });
    testGoldens('''Empty category''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Empty category''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [linkedSymbolsEmptyScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "CAD");
        await iWait(tester);
        await screenshotVerified(tester, 'empty_category');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Empty category''', success);
      }
    });
    testGoldens('''Valid search''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Valid search''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [marketSuccessScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "CAD");
        await iWait(tester);
        await iSeeText(tester, 'AUDCAD');
        await screenshotVerified(tester, 'valid_search');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Valid search''', success);
      }
    });
    testGoldens('''Failed search''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Failed search''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [linkedSymbolsFailureScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "CAD");
        await iWait(tester);
        await screenshotVerified(tester, 'failed_search');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Failed search''', success);
      }
    });
    testGoldens('''Empty search''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Empty search''');
        await theAppRunsWithConfiguration(
          tester,
          SymbolsScreen(),
          scenarios: [linkedSymbolsEmptyScenario],
        );
        await iTapOnTheDuplosearchbox(tester);
        await iEnterInTheSearchField(tester, "CAD");
        await iWait(tester);
        await screenshotVerified(tester, 'empty_search_screen');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Empty search''', success);
      }
    });
  });
}
