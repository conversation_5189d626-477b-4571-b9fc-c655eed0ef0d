import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/data/account_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/data/payment_status_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/navigation/arguments/transfer_type_page_arguments.dart';
import 'package:payment/src/navigation/arguments/withdraw_bank_transfer_arguments.dart';

abstract class PaymentNavigation {
  void goToEquitiPayCardsScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  });
  void goToPaymentStatusScreen({
    required String transactionId,
    required String accountNumber,
    required DepositFlowConfig depositFlowConfig,
    PaymentStatus? paymentStatus,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  });
  void goToFormBuilder();
  void goToPaymentOptionsScreen({required DepositFlowConfig depositFlowConfig});
  void goToDepositSelectAccountAndAmountScreen(
    DepositPaymentMethod method, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  });
  void goToWithdrawSelectAccountAndAmountScreen({
    bool replace = false,
    required WithdrawalPaymentMethod method,
    String? account,
    WithdrawCard? selectedCard,
  });
  void goToPaymentNotAvailableYetPage();
  void goToDepositSelectBankScreen(DepositPaymentMethodGroup methodGroup);
  void goToDespositBankDetailsScreen(Bank bank);
  void goToWithdrawCardPage(WithdrawalPaymentMethod method);
  void goToWithdrawAddNewBankPage(WithdrawFlowParams withdrawFlowParams);
  void goToAdditionalPaymentScreen(
    String url,
    String title,
    String transactionId,
    String accountNumber, {
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  });
  void goBackToDepositPaymentOptionsScreen();
  void goBackToDepositSelectAccountAndAmountScreen();
  void goToWithdrawBankTransferScreen(
    WithdrawBankTransferArguments withdrawBankTransferArguments,
  );
  void goToTransferTypeScreen(TransferTypePageArguments arguments);
  void goToWithdrawSkrillAndNetellerScreen(WithdrawalPaymentMethod method);

  void goToWithdrawNewBankUploadDocScreen({
    required String operationId,
    required String tradingAccountId,
    bool replace = false,
  });
  void goToTransferFundsDestSelectionScreen(Account account);
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  });
  void goToTransferFundsScreen();
  void goToWithdrawStatusScreen({
    bool replace = false,
    VoidCallback? onContinue,
    WithdrawStatusType? status,
    String? popUntilRoute,
  });

  /// Pops all screens until the specified route is reached
  /// [routeLabel] - The route label to pop until (from PaymentRouteSchema)
  /// [inclusive] - Whether to also pop the target route (default: false)
  void popUntilRoute(String routeLabel, {bool inclusive = false});
  void goBackToSwitchAccounts();
  void removeAllAndNavigateToHub();
}
