import 'scenarios/bank_transfer_accounts_success.dart';
import 'scenarios/bank_transfer_accounts_faliure.dart';
import 'scenarios/withdraw_fees_insufficient_balance.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/withdraw_bank_transfer.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/transfer_type_screen.dart';
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';



Feature: Withdraw Bank Transfer Screen
  @testMethodName: testGoldens
  Scenario: list all  accounts
    Given The {WithdrawBankTransfer(tradingAccountId: '112344',bankTransferAmountModel: WithdrawFlowParams(tradingAccountId: '1234',    paymentType: WithdrawalMop.bank,accountCurrency: 'Usd',currency: 'Usd',amount: 233,convertedAmount: 233,conversionRate: 32,conversionRateString: '32',accountBalance: 1000,),)} app is rendered {scenarios:[bankTransferAccountListSuccessful]}
    Then screenshot verified {'withdraw_bank_success'}


  @testMethodName: testGoldens
  Scenario: Failed to load accounts
    Given The {WithdrawBankTransfer(tradingAccountId: '112344',bankTransferAmountModel: WithdrawFlowParams(tradingAccountId: '1234',    paymentType: WithdrawalMop.bank,accountCurrency: 'Usd',currency: 'Usd',amount: 233,convertedAmount: 233,conversionRate: 32,conversionRateString: '32',accountBalance: 1000,),)} app is rendered {scenarios:[bankTransferAccountListFailure]}
    Then screenshot verified {'withdraw_bank_failure'}



  @testMethodName: testGoldens
  Scenario: enable deletion mode
    Given The {WithdrawBankTransfer(tradingAccountId: '112344',bankTransferAmountModel: WithdrawFlowParams(tradingAccountId: '1234',    paymentType: WithdrawalMop.bank,accountCurrency: 'Usd',currency: 'Usd',amount: 233,convertedAmount: 233,conversionRate: 32,conversionRateString: '32',accountBalance: 1000,),)} app is rendered {scenarios:[bankTransferAccountListSuccessful]}
    Then i wait
    Then i tap {'enable_delete_bank_account_button'} key
    Then screenshot verified {'enable_deletion_mode'}


  @testMethodName: testGoldens
  Scenario: tab on delete icon
    Given The {WithdrawBankTransfer(tradingAccountId: '112344',bankTransferAmountModel: WithdrawFlowParams(tradingAccountId: '1234',    paymentType: WithdrawalMop.bank,accountCurrency: 'Usd',currency: 'Usd',amount: 233,convertedAmount: 233,conversionRate: 32,conversionRateString: '32',accountBalance: 1000,),)} app is rendered {scenarios:[bankTransferAccountListSuccessful]}
    Then i wait
    Then i tap {'enable_delete_bank_account_button'} key
    Then i wait
    Then i tap {'delete_bank_account_button_ferf'} key
    Then i wait
    Then screenshot verified {'tab_on_delete_icon'}


  @testMethodName: testGoldens
  Scenario: Insufficient balance validation with high fees
    Given The {TransferTypeScreen(bankTransferAmountModel: WithdrawFlowParams(tradingAccountId: '1234',    paymentType: WithdrawalMop.bank,accountCurrency: 'USD',currency: 'USD',amount: 500,convertedAmount: 500,conversionRate: 1,conversionRateString: '1',accountBalance: 600,), bank: BankAccountData(id: 'test-id', accountNickname: 'Test Bank', bankName: 'Test Bank Name', branchName: 'Test Branch', accountHolder: 'Test Holder', country: 'US', iban: 'TEST123', swiftBic: 'TESTBIC', transferTypes: ['Domestic', 'International'], image: null, type: 0))} app is rendered {scenarios:[withdrawFeesInsufficientBalance]}
    Then i wait
    Then i tap {'transfer_type_field'} key
    Then i wait
    Then i tap {'transfer_type_domestic'} key
    Then i wait
    Then screenshot verified {'insufficient_balance_validation'}


