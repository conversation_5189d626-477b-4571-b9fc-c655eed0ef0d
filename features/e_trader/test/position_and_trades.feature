import 'package:equiti_test/equiti_test.dart';

import 'position_and_trades_test_data.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/positions_and_trades/order_list_tile.dart';
import 'package:e_trader/src/domain/model/entry_order_type.dart';

Feature: Position and Trades

  @testMethodName: testGoldens
  Scenario: Position Header Collapsed
    Given The {PositionAndTradesTestData()} app is rendered
    Then i wait
    Then screenshot verified {'position_tile_colapsed'}

  @testMethodName: testGoldens
  Scenario: Position header Expanded
    Given The {PositionAndTradesTestData()} app is rendered
    Then i tap {"hedged_button_positions"} identifier
    Then i wait
    Then screenshot verified {'position_tile_expanded'}


  @testMethodName: testGoldens
  Scenario: Position Header Single
    Given The {PositionHeaderWidget()} app is rendered
    Then screenshot verified {'position_header_single'}


  @testMethodName: testGoldens
  Scenario: Trade Tile Single
    Given The {TradeTileWidget()} app is rendered
    Then screenshot verified {'trade_tile_signle'}

  @testMethodName: testGoldens
  Scenario: Order list tile
    Given The {OrderListTile(slValue: 0.03,tpValue: 0.02,digits: 5,orderPrice: 099.987,lots: 0.04,currentPrice: 101.064,tradeType: TradeType.buy,productIconURL:'',productName: 'EURUSD', entryOrderType: EntryOrderType.buyLimit, priceChange: 12.23)} app is rendered
    Then screenshot verified {'order_list_tile'}
