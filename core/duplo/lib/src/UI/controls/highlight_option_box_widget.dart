import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class HighlightOptionBoxWidget extends StatefulWidget {
  final List<String> options;
  final void Function(String, int) onSelectionChange;
  final int selectedIndex;
  final bool oneTimeSelection;

  const HighlightOptionBoxWidget({
    required this.selectedIndex,
    required this.options,
    required this.onSelectionChange,
    this.oneTimeSelection = false,
  });

  @override
  _HighlightOptionBoxWidgetState createState() =>
      _HighlightOptionBoxWidgetState();
}

class _HighlightOptionBoxWidgetState extends State<HighlightOptionBoxWidget> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
  }

  @override
  void didUpdateWidget(covariant HighlightOptionBoxWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedIndex != oldWidget.selectedIndex) {
      _selectedIndex = widget.selectedIndex;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);

    return SegmentedButton(
      showSelectedIcon: false,
      segments: List.generate(
        widget.options.length,
        (index) => ButtonSegment<int>(
          value: index,
          label: Semantics(
            identifier: "button_segment_index_$index",
            child: DuploText(
              text: widget.options[index].toString(),
              style: context.duploTextStyles.textSm,
              fontWeight: DuploFontWeight.semiBold,
            ),
          ),
        ),
      ),
      selected: {_selectedIndex},
      onSelectionChanged: (Set<int> newSelection) {
        final newSelectionIndex = newSelection.firstOrNull ?? -1;
        if (!widget.oneTimeSelection) {
          setState(() {
            _selectedIndex = newSelectionIndex;
          });
        }
        widget.onSelectionChange(
          widget.options.elementAtOrNull(newSelectionIndex) ?? '',
          newSelectionIndex,
        );
      },
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.selected)) {
            return theme.text.textPrimary;
          }
          return theme.background.bgPrimary;
        }),
        foregroundColor: WidgetStateProperty.resolveWith((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.selected)) {
            return theme.background.bgSecondaryAlt;
          }
          return theme.text.textSecondary;
        }),
        side: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return BorderSide(color: theme.utility.utilityGray600, width: 1);
          }
          return BorderSide(color: theme.border.borderPrimary, width: 1);
        }),
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }
}
