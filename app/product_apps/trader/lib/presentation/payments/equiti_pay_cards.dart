// ignore_for_file: no-empty-block

import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';

import '../../di/di_initializer.dart';

const kUrl = "https://google.com";

DisplayableComponent equitiPayCards() {
  return DisplayableComponent(
    title: 'Equiti Pay Cards',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          _returnMockStatus("success");
          return EquitiPayCardsScreen(
            url: kUrl,
            title: "Deposit with credit/debit card",
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'No callback from webview',
        onTap: () {
          _returnMockStatus("pending");
          return EquitiPayCardsScreen(
            url: "https://google.com",
            title: "Deposit with credit/debit card",
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",

              depositType: DepositType.additional,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Rejected',
        onTap: () {
          _returnMockStatus("rejected");
          return EquitiPayCardsScreen(
            url: kUrl,
            title: "Deposit with credit/debit card",
            transactionId: '123',
            accountNumber: '1234',
            depositFlowConfig: DepositFlowConfig(
              origin: "",
              depositType: DepositType.additional,
            ),
          );
        },
      ),
    ],
  );
}

void _returnMockStatus(String status) {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'api/v1/payment/status/123': [
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(bodyFilePath: 'resources/mocks/status/status_new.json'),
        MockResponse(
          bodyFilePath: 'resources/mocks/status/status_$status.json',
        ),
      ],
    });
}
