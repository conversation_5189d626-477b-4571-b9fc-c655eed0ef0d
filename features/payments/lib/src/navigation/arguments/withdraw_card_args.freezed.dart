// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_card_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WithdrawCardArgs {

 WithdrawFlowParams get withdrawFlowParams;
/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawCardArgsCopyWith<WithdrawCardArgs> get copyWith => _$WithdrawCardArgsCopyWithImpl<WithdrawCardArgs>(this as WithdrawCardArgs, _$identity);

  /// Serializes this WithdrawCardArgs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawCardArgs&&(identical(other.withdrawFlowParams, withdrawFlowParams) || other.withdrawFlowParams == withdrawFlowParams));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,withdrawFlowParams);

@override
String toString() {
  return 'WithdrawCardArgs(withdrawFlowParams: $withdrawFlowParams)';
}


}

/// @nodoc
abstract mixin class $WithdrawCardArgsCopyWith<$Res>  {
  factory $WithdrawCardArgsCopyWith(WithdrawCardArgs value, $Res Function(WithdrawCardArgs) _then) = _$WithdrawCardArgsCopyWithImpl;
@useResult
$Res call({
 WithdrawFlowParams withdrawFlowParams
});


$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams;

}
/// @nodoc
class _$WithdrawCardArgsCopyWithImpl<$Res>
    implements $WithdrawCardArgsCopyWith<$Res> {
  _$WithdrawCardArgsCopyWithImpl(this._self, this._then);

  final WithdrawCardArgs _self;
  final $Res Function(WithdrawCardArgs) _then;

/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? withdrawFlowParams = null,}) {
  return _then(_self.copyWith(
withdrawFlowParams: null == withdrawFlowParams ? _self.withdrawFlowParams : withdrawFlowParams // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}
/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.withdrawFlowParams, (value) {
    return _then(_self.copyWith(withdrawFlowParams: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _WithdrawCardArgs implements WithdrawCardArgs {
  const _WithdrawCardArgs({required this.withdrawFlowParams});
  factory _WithdrawCardArgs.fromJson(Map<String, dynamic> json) => _$WithdrawCardArgsFromJson(json);

@override final  WithdrawFlowParams withdrawFlowParams;

/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawCardArgsCopyWith<_WithdrawCardArgs> get copyWith => __$WithdrawCardArgsCopyWithImpl<_WithdrawCardArgs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawCardArgsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawCardArgs&&(identical(other.withdrawFlowParams, withdrawFlowParams) || other.withdrawFlowParams == withdrawFlowParams));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,withdrawFlowParams);

@override
String toString() {
  return 'WithdrawCardArgs(withdrawFlowParams: $withdrawFlowParams)';
}


}

/// @nodoc
abstract mixin class _$WithdrawCardArgsCopyWith<$Res> implements $WithdrawCardArgsCopyWith<$Res> {
  factory _$WithdrawCardArgsCopyWith(_WithdrawCardArgs value, $Res Function(_WithdrawCardArgs) _then) = __$WithdrawCardArgsCopyWithImpl;
@override @useResult
$Res call({
 WithdrawFlowParams withdrawFlowParams
});


@override $WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams;

}
/// @nodoc
class __$WithdrawCardArgsCopyWithImpl<$Res>
    implements _$WithdrawCardArgsCopyWith<$Res> {
  __$WithdrawCardArgsCopyWithImpl(this._self, this._then);

  final _WithdrawCardArgs _self;
  final $Res Function(_WithdrawCardArgs) _then;

/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? withdrawFlowParams = null,}) {
  return _then(_WithdrawCardArgs(
withdrawFlowParams: null == withdrawFlowParams ? _self.withdrawFlowParams : withdrawFlowParams // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of WithdrawCardArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get withdrawFlowParams {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.withdrawFlowParams, (value) {
    return _then(_self.copyWith(withdrawFlowParams: value));
  });
}
}

// dart format on
