// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_accounts_and_amount_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositAccountsAndAmountEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent()';
}


}

/// @nodoc
class $DepositAccountsAndAmountEventCopyWith<$Res>  {
$DepositAccountsAndAmountEventCopyWith(DepositAccountsAndAmountEvent _, $Res Function(DepositAccountsAndAmountEvent) __);
}


/// @nodoc


class _OnAccountChange implements DepositAccountsAndAmountEvent {
  const _OnAccountChange(this.account);
  

 final  Account account;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAccountChangeCopyWith<_OnAccountChange> get copyWith => __$OnAccountChangeCopyWithImpl<_OnAccountChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAccountChange&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,account);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onAccountChange(account: $account)';
}


}

/// @nodoc
abstract mixin class _$OnAccountChangeCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnAccountChangeCopyWith(_OnAccountChange value, $Res Function(_OnAccountChange) _then) = __$OnAccountChangeCopyWithImpl;
@useResult
$Res call({
 Account account
});


$AccountCopyWith<$Res> get account;

}
/// @nodoc
class __$OnAccountChangeCopyWithImpl<$Res>
    implements _$OnAccountChangeCopyWith<$Res> {
  __$OnAccountChangeCopyWithImpl(this._self, this._then);

  final _OnAccountChange _self;
  final $Res Function(_OnAccountChange) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? account = null,}) {
  return _then(_OnAccountChange(
null == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as Account,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res> get account {
  
  return $AccountCopyWith<$Res>(_self.account, (value) {
    return _then(_self.copyWith(account: value));
  });
}
}

/// @nodoc


class _NaviagteToSelectedPaymentMethod implements DepositAccountsAndAmountEvent {
  const _NaviagteToSelectedPaymentMethod(this.mop);
  

 final  DepositPaymentMethod? mop;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NaviagteToSelectedPaymentMethodCopyWith<_NaviagteToSelectedPaymentMethod> get copyWith => __$NaviagteToSelectedPaymentMethodCopyWithImpl<_NaviagteToSelectedPaymentMethod>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NaviagteToSelectedPaymentMethod&&(identical(other.mop, mop) || other.mop == mop));
}


@override
int get hashCode => Object.hash(runtimeType,mop);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.naviagteToSelectedPaymentMethod(mop: $mop)';
}


}

/// @nodoc
abstract mixin class _$NaviagteToSelectedPaymentMethodCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$NaviagteToSelectedPaymentMethodCopyWith(_NaviagteToSelectedPaymentMethod value, $Res Function(_NaviagteToSelectedPaymentMethod) _then) = __$NaviagteToSelectedPaymentMethodCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod? mop
});


$DepositPaymentMethodCopyWith<$Res>? get mop;

}
/// @nodoc
class __$NaviagteToSelectedPaymentMethodCopyWithImpl<$Res>
    implements _$NaviagteToSelectedPaymentMethodCopyWith<$Res> {
  __$NaviagteToSelectedPaymentMethodCopyWithImpl(this._self, this._then);

  final _NaviagteToSelectedPaymentMethod _self;
  final $Res Function(_NaviagteToSelectedPaymentMethod) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? mop = freezed,}) {
  return _then(_NaviagteToSelectedPaymentMethod(
freezed == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod?,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res>? get mop {
    if (_self.mop == null) {
    return null;
  }

  return $DepositPaymentMethodCopyWith<$Res>(_self.mop!, (value) {
    return _then(_self.copyWith(mop: value));
  });
}
}

/// @nodoc


class _GetDepositDetails implements DepositAccountsAndAmountEvent {
  const _GetDepositDetails({required this.paymentMethod});
  

 final  DepositPaymentMethod paymentMethod;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetDepositDetailsCopyWith<_GetDepositDetails> get copyWith => __$GetDepositDetailsCopyWithImpl<_GetDepositDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetDepositDetails&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethod);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.getDepositDetails(paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class _$GetDepositDetailsCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$GetDepositDetailsCopyWith(_GetDepositDetails value, $Res Function(_GetDepositDetails) _then) = __$GetDepositDetailsCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod paymentMethod
});


$DepositPaymentMethodCopyWith<$Res> get paymentMethod;

}
/// @nodoc
class __$GetDepositDetailsCopyWithImpl<$Res>
    implements _$GetDepositDetailsCopyWith<$Res> {
  __$GetDepositDetailsCopyWithImpl(this._self, this._then);

  final _GetDepositDetails _self;
  final $Res Function(_GetDepositDetails) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentMethod = null,}) {
  return _then(_GetDepositDetails(
paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get paymentMethod {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.paymentMethod, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}
}

/// @nodoc


class _OnAmountChange implements DepositAccountsAndAmountEvent {
  const _OnAmountChange(this.amount, this.convertedAmount, this.ratesModel, this.conversionRateString, this.targetCurrency);
  

 final  String amount;
 final  String convertedAmount;
 final  RatesModel? ratesModel;
 final  String? conversionRateString;
 final  String? targetCurrency;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAmountChangeCopyWith<_OnAmountChange> get copyWith => __$OnAmountChangeCopyWithImpl<_OnAmountChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAmountChange&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,amount,convertedAmount,ratesModel,conversionRateString,targetCurrency);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onAmountChange(amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency)';
}


}

/// @nodoc
abstract mixin class _$OnAmountChangeCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnAmountChangeCopyWith(_OnAmountChange value, $Res Function(_OnAmountChange) _then) = __$OnAmountChangeCopyWithImpl;
@useResult
$Res call({
 String amount, String convertedAmount, RatesModel? ratesModel, String? conversionRateString, String? targetCurrency
});


$RatesModelCopyWith<$Res>? get ratesModel;

}
/// @nodoc
class __$OnAmountChangeCopyWithImpl<$Res>
    implements _$OnAmountChangeCopyWith<$Res> {
  __$OnAmountChangeCopyWithImpl(this._self, this._then);

  final _OnAmountChange _self;
  final $Res Function(_OnAmountChange) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? conversionRateString = freezed,Object? targetCurrency = freezed,}) {
  return _then(_OnAmountChange(
null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,freezed == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}
}

/// @nodoc


class _ChangeButtonState implements DepositAccountsAndAmountEvent {
  const _ChangeButtonState(this.isValid);
  

 final  bool isValid;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeButtonStateCopyWith<_ChangeButtonState> get copyWith => __$ChangeButtonStateCopyWithImpl<_ChangeButtonState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeButtonState&&(identical(other.isValid, isValid) || other.isValid == isValid));
}


@override
int get hashCode => Object.hash(runtimeType,isValid);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.changeButtonState(isValid: $isValid)';
}


}

/// @nodoc
abstract mixin class _$ChangeButtonStateCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$ChangeButtonStateCopyWith(_ChangeButtonState value, $Res Function(_ChangeButtonState) _then) = __$ChangeButtonStateCopyWithImpl;
@useResult
$Res call({
 bool isValid
});




}
/// @nodoc
class __$ChangeButtonStateCopyWithImpl<$Res>
    implements _$ChangeButtonStateCopyWith<$Res> {
  __$ChangeButtonStateCopyWithImpl(this._self, this._then);

  final _ChangeButtonState _self;
  final $Res Function(_ChangeButtonState) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isValid = null,}) {
  return _then(_ChangeButtonState(
null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _OnApplePayResult implements DepositAccountsAndAmountEvent {
  const _OnApplePayResult({required this.paymentStatus, required this.gatewayCode, required this.applePay});
  

 final  ApplePayStatus paymentStatus;
 final  String? gatewayCode;
 final  ApplePayUtils applePay;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnApplePayResultCopyWith<_OnApplePayResult> get copyWith => __$OnApplePayResultCopyWithImpl<_OnApplePayResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnApplePayResult&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.gatewayCode, gatewayCode) || other.gatewayCode == gatewayCode)&&(identical(other.applePay, applePay) || other.applePay == applePay));
}


@override
int get hashCode => Object.hash(runtimeType,paymentStatus,gatewayCode,applePay);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onApplePayResult(paymentStatus: $paymentStatus, gatewayCode: $gatewayCode, applePay: $applePay)';
}


}

/// @nodoc
abstract mixin class _$OnApplePayResultCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnApplePayResultCopyWith(_OnApplePayResult value, $Res Function(_OnApplePayResult) _then) = __$OnApplePayResultCopyWithImpl;
@useResult
$Res call({
 ApplePayStatus paymentStatus, String? gatewayCode, ApplePayUtils applePay
});




}
/// @nodoc
class __$OnApplePayResultCopyWithImpl<$Res>
    implements _$OnApplePayResultCopyWith<$Res> {
  __$OnApplePayResultCopyWithImpl(this._self, this._then);

  final _OnApplePayResult _self;
  final $Res Function(_OnApplePayResult) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentStatus = null,Object? gatewayCode = freezed,Object? applePay = null,}) {
  return _then(_OnApplePayResult(
paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ApplePayStatus,gatewayCode: freezed == gatewayCode ? _self.gatewayCode : gatewayCode // ignore: cast_nullable_to_non_nullable
as String?,applePay: null == applePay ? _self.applePay : applePay // ignore: cast_nullable_to_non_nullable
as ApplePayUtils,
  ));
}


}

/// @nodoc


class _OnGooglePayResult implements DepositAccountsAndAmountEvent {
  const _OnGooglePayResult({required this.paymentStatus, required this.gatewayCode, required this.googlePay});
  

 final  GooglePayStatus paymentStatus;
 final  String? gatewayCode;
 final  GooglePayUtils googlePay;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGooglePayResultCopyWith<_OnGooglePayResult> get copyWith => __$OnGooglePayResultCopyWithImpl<_OnGooglePayResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGooglePayResult&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.gatewayCode, gatewayCode) || other.gatewayCode == gatewayCode)&&(identical(other.googlePay, googlePay) || other.googlePay == googlePay));
}


@override
int get hashCode => Object.hash(runtimeType,paymentStatus,gatewayCode,googlePay);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onGooglePayResult(paymentStatus: $paymentStatus, gatewayCode: $gatewayCode, googlePay: $googlePay)';
}


}

/// @nodoc
abstract mixin class _$OnGooglePayResultCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnGooglePayResultCopyWith(_OnGooglePayResult value, $Res Function(_OnGooglePayResult) _then) = __$OnGooglePayResultCopyWithImpl;
@useResult
$Res call({
 GooglePayStatus paymentStatus, String? gatewayCode, GooglePayUtils googlePay
});




}
/// @nodoc
class __$OnGooglePayResultCopyWithImpl<$Res>
    implements _$OnGooglePayResultCopyWith<$Res> {
  __$OnGooglePayResultCopyWithImpl(this._self, this._then);

  final _OnGooglePayResult _self;
  final $Res Function(_OnGooglePayResult) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentStatus = null,Object? gatewayCode = freezed,Object? googlePay = null,}) {
  return _then(_OnGooglePayResult(
paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as GooglePayStatus,gatewayCode: freezed == gatewayCode ? _self.gatewayCode : gatewayCode // ignore: cast_nullable_to_non_nullable
as String?,googlePay: null == googlePay ? _self.googlePay : googlePay // ignore: cast_nullable_to_non_nullable
as GooglePayUtils,
  ));
}


}

/// @nodoc


class OnPaymentMethodChange implements DepositAccountsAndAmountEvent {
  const OnPaymentMethodChange();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnPaymentMethodChange);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onPaymentMethodChange()';
}


}




/// @nodoc


class OnPaymentSuccessContinuePressed implements DepositAccountsAndAmountEvent {
  const OnPaymentSuccessContinuePressed();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnPaymentSuccessContinuePressed);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onPaymentSuccessContinuePressed()';
}


}




/// @nodoc
mixin _$DepositAccountsAndAmountState {

 Account? get selectedAccount; bool get isButtonEnabled; String get amount; String get convertedAmount; RatesModel? get ratesModel; bool get isButtonLoading; String get conversionRateString; String get targetCurrency; DepositAccountsAndAmountProcessState get processState;
/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountStateCopyWith<DepositAccountsAndAmountState> get copyWith => _$DepositAccountsAndAmountStateCopyWithImpl<DepositAccountsAndAmountState>(this as DepositAccountsAndAmountState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,selectedAccount,isButtonEnabled,amount,convertedAmount,ratesModel,isButtonLoading,conversionRateString,targetCurrency,processState);

@override
String toString() {
  return 'DepositAccountsAndAmountState(selectedAccount: $selectedAccount, isButtonEnabled: $isButtonEnabled, amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, isButtonLoading: $isButtonLoading, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency, processState: $processState)';
}


}

/// @nodoc
abstract mixin class $DepositAccountsAndAmountStateCopyWith<$Res>  {
  factory $DepositAccountsAndAmountStateCopyWith(DepositAccountsAndAmountState value, $Res Function(DepositAccountsAndAmountState) _then) = _$DepositAccountsAndAmountStateCopyWithImpl;
@useResult
$Res call({
 Account? selectedAccount, bool isButtonEnabled, String amount, String convertedAmount, RatesModel? ratesModel, bool isButtonLoading, String conversionRateString, String targetCurrency, DepositAccountsAndAmountProcessState processState
});


$AccountCopyWith<$Res>? get selectedAccount;$RatesModelCopyWith<$Res>? get ratesModel;$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$DepositAccountsAndAmountStateCopyWithImpl<$Res>
    implements $DepositAccountsAndAmountStateCopyWith<$Res> {
  _$DepositAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final DepositAccountsAndAmountState _self;
  final $Res Function(DepositAccountsAndAmountState) _then;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedAccount = freezed,Object? isButtonEnabled = null,Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? isButtonLoading = null,Object? conversionRateString = null,Object? targetCurrency = null,Object? processState = null,}) {
  return _then(_self.copyWith(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as Account?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as DepositAccountsAndAmountProcessState,
  ));
}
/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $AccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState {
  
  return $DepositAccountsAndAmountProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _DepositAccountsAndAmountState implements DepositAccountsAndAmountState {
  const _DepositAccountsAndAmountState({this.selectedAccount, this.isButtonEnabled = false, this.amount = '', this.convertedAmount = '', this.ratesModel = null, this.isButtonLoading = false, this.conversionRateString = '', this.targetCurrency = '', this.processState = const LoadedState()});
  

@override final  Account? selectedAccount;
@override@JsonKey() final  bool isButtonEnabled;
@override@JsonKey() final  String amount;
@override@JsonKey() final  String convertedAmount;
@override@JsonKey() final  RatesModel? ratesModel;
@override@JsonKey() final  bool isButtonLoading;
@override@JsonKey() final  String conversionRateString;
@override@JsonKey() final  String targetCurrency;
@override@JsonKey() final  DepositAccountsAndAmountProcessState processState;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositAccountsAndAmountStateCopyWith<_DepositAccountsAndAmountState> get copyWith => __$DepositAccountsAndAmountStateCopyWithImpl<_DepositAccountsAndAmountState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.targetCurrency, targetCurrency) || other.targetCurrency == targetCurrency)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,selectedAccount,isButtonEnabled,amount,convertedAmount,ratesModel,isButtonLoading,conversionRateString,targetCurrency,processState);

@override
String toString() {
  return 'DepositAccountsAndAmountState(selectedAccount: $selectedAccount, isButtonEnabled: $isButtonEnabled, amount: $amount, convertedAmount: $convertedAmount, ratesModel: $ratesModel, isButtonLoading: $isButtonLoading, conversionRateString: $conversionRateString, targetCurrency: $targetCurrency, processState: $processState)';
}


}

/// @nodoc
abstract mixin class _$DepositAccountsAndAmountStateCopyWith<$Res> implements $DepositAccountsAndAmountStateCopyWith<$Res> {
  factory _$DepositAccountsAndAmountStateCopyWith(_DepositAccountsAndAmountState value, $Res Function(_DepositAccountsAndAmountState) _then) = __$DepositAccountsAndAmountStateCopyWithImpl;
@override @useResult
$Res call({
 Account? selectedAccount, bool isButtonEnabled, String amount, String convertedAmount, RatesModel? ratesModel, bool isButtonLoading, String conversionRateString, String targetCurrency, DepositAccountsAndAmountProcessState processState
});


@override $AccountCopyWith<$Res>? get selectedAccount;@override $RatesModelCopyWith<$Res>? get ratesModel;@override $DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$DepositAccountsAndAmountStateCopyWithImpl<$Res>
    implements _$DepositAccountsAndAmountStateCopyWith<$Res> {
  __$DepositAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final _DepositAccountsAndAmountState _self;
  final $Res Function(_DepositAccountsAndAmountState) _then;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedAccount = freezed,Object? isButtonEnabled = null,Object? amount = null,Object? convertedAmount = null,Object? ratesModel = freezed,Object? isButtonLoading = null,Object? conversionRateString = null,Object? targetCurrency = null,Object? processState = null,}) {
  return _then(_DepositAccountsAndAmountState(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as Account?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,targetCurrency: null == targetCurrency ? _self.targetCurrency : targetCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as DepositAccountsAndAmountProcessState,
  ));
}

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $AccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState {
  
  return $DepositAccountsAndAmountProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$DepositAccountsAndAmountProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState()';
}


}

/// @nodoc
class $DepositAccountsAndAmountProcessStateCopyWith<$Res>  {
$DepositAccountsAndAmountProcessStateCopyWith(DepositAccountsAndAmountProcessState _, $Res Function(DepositAccountsAndAmountProcessState) __);
}


/// @nodoc


class LoadedState implements DepositAccountsAndAmountProcessState {
  const LoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.loaded()';
}


}




/// @nodoc


class ErrorState implements DepositAccountsAndAmountProcessState {
  const ErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.error()';
}


}




/// @nodoc


class PaymentFailedState implements DepositAccountsAndAmountProcessState {
  const PaymentFailedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentFailedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentFailed()';
}


}




/// @nodoc


class PaymentSuccessState implements DepositAccountsAndAmountProcessState {
  const PaymentSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentSuccess()';
}


}




// dart format on
