import 'package:e_trader/src/data/api/events_news_request_model.dart';
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/domain/model/calendar_error_result.dart';
import 'package:e_trader/src/domain/model/news_events_sort_order.dart';
import 'package:e_trader/src/domain/usecase/calendar_use_case.dart';
import 'package:e_trader/src/domain/usecase/events_news_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/events_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'events_bloc.freezed.dart';
part 'events_event.dart';
part 'events_state.dart';

class EventsBloc extends Bloc<EventsEvent, EventsState> {
  final EventsUseCase _eventsUseCase;
  final EventsNewsLocalDataUseCase _eventsLocalDataUseCase;
  final CalendarUseCase _calendarUseCase;
  final String? _tickerName;
  EventsBloc(
    this._eventsUseCase,
    this._eventsLocalDataUseCase,
    this._calendarUseCase,
    this._tickerName,
  ) : super(
        EventsState(
          processState: EventsProcessState.initial(),
          sortOrder: _eventsLocalDataUseCase.getNewsSortingOption(),
        ),
      ) {
    on<FetchEvents>(_onFetchEvents);
    on<SearchEvents>(
      _onSearchEvents,
      transformer: throttleTransformer(_throttleDuration),
    );
    on<_ChangeSortOrder>(_onChangeSortOrder);
    on<_AddEventToCalendar>(_onAddEventToCalendar);
    on<UpdateDateLabel>(_onUpdateDateLabel);
  }
  final _throttleDuration = Duration(milliseconds: 500);
  final int _pageSize = 10;

  Future<void> _onFetchEvents(
    FetchEvents event,
    Emitter<EventsState> emit,
  ) async {
    if (event.loadMore && state.hasReachedMax) {
      return;
    }
    final bool isLoadingMore = event.loadMore;
    final int currentPage = isLoadingMore ? state.currentPage + 1 : 0;
    final int startIndex = currentPage;
    if (!isLoadingMore) {
      emit(
        state.copyWith(
          processState: const EventsProcessState.loading(),
          currentPage: 0,
          hasReachedMax: false,
        ),
      );
    } else {
      emit(
        state.copyWith(processState: const EventsProcessState.loadingMore()),
      );
    }
    final request = EventsNewsRequestModel(
      start: startIndex,
      length: _pageSize,
      order: EventsSearchOrder(name: 'date', dir: 'desc'),
      search: EventsSearchCriteria(value: event.query, matchingType: 1),
      filters: [
        EventsSearchFilter(field: 'tickername', value: _tickerName ?? ''),
      ],
    );

    final result = await _eventsUseCase(body: request).run();

    result.fold(
      (failure) {
        emit(
          state.copyWith(processState: EventsProcessState.error('${failure}')),
        );
      },
      (response) {
        final List<EventItem> allEventItems =
            isLoadingMore
                ? [...state.allEventItems, ...response.data]
                : response.data;

        final groupedList = _groupEventsItems(allEventItems);
        final bool hasReachedMax =
            response.count <= startIndex + response.data.length;

        emit(
          state.copyWith(
            processState: const EventsProcessState.success(),
            groupedEvents: groupedList,
            hasReachedMax: hasReachedMax,
            pageSize: _pageSize,
            currentPage: currentPage,
            count: response.count,
            allEventItems: allEventItems,
          ),
        );
      },
    );
  }

  Future<void> _onAddEventToCalendar(
    _AddEventToCalendar event,
    Emitter<EventsState> emit,
  ) async {
    emit(
      state.copyWith(
        processState: const EventsProcessState.calendarMessage(
          message: '',
          eventId: '',
        ),
      ),
    );
    final result = await _calendarUseCase.addEventToCalendar(
      title: event.title,
      subtitle: event.subtitle,
      date: event.date,
    );

    result.fold(
      (error) {
        final message =
            error == CalendarErrorResult.permissionDenied
                ? event.permissionDeniedMessage
                : error == CalendarErrorResult.noCalendarFound
                ? event.noCalendarFoundMessage
                : error == CalendarErrorResult.operationFailed
                ? event.operationFailedMessage
                : event.errorMessage;
        emit(
          state.copyWith(
            processState: EventsProcessState.calendarError(
              title: event.errorMessage,
              message: message,
              eventId: event.eventId,
            ),
          ),
        );
      },
      (success) {
        final message = event.successMessage;
        emit(
          state.copyWith(
            processState: EventsProcessState.calendarMessage(
              message: message,
              eventId: event.eventId,
            ),
          ),
        );
      },
    );
  }

  void _onUpdateDateLabel(UpdateDateLabel event, Emitter<EventsState> emit) {
    if (state.dateLabel == event.dateLabel) return;

    emit(state.copyWith(dateLabel: event.dateLabel));
  }

  Future<void> _onChangeSortOrder(
    _ChangeSortOrder event,
    Emitter<EventsState> emit,
  ) async {
    await _eventsLocalDataUseCase.saveEventsSortingOption(event.sortOrder);
    if (!isClosed) {
      emit(
        state.copyWith(
          processState: EventsProcessState.loading(),
          hasReachedMax: false,
          sortOrder: event.sortOrder,
          currentPage: 0,
        ),
      );

      add(EventsEvent.fetchEvents());
    }
  }

  Future<void> _onSearchEvents(
    SearchEvents event,
    Emitter<EventsState> emit,
  ) async {
    final trimmedQuery = event.query.trim();

    if (trimmedQuery.isEmpty) {
      emit(
        state.copyWith(
          processState: const EventsProcessState.success(),
          groupedEvents: [],
          currentPage: 0,
          hasReachedMax: false,
        ),
      );
      return;
    }

    if (trimmedQuery.length > 1) {
      add(
        EventsEvent.fetchEvents(query: trimmedQuery, loadMore: event.loadMore),
      );

      final previousSearches =
          await _eventsLocalDataUseCase.getEventsPreviousSearches();

      if (!previousSearches.contains(trimmedQuery)) {
        await _eventsLocalDataUseCase.saveEventsPreviousSearches(trimmedQuery);
      }
    }
  }

  List<Object?> _groupEventsItems(List<EventItem> newsItems) {
    final List<Object?> groupedList = [];
    final Map<DayPart, List<EventItem>> labelGroups = {};
    final Map<DateTime, List<EventItem>> specificDateGroups = {};

    for (final item in newsItems) {
      final DayPart label = _getDateLabel(item.item.parsedDate.toLocal());
      if (label == DayPart.specificDate) {
        final dateKey = DateTime(
          item.item.parsedDate.year,
          item.item.parsedDate.month,
          item.item.parsedDate.day,
        );
        specificDateGroups.putIfAbsent(dateKey, () => []).add(item);
      } else {
        labelGroups.putIfAbsent(label, () => []).add(item);
      }
    }

    final orderedDayParts = [
      DayPart.today,
      DayPart.yesterday,
      DayPart.tomorrow,
    ];

    for (final dayPart in orderedDayParts) {
      if (labelGroups.containsKey(dayPart)) {
        groupedList.add(dayPart);
        groupedList.addAll(labelGroups[dayPart]!);
      }
    }

    final sortedDates =
        specificDateGroups.keys.toList()
          ..sort((a, b) => b.compareTo(a)); // Newest first, optional

    for (final date in sortedDates) {
      groupedList.add(date); // ✅ Use DateTime directly
      groupedList.addAll(specificDateGroups[date]!);
    }

    return groupedList;
  }

  DayPart _getDateLabel(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final tomorrow = today.add(Duration(days: 1));
    final itemDate = DateTime(date.year, date.month, date.day);

    if (itemDate == today) {
      return DayPart.today;
    } else if (itemDate == yesterday) {
      return DayPart.yesterday;
    } else if (itemDate == tomorrow) {
      return DayPart.tomorrow;
    }
    return DayPart.specificDate;
  }
}
