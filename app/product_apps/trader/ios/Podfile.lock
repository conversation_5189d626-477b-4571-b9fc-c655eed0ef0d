PODS:
  - audio_session (0.0.1):
    - Flutter
  - Auth0 (2.10.0):
    - JWTDecode (= 3.2.0)
    - SimpleKeychain (= 1.2.0)
  - auth0_flutter (1.10.0):
    - Auth0 (= 2.10.0)
    - Flutter
    - FlutterMacOS
    - JWTDecode (= 3.2.0)
    - SimpleKeychain (= 1.2.0)
  - AuthadaAuthenticationLibrary (1.3.4)
  - cupertino_http (0.0.1):
    - Flutter
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - FirebaseAnalytics (11.14.0):
    - FirebaseAnalytics/Default (= 11.14.0)
    - FirebaseCore (~> 11.14.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.14.0):
    - FirebaseCore (~> 11.14.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.14.0):
    - FirebaseCoreInternal (~> 11.14.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.14.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.14.0):
    - FirebaseCore (~> 11.14.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - Flutter (1.0.0)
  - flutter_idensic_mobile_sdk_plugin (1.34.3):
    - Flutter
    - IdensicMobileSDK (= 1.34.1)
    - IdensicMobileSDK/EID (= 1.34.1)
    - IdensicMobileSDK/MRTDReader (= 1.34.1)
    - IdensicMobileSDK/VideoIdent (= 1.34.1)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_libphonenumber_ios (1.1.4):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (= 3.8.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - GoogleAdsOnDeviceConversion (2.0.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (11.14.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.14.0):
    - GoogleAdsOnDeviceConversion (= 2.0.0)
    - GoogleAppMeasurement/Core (= 11.14.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.14.0):
    - GoogleAppMeasurement/Core (= 11.14.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleTagManager (8.0.0):
    - FirebaseAnalytics (~> 11.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - IdensicMobileSDK (1.34.1):
    - IdensicMobileSDK/Default (= 1.34.1)
  - IdensicMobileSDK/Core (1.34.1)
  - IdensicMobileSDK/Default (1.34.1):
    - IdensicMobileSDK/Core
  - IdensicMobileSDK/EID (1.34.1):
    - AuthadaAuthenticationLibrary (= 1.3.4)
    - IdensicMobileSDK/Core
  - IdensicMobileSDK/MRTDReader (1.34.1):
    - IdensicMobileSDK/MRTDReader-latest
  - IdensicMobileSDK/MRTDReader-latest (1.34.1):
    - IdensicMobileSDK/Core
    - OpenSSL-Universal (>= 3.1.5001)
  - IdensicMobileSDK/VideoIdent (1.34.1):
    - IdensicMobileSDK/VideoIdent-latest
  - IdensicMobileSDK/VideoIdent-latest (1.34.1):
    - IdensicMobileSDK/Core
    - TwilioVideo (>= 5.8.2)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - JWTDecode (3.2.0)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OpenSSL-Universal (3.3.3001)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PhoneNumberKit/PhoneNumberKitCore (3.8.0)
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.1):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SimpleKeychain (1.2.0)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - TwilioVideo (5.9.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - Zendesk (3.10.0):
    - ZendeskSDKConversationKit (~> 11.3.0)
    - ZendeskSDKCoreUtilities (~> 7.2.0)
  - ZendeskCommonUISDK (9.1.0)
  - ZendeskCoreSDK (5.0.5)
  - ZendeskMessagingAPISDK (6.0.2):
    - ZendeskSDKConfigurationsSDK (~> 4.0.1)
  - ZendeskMessagingSDK (6.1.0):
    - ZendeskCommonUISDK (~> 9.1.0)
    - ZendeskMessagingAPISDK (~> 6.0.2)
  - ZendeskSDKConfigurationsSDK (4.0.1)
  - ZendeskSDKConversationKit (11.3.0):
    - ZendeskSDKCoreUtilities (~> 7.2.0)
    - ZendeskSDKFayeClient (~> 1.13.0)
    - ZendeskSDKHTTPClient (~> 0.20.0)
    - ZendeskSDKStorage (~> 1.3.0)
  - ZendeskSDKCoreUtilities (7.2.0)
  - ZendeskSDKFayeClient (1.13.0):
    - ZendeskSDKLogger (~> 0.11.0)
    - ZendeskSDKSocketClient (~> 1.12.0)
  - ZendeskSDKGuideKit (2.5.0):
    - ZendeskSDKCoreUtilities (~> 7.2.0)
    - ZendeskSDKHTTPClient (~> 0.20.0)
    - ZendeskSDKLogger (~> 0.11.0)
    - ZendeskSDKStorage (~> 1.3.0)
  - ZendeskSDKHTTPClient (0.20.0):
    - ZendeskSDKLogger (~> 0.11.0)
  - ZendeskSDKLogger (0.11.0)
  - ZendeskSDKMessaging (2.32.0):
    - Zendesk (~> 3.10.0)
    - ZendeskSDKConversationKit (~> 11.3.0)
    - ZendeskSDKCoreUtilities (~> 7.2.0)
    - ZendeskSDKGuideKit (~> 2.5.0)
    - ZendeskSDKUIComponents (~> 13.3.0)
  - ZendeskSDKSocketClient (1.12.0):
    - ZendeskSDKLogger (~> 0.11.0)
  - ZendeskSDKStorage (1.3.0):
    - ZendeskSDKLogger (~> 0.11.0)
  - ZendeskSDKUIComponents (13.3.0):
    - ZendeskSDKCoreUtilities (~> 7.2.0)
  - ZendeskSupportProvidersSDK (9.0.0):
    - ZendeskCoreSDK (~> 5.0.5)
  - ZendeskSupportSDK (9.1.0):
    - ZendeskMessagingSDK (~> 6.1.0)
    - ZendeskSupportProvidersSDK (~> 9.0.0)

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - auth0_flutter (from `.symlinks/plugins/auth0_flutter/darwin`)
  - cupertino_http (from `.symlinks/plugins/cupertino_http/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_idensic_mobile_sdk_plugin (from `.symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_libphonenumber_ios (from `.symlinks/plugins/flutter_libphonenumber_ios/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - GoogleTagManager
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - ZendeskSDKMessaging
  - ZendeskSupportSDK

SPEC REPOS:
  https://github.com/SumSubstance/Specs.git:
    - AuthadaAuthenticationLibrary
    - IdensicMobileSDK
  trunk:
    - Auth0
    - DKImagePickerController
    - DKPhotoGallery
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleTagManager
    - GoogleUtilities
    - JWTDecode
    - nanopb
    - OpenSSL-Universal
    - OrderedSet
    - PhoneNumberKit
    - PromisesObjC
    - SDWebImage
    - Sentry
    - SimpleKeychain
    - SwiftyGif
    - TwilioVideo
    - Zendesk
    - ZendeskCommonUISDK
    - ZendeskCoreSDK
    - ZendeskMessagingAPISDK
    - ZendeskMessagingSDK
    - ZendeskSDKConfigurationsSDK
    - ZendeskSDKConversationKit
    - ZendeskSDKCoreUtilities
    - ZendeskSDKFayeClient
    - ZendeskSDKGuideKit
    - ZendeskSDKHTTPClient
    - ZendeskSDKLogger
    - ZendeskSDKMessaging
    - ZendeskSDKSocketClient
    - ZendeskSDKStorage
    - ZendeskSDKUIComponents
    - ZendeskSupportProvidersSDK
    - ZendeskSupportSDK

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  auth0_flutter:
    :path: ".symlinks/plugins/auth0_flutter/darwin"
  cupertino_http:
    :path: ".symlinks/plugins/cupertino_http/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_idensic_mobile_sdk_plugin:
    :path: ".symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_libphonenumber_ios:
    :path: ".symlinks/plugins/flutter_libphonenumber_ios/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  Auth0: 2876d0c36857422eda9cb580a6cc896c7d14cb36
  auth0_flutter: 5b921a0291e9cf364ffd473f7689156df88356b3
  AuthadaAuthenticationLibrary: 716df56f6296af3f2220ec9e78459f71fd8ef22e
  cupertino_http: 6c6bff76e2f6915cd6b22e0e6d2698309339ec46
  device_calendar: b55b2c5406cfba45c95a59f9059156daee1f74ed
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  FirebaseAnalytics: d604dc875290f0213b9ead8a4692cf1e91e024d9
  FirebaseCore: 8fb12caed934c900218ce66a419107db74214ade
  FirebaseCoreInternal: 6a3b668197644aa858fc4127578637c6767ba123
  FirebaseInstallations: 863f2846bb124331b501d29c0f17f95caa2ef6bb
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_idensic_mobile_sdk_plugin: b80735cb440daef8299714415aca17c4c183555e
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_libphonenumber_ios: 6bd2fac9dfb8f37c5732451fb7c9a992f1088d86
  flutter_native_splash: 576fbd69b830a63594ae678396fa17e43abbc5f8
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_timezone: ee50ce7786b5fde27e2fe5375bbc8c9661ffc13f
  GoogleAdsOnDeviceConversion: 5c3c8de58786e7d0a4bdecbd32c16f87d815cc9f
  GoogleAppMeasurement: 078fe9bfeed8e398253772ce81ef8690b0413b45
  GoogleTagManager: 05f257a9afe6e1d6da24f0ee37ab255ce34108b6
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  IdensicMobileSDK: 1c37c8a37df3fedf4f60937f66abc13885c21fe5
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  JWTDecode: 7dae24cb9bf9b608eae61e5081029ec169bb5527
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OpenSSL-Universal: 6082b0bf950e5636fe0d78def171184e2b3899c2
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PhoneNumberKit: ec00ab8cef5342c1dc49fadb99d23fa7e66cf0ef
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 942017adbe00f963061cb11ec260414a990b7a42
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SimpleKeychain: 768cf43ae778b1c21816e94dddf01bb8ee96a075
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  syncfusion_flutter_pdfviewer: 90dc48305d2e33d4aa20681d1e98ddeda891bc14
  TwilioVideo: 4e8d54f4c5c84f8d9bf6e25fdafa44dece6e849a
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  Zendesk: e3fe7a319ff76d38d38baabf38f1bb641dcd24b8
  ZendeskCommonUISDK: 1ad4f0051a999a5e56f09f789f916afe15e00419
  ZendeskCoreSDK: c542b3f82abc3ee52e1291c6fb3436515e098806
  ZendeskMessagingAPISDK: 5b3a9d16f2f4d8b3c07b4ca0451a7cf9bfd8107d
  ZendeskMessagingSDK: 62eb7832104fbabcb4c2421a75823460f1ff1536
  ZendeskSDKConfigurationsSDK: d0b28eeb9c3d88191cfd2c7f246b2522c8c357f4
  ZendeskSDKConversationKit: 62ce7e4cba75b718e1ff079dae051e6a562f7f15
  ZendeskSDKCoreUtilities: be8ad25c945ae13ee8df4a34b361021d30f8589b
  ZendeskSDKFayeClient: 4beff369ad002de9603e6a8c3ed5f2e45cf4f5cc
  ZendeskSDKGuideKit: eff47ad486246b65e9fc199b7126b42050bd6f59
  ZendeskSDKHTTPClient: 1cfbcd7705614532ac6de747d00d038dc4f06625
  ZendeskSDKLogger: aff150c8ce1558dc2ba464dc42d420d14c9b1369
  ZendeskSDKMessaging: 1cb3f5475648c2f316d5b13f8cdb3744cb1b0291
  ZendeskSDKSocketClient: 8e5af65250555a4235ab1936b9549f96321054e1
  ZendeskSDKStorage: 8567fcb49ddbae493f431a8d0edaae51872c9ec1
  ZendeskSDKUIComponents: c0bcaaaa706b57fe580bcf17917f56f667604d62
  ZendeskSupportProvidersSDK: 281acf2bb731d2a67f913cfe653ed0da9f5b2f42
  ZendeskSupportSDK: 7fd3e7b70b3941e6da069d06deba2c9316d36088

PODFILE CHECKSUM: 8bbafe79e590eda7ead5d04d55ea822658e0b599

COCOAPODS: 1.16.2
