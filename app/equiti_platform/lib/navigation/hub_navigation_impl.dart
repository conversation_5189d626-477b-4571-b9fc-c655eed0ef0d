import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:login/login.dart';
import 'package:payment/payments.dart';
import 'package:user_account/user_account.dart';

class HubNavigationImpl implements HubNavigation {
  const HubNavigationImpl();

  @override
  void gotoBrokerage() {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.switchAccountRoute.label,
    );
  }

  @override
  void gotoGold() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.newProductInfoRoute.label,
      arguments: {"identifier": "promotion_gold"},
    );
  }

  @override
  void gotoWealth() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.newProductInfoRoute.label,
      arguments: {"identifier": "promotion_wealth"},
    );
  }

  @override
  void gotoSettings() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.hubSettingsRoute.label,
    );
  }

  @override
  void gotoProfile(ClientProfileData clientProfileData) {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.profileRoute.label,
      arguments: clientProfileData,
    );
  }

  @override
  void gotoHistoricalPerformance() {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.historicalPerformanceRoute.label,
    );
  }

  @override
  void logout() {
    diContainer<LogoutUseCase>().logout(
      onSuccess: () {
        diContainer<EquitiTraderNavigation>().navigateToLoginOptions();
      },
      onFailure: () {
        log("Logout failed");
      },
    );
  }

  @override
  void goToDepositPaymentOptions(DepositFlowConfig depositFlowConfig) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositOptionsRoute.label,
      arguments: depositFlowConfig,
    );
  }

  @override
  void goToWithdrawPaymentOptions() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawOptionsRoute.label,
    );
  }

  @override
  void goToTransferFundsScreen() {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsScreen.label,
    );
  }

  @override
  void goToTrading() {
    final account = diContainer<GetSelectedAccountUseCase>().call();
    if (account == null) {
      diContainer<EquitiNavigatorBase>().push(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        EquitiTraderRouteSchema.navBarRoute.label,
      );
    }
  }
}
