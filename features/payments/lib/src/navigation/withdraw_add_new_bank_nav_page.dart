import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/navigation/payment_route_schema.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/withdraw_add_new_bank.dart';

class WithdrawAddNewBankNavPage extends EquitiPage {
  const WithdrawAddNewBankNavPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    return WithdrawAddNewBank(
      withdrawFlowParams:
          globalData['withdrawFlowParams'] as WithdrawFlowParams,
    );
  }

  @override
  String get label => PaymentRouteSchema.withdrawAddNewBankRoute.label;

  @override
  String get url => PaymentRouteSchema.withdrawAddNewBankRoute.url;
}
