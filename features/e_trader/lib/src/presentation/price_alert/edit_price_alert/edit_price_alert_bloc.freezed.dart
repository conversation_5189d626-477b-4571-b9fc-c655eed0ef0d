// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_price_alert_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$EditPriceAlertEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditPriceAlertEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EditPriceAlertEvent()';
}


}

/// @nodoc
class $EditPriceAlertEventCopyWith<$Res>  {
$EditPriceAlertEventCopyWith(EditPriceAlertEvent _, $Res Function(EditPriceAlertEvent) __);
}


/// @nodoc


class _PercentOptionChanged implements EditPriceAlertEvent {
  const _PercentOptionChanged(this.index);
  

 final  int index;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PercentOptionChangedCopyWith<_PercentOptionChanged> get copyWith => __$PercentOptionChangedCopyWithImpl<_PercentOptionChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PercentOptionChanged&&(identical(other.index, index) || other.index == index));
}


@override
int get hashCode => Object.hash(runtimeType,index);

@override
String toString() {
  return 'EditPriceAlertEvent.percentOptionChanged(index: $index)';
}


}

/// @nodoc
abstract mixin class _$PercentOptionChangedCopyWith<$Res> implements $EditPriceAlertEventCopyWith<$Res> {
  factory _$PercentOptionChangedCopyWith(_PercentOptionChanged value, $Res Function(_PercentOptionChanged) _then) = __$PercentOptionChangedCopyWithImpl;
@useResult
$Res call({
 int index
});




}
/// @nodoc
class __$PercentOptionChangedCopyWithImpl<$Res>
    implements _$PercentOptionChangedCopyWith<$Res> {
  __$PercentOptionChangedCopyWithImpl(this._self, this._then);

  final _PercentOptionChanged _self;
  final $Res Function(_PercentOptionChanged) _then;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? index = null,}) {
  return _then(_PercentOptionChanged(
null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _SymbolPriceChanged implements EditPriceAlertEvent {
  const _SymbolPriceChanged(this.symbolPrice);
  

 final  double symbolPrice;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolPriceChangedCopyWith<_SymbolPriceChanged> get copyWith => __$SymbolPriceChangedCopyWithImpl<_SymbolPriceChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SymbolPriceChanged&&(identical(other.symbolPrice, symbolPrice) || other.symbolPrice == symbolPrice));
}


@override
int get hashCode => Object.hash(runtimeType,symbolPrice);

@override
String toString() {
  return 'EditPriceAlertEvent.symbolPriceChanged(symbolPrice: $symbolPrice)';
}


}

/// @nodoc
abstract mixin class _$SymbolPriceChangedCopyWith<$Res> implements $EditPriceAlertEventCopyWith<$Res> {
  factory _$SymbolPriceChangedCopyWith(_SymbolPriceChanged value, $Res Function(_SymbolPriceChanged) _then) = __$SymbolPriceChangedCopyWithImpl;
@useResult
$Res call({
 double symbolPrice
});




}
/// @nodoc
class __$SymbolPriceChangedCopyWithImpl<$Res>
    implements _$SymbolPriceChangedCopyWith<$Res> {
  __$SymbolPriceChangedCopyWithImpl(this._self, this._then);

  final _SymbolPriceChanged _self;
  final $Res Function(_SymbolPriceChanged) _then;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolPrice = null,}) {
  return _then(_SymbolPriceChanged(
null == symbolPrice ? _self.symbolPrice : symbolPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _TradeTypeChanged implements EditPriceAlertEvent {
  const _TradeTypeChanged(this.tradeType, this.symbolPrice);
  

 final  TradeType tradeType;
 final  double symbolPrice;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TradeTypeChangedCopyWith<_TradeTypeChanged> get copyWith => __$TradeTypeChangedCopyWithImpl<_TradeTypeChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TradeTypeChanged&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.symbolPrice, symbolPrice) || other.symbolPrice == symbolPrice));
}


@override
int get hashCode => Object.hash(runtimeType,tradeType,symbolPrice);

@override
String toString() {
  return 'EditPriceAlertEvent.tradeTypeChanged(tradeType: $tradeType, symbolPrice: $symbolPrice)';
}


}

/// @nodoc
abstract mixin class _$TradeTypeChangedCopyWith<$Res> implements $EditPriceAlertEventCopyWith<$Res> {
  factory _$TradeTypeChangedCopyWith(_TradeTypeChanged value, $Res Function(_TradeTypeChanged) _then) = __$TradeTypeChangedCopyWithImpl;
@useResult
$Res call({
 TradeType tradeType, double symbolPrice
});




}
/// @nodoc
class __$TradeTypeChangedCopyWithImpl<$Res>
    implements _$TradeTypeChangedCopyWith<$Res> {
  __$TradeTypeChangedCopyWithImpl(this._self, this._then);

  final _TradeTypeChanged _self;
  final $Res Function(_TradeTypeChanged) _then;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tradeType = null,Object? symbolPrice = null,}) {
  return _then(_TradeTypeChanged(
null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,null == symbolPrice ? _self.symbolPrice : symbolPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _SetInitialValues implements EditPriceAlertEvent {
  const _SetInitialValues(this.tradeType, this.symbolPrice, this.digits, this.enteredPrice);
  

 final  TradeType tradeType;
 final  double symbolPrice;
 final  int digits;
 final  double? enteredPrice;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetInitialValuesCopyWith<_SetInitialValues> get copyWith => __$SetInitialValuesCopyWithImpl<_SetInitialValues>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetInitialValues&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.symbolPrice, symbolPrice) || other.symbolPrice == symbolPrice)&&(identical(other.digits, digits) || other.digits == digits)&&(identical(other.enteredPrice, enteredPrice) || other.enteredPrice == enteredPrice));
}


@override
int get hashCode => Object.hash(runtimeType,tradeType,symbolPrice,digits,enteredPrice);

@override
String toString() {
  return 'EditPriceAlertEvent.setInitialValues(tradeType: $tradeType, symbolPrice: $symbolPrice, digits: $digits, enteredPrice: $enteredPrice)';
}


}

/// @nodoc
abstract mixin class _$SetInitialValuesCopyWith<$Res> implements $EditPriceAlertEventCopyWith<$Res> {
  factory _$SetInitialValuesCopyWith(_SetInitialValues value, $Res Function(_SetInitialValues) _then) = __$SetInitialValuesCopyWithImpl;
@useResult
$Res call({
 TradeType tradeType, double symbolPrice, int digits, double? enteredPrice
});




}
/// @nodoc
class __$SetInitialValuesCopyWithImpl<$Res>
    implements _$SetInitialValuesCopyWith<$Res> {
  __$SetInitialValuesCopyWithImpl(this._self, this._then);

  final _SetInitialValues _self;
  final $Res Function(_SetInitialValues) _then;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tradeType = null,Object? symbolPrice = null,Object? digits = null,Object? enteredPrice = freezed,}) {
  return _then(_SetInitialValues(
null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,null == symbolPrice ? _self.symbolPrice : symbolPrice // ignore: cast_nullable_to_non_nullable
as double,null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,freezed == enteredPrice ? _self.enteredPrice : enteredPrice // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

/// @nodoc


class _InputFieldValueChanged implements EditPriceAlertEvent {
  const _InputFieldValueChanged(this.value);
  

 final  String value;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InputFieldValueChangedCopyWith<_InputFieldValueChanged> get copyWith => __$InputFieldValueChangedCopyWithImpl<_InputFieldValueChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InputFieldValueChanged&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'EditPriceAlertEvent.inputFieldValueChanged(value: $value)';
}


}

/// @nodoc
abstract mixin class _$InputFieldValueChangedCopyWith<$Res> implements $EditPriceAlertEventCopyWith<$Res> {
  factory _$InputFieldValueChangedCopyWith(_InputFieldValueChanged value, $Res Function(_InputFieldValueChanged) _then) = __$InputFieldValueChangedCopyWithImpl;
@useResult
$Res call({
 String value
});




}
/// @nodoc
class __$InputFieldValueChangedCopyWithImpl<$Res>
    implements _$InputFieldValueChangedCopyWith<$Res> {
  __$InputFieldValueChangedCopyWithImpl(this._self, this._then);

  final _InputFieldValueChanged _self;
  final $Res Function(_InputFieldValueChanged) _then;

/// Create a copy of EditPriceAlertEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? value = null,}) {
  return _then(_InputFieldValueChanged(
null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$EditPriceAlertState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditPriceAlertState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EditPriceAlertState()';
}


}

/// @nodoc
class $EditPriceAlertStateCopyWith<$Res>  {
$EditPriceAlertStateCopyWith(EditPriceAlertState _, $Res Function(EditPriceAlertState) __);
}


/// @nodoc


class EditPriceAlertLoading implements EditPriceAlertState {
  const EditPriceAlertLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditPriceAlertLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EditPriceAlertState.loading()';
}


}




/// @nodoc


class EditPriceAlertError implements EditPriceAlertState {
  const EditPriceAlertError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditPriceAlertError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'EditPriceAlertState.error()';
}


}




/// @nodoc


class EditPriceAlertSuccess implements EditPriceAlertState {
  const EditPriceAlertSuccess(this.info);
  

 final  EditedPriceAlertModel info;

/// Create a copy of EditPriceAlertState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EditPriceAlertSuccessCopyWith<EditPriceAlertSuccess> get copyWith => _$EditPriceAlertSuccessCopyWithImpl<EditPriceAlertSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EditPriceAlertSuccess&&(identical(other.info, info) || other.info == info));
}


@override
int get hashCode => Object.hash(runtimeType,info);

@override
String toString() {
  return 'EditPriceAlertState.success(info: $info)';
}


}

/// @nodoc
abstract mixin class $EditPriceAlertSuccessCopyWith<$Res> implements $EditPriceAlertStateCopyWith<$Res> {
  factory $EditPriceAlertSuccessCopyWith(EditPriceAlertSuccess value, $Res Function(EditPriceAlertSuccess) _then) = _$EditPriceAlertSuccessCopyWithImpl;
@useResult
$Res call({
 EditedPriceAlertModel info
});


$EditedPriceAlertModelCopyWith<$Res> get info;

}
/// @nodoc
class _$EditPriceAlertSuccessCopyWithImpl<$Res>
    implements $EditPriceAlertSuccessCopyWith<$Res> {
  _$EditPriceAlertSuccessCopyWithImpl(this._self, this._then);

  final EditPriceAlertSuccess _self;
  final $Res Function(EditPriceAlertSuccess) _then;

/// Create a copy of EditPriceAlertState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? info = null,}) {
  return _then(EditPriceAlertSuccess(
null == info ? _self.info : info // ignore: cast_nullable_to_non_nullable
as EditedPriceAlertModel,
  ));
}

/// Create a copy of EditPriceAlertState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EditedPriceAlertModelCopyWith<$Res> get info {
  
  return $EditedPriceAlertModelCopyWith<$Res>(_self.info, (value) {
    return _then(_self.copyWith(info: value));
  });
}
}

// dart format on
