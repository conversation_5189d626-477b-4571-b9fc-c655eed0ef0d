import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/statements_tab.dart';
import 'scenarios/statements_empty_scenario.dart';
import 'scenarios/statements_success_scenario.dart';
import 'scenarios/events_empty_scenario.dart';
import 'scenarios/events_failure_scenario.dart';
import 'scenarios/events_success_scenario.dart';
import 'scenarios/news_success_scenario.dart';
import 'package:e_trader/src/presentation/discover/discover_screen.dart';


Feature: Discover Screen


  @testMethodName: testGoldens


  @testMethodName: testGoldens
  Scenario: Open news tab
    Given The {DiscoverScreen()} app is rendered {scenarios: [newsSuccessScenario]}
    And I wait
    Then screenshot verified {'open_news_tab'} with custom pump
   


  @testMethodName: testGoldens
   Scenario: Faild loading news
     Given The {DiscoverScreen()} app is rendered {scenarios: [newsFailureScenario]}
     And i Wait for {1} seconds
     And screenshot verified {'failed_laoding_news'}     
 
  @testMethodName: testGoldens
  Scenario: Empty events
    Given The {DiscoverScreen()} app is rendered {scenarios: [eventsEmptyScenario]}
    And I tap {'discover_events_tab'} identifier
    And i Wait for {1} seconds
    And screenshot verified {'empty_events'}    



   @testMethodName: testGoldens
   Scenario: Events tab
     Given The {DiscoverScreen()} app is rendered {scenarios: [eventsSuccessScenario]}
     And I wait
    And I tap {'discover_events_tab'} identifier
    And i Wait for {1} seconds
     And screenshot verified {'event_tab'}     
 
   @testMethodName: testGoldens
   Scenario: News search
     Given The {DiscoverScreen()} app is rendered {scenarios: [newsSuccessScenario]}
     When I tap on the DuploSearchBox
     When I enter {"CAD"} in the search field
     And I wait
     And screenshot verified {'news_search_screen'}   


