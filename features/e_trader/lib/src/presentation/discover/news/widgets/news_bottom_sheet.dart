import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/model/news_events_sort_order.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_details_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NewsBottomSheet {
  const NewsBottomSheet();

  static void showSort(BuildContext context) {
    final loc = EquitiLocalization.of(context);

    final existing = context.read<NewsBloc>().state.sortOrder;
    final allSortOptions = <SelectionOptionModel>[];

    SelectionOptionModel selected = SelectionOptionModel(
      displayText: NewsEventSortOrderOptions.defaultOption.display(loc),
      identifier: NewsEventSortOrderOptions.defaultOption.indentifier(),
    );
    NewsEventsSortOrder.values.forEach((element) {
      final model = SelectionOptionModel(
        displayText: element.display(loc),
        identifier: element.indentifier(),
      );
      if (model.identifier == existing.indentifier()) {
        selected = model;
      }
      allSortOptions.add(model);
    });

    final textSelection = TextSelectionComponentScreen(
      buttonTitle: loc.trader_sortMarkets,
      options: allSortOptions.reversed.toList(),
      pageTitle: loc.trader_sortListBy,
      selected: selected,
      onSelection: (selectedOption) {
        final order = selectedOption.identifier.toSortOrder();
        if (order != existing) {
          context.read<NewsBloc>().add(
            NewsEvent.changeSortOrder(sortOrder: order),
          );
        }

        Navigator.pop(context);
      },
    );

    DuploSheet.showNonScrollableModalSheet<void>(
      context: context,
      title: loc.trader_sortNewsBy,
      content: (ctx) => textSelection,
    );
  }

  static void showDetails({
    required BuildContext context,
    required NewsItemDetails newsItemDetails,
  }) {
    final loc = EquitiLocalization.of(context);

    DuploSheet.showModalSheetV2<void>(
      context,
      appBar: DuploAppBar(
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        title: loc.trader_news,
        titleWidget: DuploText(
          text: loc.trader_news,
          style: context.duploTextStyles.textLg,
          fontWeight: DuploFontWeight.bold,
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),

      content: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 16),
        child: Column(
          children: [
            Divider(color: context.duploTheme.border.borderSecondary),
            NewsDetailsView(newsItemDetails: newsItemDetails),
          ],
        ),
      ),
    );
  }
}
