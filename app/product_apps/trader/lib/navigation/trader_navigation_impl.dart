import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';

class TraderNavigationImpl extends EquitiTraderNavigation {
  TraderNavigationImpl({required this.navigatorKey});
  final GlobalKey<NavigatorState> navigatorKey;

  @override
  void navigateToSymbols() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(builder: (context) => const SymbolsScreen()),
    );
  }

  @override
  void navigateToProductDetail({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
  }) {
    navigatorKey.currentState!.push<Object?>(
      MaterialPageRoute<void>(
        builder:
            (context) => ProductDetailsWidget(
              symbolDetail: symbolDetail,
              accountNumber: accountNumber,
            ),
      ),
    );
  }

  @override
  void navigateToTradingPrefrences() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder: (context) => const TradingSettingsScreen(),
      ),
    );
  }

  @override
  void navigateToPortfolio({TradeConfirmationResult? result}) {
    navigatorKey.currentState!.pushAndRemoveUntil(
      MaterialPageRoute<void>(builder: (_) => const NavigationBottomBar()),
      (route) => true,
    );
  }

  @override
  navigateToLogin({String? email}) {
    print("navigateToLogin");
  }

  @override
  void navigateToLoginOptions() {
    print("navigateToLoginOptions");
  }

  @override
  void navigateToSignUpOptions() {
    print("navigateToSignUpOptions");
  }

  @override
  void navigateToFullChart({required String symbol, required int digit}) {
    print("navigateToFullChart");
  }

  @override
  void navigateToDepositOptions({
    required DepositFlowConfig depositFlowConfig,
  }) {
    throw UnimplementedError();
  }

  @override
  void navigateToWithdrawOptions() {
    throw UnimplementedError();
  }

  @override
  navigateToHub() {
    throw UnimplementedError();
  }

  @override
  void navigateToSwitchAccounts() {
    throw UnimplementedError();
  }

  @override
  void goToTransferFundsScreen() {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder: (context) => const TransferFundsScreen(),
      ),
    );
  }

  @override
  void navigateToPerformance() {
    print('Navigate to Performance ');
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    navigatorKey.currentState!.push<void>(
      MaterialPageRoute<void>(
        builder:
            (context) =>
                CreateAccountMainScreen(createAccountFlow: createAccountFlow),
      ),
    );
  }
}
