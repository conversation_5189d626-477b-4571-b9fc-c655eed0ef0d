part of 'accounts_bloc.dart';

@freezed
sealed class AccountsEvent with _$AccountsEvent {
  const factory AccountsEvent.fetch() = _FetchAccounts;
  const factory AccountsEvent.refreshAccounts() = _RefreshAccounts;
  const factory AccountsEvent.onAccountSelected({required String accountId}) =
      _OnAccountSelected;
  const factory AccountsEvent.goToSymbols() = _GoToSymbols;
  const factory AccountsEvent.goToDepositPaymentOptions() =
      _GoToDepositPaymentOptions;
  const factory AccountsEvent.goToWithdrawPaymentOptions() =
      _GoToWithdrawPaymentOptions;
  const factory AccountsEvent.goToTransferOptions() = _GoToTransferOptions;
}
