// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_bank_transfer_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WithdrawBankTransferEvent implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent()';
}


}

/// @nodoc
class $WithdrawBankTransferEventCopyWith<$Res>  {
$WithdrawBankTransferEventCopyWith(WithdrawBankTransferEvent _, $Res Function(WithdrawBankTransferEvent) __);
}


/// @nodoc


class _FetchBankAccounts with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _FetchBankAccounts(this.tradingAccountId);
  

 final  String tradingAccountId;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FetchBankAccountsCopyWith<_FetchBankAccounts> get copyWith => __$FetchBankAccountsCopyWithImpl<_FetchBankAccounts>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.fetchBankAccounts'))
    ..add(DiagnosticsProperty('tradingAccountId', tradingAccountId));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FetchBankAccounts&&(identical(other.tradingAccountId, tradingAccountId) || other.tradingAccountId == tradingAccountId));
}


@override
int get hashCode => Object.hash(runtimeType,tradingAccountId);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.fetchBankAccounts(tradingAccountId: $tradingAccountId)';
}


}

/// @nodoc
abstract mixin class _$FetchBankAccountsCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$FetchBankAccountsCopyWith(_FetchBankAccounts value, $Res Function(_FetchBankAccounts) _then) = __$FetchBankAccountsCopyWithImpl;
@useResult
$Res call({
 String tradingAccountId
});




}
/// @nodoc
class __$FetchBankAccountsCopyWithImpl<$Res>
    implements _$FetchBankAccountsCopyWith<$Res> {
  __$FetchBankAccountsCopyWithImpl(this._self, this._then);

  final _FetchBankAccounts _self;
  final $Res Function(_FetchBankAccounts) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tradingAccountId = null,}) {
  return _then(_FetchBankAccounts(
null == tradingAccountId ? _self.tradingAccountId : tradingAccountId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ChangeSelectedIndex with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _ChangeSelectedIndex(this.index);
  

 final  int index;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeSelectedIndexCopyWith<_ChangeSelectedIndex> get copyWith => __$ChangeSelectedIndexCopyWithImpl<_ChangeSelectedIndex>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.changeSelectedIndex'))
    ..add(DiagnosticsProperty('index', index));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeSelectedIndex&&(identical(other.index, index) || other.index == index));
}


@override
int get hashCode => Object.hash(runtimeType,index);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.changeSelectedIndex(index: $index)';
}


}

/// @nodoc
abstract mixin class _$ChangeSelectedIndexCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$ChangeSelectedIndexCopyWith(_ChangeSelectedIndex value, $Res Function(_ChangeSelectedIndex) _then) = __$ChangeSelectedIndexCopyWithImpl;
@useResult
$Res call({
 int index
});




}
/// @nodoc
class __$ChangeSelectedIndexCopyWithImpl<$Res>
    implements _$ChangeSelectedIndexCopyWith<$Res> {
  __$ChangeSelectedIndexCopyWithImpl(this._self, this._then);

  final _ChangeSelectedIndex _self;
  final $Res Function(_ChangeSelectedIndex) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? index = null,}) {
  return _then(_ChangeSelectedIndex(
null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _OnPressContinue with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _OnPressContinue(this.bankTransferAmountModel);
  

 final  WithdrawFlowParams bankTransferAmountModel;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnPressContinueCopyWith<_OnPressContinue> get copyWith => __$OnPressContinueCopyWithImpl<_OnPressContinue>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.onPressContinue'))
    ..add(DiagnosticsProperty('bankTransferAmountModel', bankTransferAmountModel));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnPressContinue&&(identical(other.bankTransferAmountModel, bankTransferAmountModel) || other.bankTransferAmountModel == bankTransferAmountModel));
}


@override
int get hashCode => Object.hash(runtimeType,bankTransferAmountModel);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.onPressContinue(bankTransferAmountModel: $bankTransferAmountModel)';
}


}

/// @nodoc
abstract mixin class _$OnPressContinueCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$OnPressContinueCopyWith(_OnPressContinue value, $Res Function(_OnPressContinue) _then) = __$OnPressContinueCopyWithImpl;
@useResult
$Res call({
 WithdrawFlowParams bankTransferAmountModel
});


$WithdrawFlowParamsCopyWith<$Res> get bankTransferAmountModel;

}
/// @nodoc
class __$OnPressContinueCopyWithImpl<$Res>
    implements _$OnPressContinueCopyWith<$Res> {
  __$OnPressContinueCopyWithImpl(this._self, this._then);

  final _OnPressContinue _self;
  final $Res Function(_OnPressContinue) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? bankTransferAmountModel = null,}) {
  return _then(_OnPressContinue(
null == bankTransferAmountModel ? _self.bankTransferAmountModel : bankTransferAmountModel // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get bankTransferAmountModel {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.bankTransferAmountModel, (value) {
    return _then(_self.copyWith(bankTransferAmountModel: value));
  });
}
}

/// @nodoc


class _OnChangeMode with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _OnChangeMode(this.mode);
  

 final  ScreenMode mode;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnChangeModeCopyWith<_OnChangeMode> get copyWith => __$OnChangeModeCopyWithImpl<_OnChangeMode>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.onChangeMode'))
    ..add(DiagnosticsProperty('mode', mode));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChangeMode&&(identical(other.mode, mode) || other.mode == mode));
}


@override
int get hashCode => Object.hash(runtimeType,mode);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.onChangeMode(mode: $mode)';
}


}

/// @nodoc
abstract mixin class _$OnChangeModeCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$OnChangeModeCopyWith(_OnChangeMode value, $Res Function(_OnChangeMode) _then) = __$OnChangeModeCopyWithImpl;
@useResult
$Res call({
 ScreenMode mode
});




}
/// @nodoc
class __$OnChangeModeCopyWithImpl<$Res>
    implements _$OnChangeModeCopyWith<$Res> {
  __$OnChangeModeCopyWithImpl(this._self, this._then);

  final _OnChangeMode _self;
  final $Res Function(_OnChangeMode) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? mode = null,}) {
  return _then(_OnChangeMode(
null == mode ? _self.mode : mode // ignore: cast_nullable_to_non_nullable
as ScreenMode,
  ));
}


}

/// @nodoc


class _OnDeleteBankAccount with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _OnDeleteBankAccount(this.bankAccountModel);
  

 final  BankAccountData bankAccountModel;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnDeleteBankAccountCopyWith<_OnDeleteBankAccount> get copyWith => __$OnDeleteBankAccountCopyWithImpl<_OnDeleteBankAccount>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.onDeleteBankAccount'))
    ..add(DiagnosticsProperty('bankAccountModel', bankAccountModel));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnDeleteBankAccount&&(identical(other.bankAccountModel, bankAccountModel) || other.bankAccountModel == bankAccountModel));
}


@override
int get hashCode => Object.hash(runtimeType,bankAccountModel);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.onDeleteBankAccount(bankAccountModel: $bankAccountModel)';
}


}

/// @nodoc
abstract mixin class _$OnDeleteBankAccountCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$OnDeleteBankAccountCopyWith(_OnDeleteBankAccount value, $Res Function(_OnDeleteBankAccount) _then) = __$OnDeleteBankAccountCopyWithImpl;
@useResult
$Res call({
 BankAccountData bankAccountModel
});


$BankAccountDataCopyWith<$Res> get bankAccountModel;

}
/// @nodoc
class __$OnDeleteBankAccountCopyWithImpl<$Res>
    implements _$OnDeleteBankAccountCopyWith<$Res> {
  __$OnDeleteBankAccountCopyWithImpl(this._self, this._then);

  final _OnDeleteBankAccount _self;
  final $Res Function(_OnDeleteBankAccount) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? bankAccountModel = null,}) {
  return _then(_OnDeleteBankAccount(
null == bankAccountModel ? _self.bankAccountModel : bankAccountModel // ignore: cast_nullable_to_non_nullable
as BankAccountData,
  ));
}

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BankAccountDataCopyWith<$Res> get bankAccountModel {
  
  return $BankAccountDataCopyWith<$Res>(_self.bankAccountModel, (value) {
    return _then(_self.copyWith(bankAccountModel: value));
  });
}
}

/// @nodoc


class _OnAddNewAccountPressed with DiagnosticableTreeMixin implements WithdrawBankTransferEvent {
  const _OnAddNewAccountPressed(this.bankTransferAmountModel);
  

 final  WithdrawFlowParams bankTransferAmountModel;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAddNewAccountPressedCopyWith<_OnAddNewAccountPressed> get copyWith => __$OnAddNewAccountPressedCopyWithImpl<_OnAddNewAccountPressed>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferEvent.onAddNewAccountPressed'))
    ..add(DiagnosticsProperty('bankTransferAmountModel', bankTransferAmountModel));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAddNewAccountPressed&&(identical(other.bankTransferAmountModel, bankTransferAmountModel) || other.bankTransferAmountModel == bankTransferAmountModel));
}


@override
int get hashCode => Object.hash(runtimeType,bankTransferAmountModel);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferEvent.onAddNewAccountPressed(bankTransferAmountModel: $bankTransferAmountModel)';
}


}

/// @nodoc
abstract mixin class _$OnAddNewAccountPressedCopyWith<$Res> implements $WithdrawBankTransferEventCopyWith<$Res> {
  factory _$OnAddNewAccountPressedCopyWith(_OnAddNewAccountPressed value, $Res Function(_OnAddNewAccountPressed) _then) = __$OnAddNewAccountPressedCopyWithImpl;
@useResult
$Res call({
 WithdrawFlowParams bankTransferAmountModel
});


$WithdrawFlowParamsCopyWith<$Res> get bankTransferAmountModel;

}
/// @nodoc
class __$OnAddNewAccountPressedCopyWithImpl<$Res>
    implements _$OnAddNewAccountPressedCopyWith<$Res> {
  __$OnAddNewAccountPressedCopyWithImpl(this._self, this._then);

  final _OnAddNewAccountPressed _self;
  final $Res Function(_OnAddNewAccountPressed) _then;

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? bankTransferAmountModel = null,}) {
  return _then(_OnAddNewAccountPressed(
null == bankTransferAmountModel ? _self.bankTransferAmountModel : bankTransferAmountModel // ignore: cast_nullable_to_non_nullable
as WithdrawFlowParams,
  ));
}

/// Create a copy of WithdrawBankTransferEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<$Res> get bankTransferAmountModel {
  
  return $WithdrawFlowParamsCopyWith<$Res>(_self.bankTransferAmountModel, (value) {
    return _then(_self.copyWith(bankTransferAmountModel: value));
  });
}
}

/// @nodoc
mixin _$WithdrawBankTransferState implements DiagnosticableTreeMixin {

 List<BankAccountData> get bankAccounts; ScreenMode get screenMode; bool get isDeleteAccountButtonLoading; bool get shouldShowDeleteAccountErrorBottomSheet; bool get dismissDeleteConfirmationBottomSheet;// Fixed typo
 int? get banksLimit; int? get index; WithdrawBankTransferProccessState get withdrawBankTransferProccessState;
/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawBankTransferStateCopyWith<WithdrawBankTransferState> get copyWith => _$WithdrawBankTransferStateCopyWithImpl<WithdrawBankTransferState>(this as WithdrawBankTransferState, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferState'))
    ..add(DiagnosticsProperty('bankAccounts', bankAccounts))..add(DiagnosticsProperty('screenMode', screenMode))..add(DiagnosticsProperty('isDeleteAccountButtonLoading', isDeleteAccountButtonLoading))..add(DiagnosticsProperty('shouldShowDeleteAccountErrorBottomSheet', shouldShowDeleteAccountErrorBottomSheet))..add(DiagnosticsProperty('dismissDeleteConfirmationBottomSheet', dismissDeleteConfirmationBottomSheet))..add(DiagnosticsProperty('banksLimit', banksLimit))..add(DiagnosticsProperty('index', index))..add(DiagnosticsProperty('withdrawBankTransferProccessState', withdrawBankTransferProccessState));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferState&&const DeepCollectionEquality().equals(other.bankAccounts, bankAccounts)&&(identical(other.screenMode, screenMode) || other.screenMode == screenMode)&&(identical(other.isDeleteAccountButtonLoading, isDeleteAccountButtonLoading) || other.isDeleteAccountButtonLoading == isDeleteAccountButtonLoading)&&(identical(other.shouldShowDeleteAccountErrorBottomSheet, shouldShowDeleteAccountErrorBottomSheet) || other.shouldShowDeleteAccountErrorBottomSheet == shouldShowDeleteAccountErrorBottomSheet)&&(identical(other.dismissDeleteConfirmationBottomSheet, dismissDeleteConfirmationBottomSheet) || other.dismissDeleteConfirmationBottomSheet == dismissDeleteConfirmationBottomSheet)&&(identical(other.banksLimit, banksLimit) || other.banksLimit == banksLimit)&&(identical(other.index, index) || other.index == index)&&(identical(other.withdrawBankTransferProccessState, withdrawBankTransferProccessState) || other.withdrawBankTransferProccessState == withdrawBankTransferProccessState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(bankAccounts),screenMode,isDeleteAccountButtonLoading,shouldShowDeleteAccountErrorBottomSheet,dismissDeleteConfirmationBottomSheet,banksLimit,index,withdrawBankTransferProccessState);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferState(bankAccounts: $bankAccounts, screenMode: $screenMode, isDeleteAccountButtonLoading: $isDeleteAccountButtonLoading, shouldShowDeleteAccountErrorBottomSheet: $shouldShowDeleteAccountErrorBottomSheet, dismissDeleteConfirmationBottomSheet: $dismissDeleteConfirmationBottomSheet, banksLimit: $banksLimit, index: $index, withdrawBankTransferProccessState: $withdrawBankTransferProccessState)';
}


}

/// @nodoc
abstract mixin class $WithdrawBankTransferStateCopyWith<$Res>  {
  factory $WithdrawBankTransferStateCopyWith(WithdrawBankTransferState value, $Res Function(WithdrawBankTransferState) _then) = _$WithdrawBankTransferStateCopyWithImpl;
@useResult
$Res call({
 List<BankAccountData> bankAccounts, ScreenMode screenMode, bool isDeleteAccountButtonLoading, bool shouldShowDeleteAccountErrorBottomSheet, bool dismissDeleteConfirmationBottomSheet, int? banksLimit, int? index, WithdrawBankTransferProccessState withdrawBankTransferProccessState
});


$WithdrawBankTransferProccessStateCopyWith<$Res> get withdrawBankTransferProccessState;

}
/// @nodoc
class _$WithdrawBankTransferStateCopyWithImpl<$Res>
    implements $WithdrawBankTransferStateCopyWith<$Res> {
  _$WithdrawBankTransferStateCopyWithImpl(this._self, this._then);

  final WithdrawBankTransferState _self;
  final $Res Function(WithdrawBankTransferState) _then;

/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? bankAccounts = null,Object? screenMode = null,Object? isDeleteAccountButtonLoading = null,Object? shouldShowDeleteAccountErrorBottomSheet = null,Object? dismissDeleteConfirmationBottomSheet = null,Object? banksLimit = freezed,Object? index = freezed,Object? withdrawBankTransferProccessState = null,}) {
  return _then(_self.copyWith(
bankAccounts: null == bankAccounts ? _self.bankAccounts : bankAccounts // ignore: cast_nullable_to_non_nullable
as List<BankAccountData>,screenMode: null == screenMode ? _self.screenMode : screenMode // ignore: cast_nullable_to_non_nullable
as ScreenMode,isDeleteAccountButtonLoading: null == isDeleteAccountButtonLoading ? _self.isDeleteAccountButtonLoading : isDeleteAccountButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,shouldShowDeleteAccountErrorBottomSheet: null == shouldShowDeleteAccountErrorBottomSheet ? _self.shouldShowDeleteAccountErrorBottomSheet : shouldShowDeleteAccountErrorBottomSheet // ignore: cast_nullable_to_non_nullable
as bool,dismissDeleteConfirmationBottomSheet: null == dismissDeleteConfirmationBottomSheet ? _self.dismissDeleteConfirmationBottomSheet : dismissDeleteConfirmationBottomSheet // ignore: cast_nullable_to_non_nullable
as bool,banksLimit: freezed == banksLimit ? _self.banksLimit : banksLimit // ignore: cast_nullable_to_non_nullable
as int?,index: freezed == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int?,withdrawBankTransferProccessState: null == withdrawBankTransferProccessState ? _self.withdrawBankTransferProccessState : withdrawBankTransferProccessState // ignore: cast_nullable_to_non_nullable
as WithdrawBankTransferProccessState,
  ));
}
/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawBankTransferProccessStateCopyWith<$Res> get withdrawBankTransferProccessState {
  
  return $WithdrawBankTransferProccessStateCopyWith<$Res>(_self.withdrawBankTransferProccessState, (value) {
    return _then(_self.copyWith(withdrawBankTransferProccessState: value));
  });
}
}


/// @nodoc


class _WithdrawBankTransferState with DiagnosticableTreeMixin implements WithdrawBankTransferState {
  const _WithdrawBankTransferState({final  List<BankAccountData> bankAccounts = const [], this.screenMode = ScreenMode.view, this.isDeleteAccountButtonLoading = false, this.shouldShowDeleteAccountErrorBottomSheet = false, this.dismissDeleteConfirmationBottomSheet = false, this.banksLimit, this.index, this.withdrawBankTransferProccessState = const WithdrawBankTransferProccessState.loading()}): _bankAccounts = bankAccounts;
  

 final  List<BankAccountData> _bankAccounts;
@override@JsonKey() List<BankAccountData> get bankAccounts {
  if (_bankAccounts is EqualUnmodifiableListView) return _bankAccounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_bankAccounts);
}

@override@JsonKey() final  ScreenMode screenMode;
@override@JsonKey() final  bool isDeleteAccountButtonLoading;
@override@JsonKey() final  bool shouldShowDeleteAccountErrorBottomSheet;
@override@JsonKey() final  bool dismissDeleteConfirmationBottomSheet;
// Fixed typo
@override final  int? banksLimit;
@override final  int? index;
@override@JsonKey() final  WithdrawBankTransferProccessState withdrawBankTransferProccessState;

/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawBankTransferStateCopyWith<_WithdrawBankTransferState> get copyWith => __$WithdrawBankTransferStateCopyWithImpl<_WithdrawBankTransferState>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferState'))
    ..add(DiagnosticsProperty('bankAccounts', bankAccounts))..add(DiagnosticsProperty('screenMode', screenMode))..add(DiagnosticsProperty('isDeleteAccountButtonLoading', isDeleteAccountButtonLoading))..add(DiagnosticsProperty('shouldShowDeleteAccountErrorBottomSheet', shouldShowDeleteAccountErrorBottomSheet))..add(DiagnosticsProperty('dismissDeleteConfirmationBottomSheet', dismissDeleteConfirmationBottomSheet))..add(DiagnosticsProperty('banksLimit', banksLimit))..add(DiagnosticsProperty('index', index))..add(DiagnosticsProperty('withdrawBankTransferProccessState', withdrawBankTransferProccessState));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawBankTransferState&&const DeepCollectionEquality().equals(other._bankAccounts, _bankAccounts)&&(identical(other.screenMode, screenMode) || other.screenMode == screenMode)&&(identical(other.isDeleteAccountButtonLoading, isDeleteAccountButtonLoading) || other.isDeleteAccountButtonLoading == isDeleteAccountButtonLoading)&&(identical(other.shouldShowDeleteAccountErrorBottomSheet, shouldShowDeleteAccountErrorBottomSheet) || other.shouldShowDeleteAccountErrorBottomSheet == shouldShowDeleteAccountErrorBottomSheet)&&(identical(other.dismissDeleteConfirmationBottomSheet, dismissDeleteConfirmationBottomSheet) || other.dismissDeleteConfirmationBottomSheet == dismissDeleteConfirmationBottomSheet)&&(identical(other.banksLimit, banksLimit) || other.banksLimit == banksLimit)&&(identical(other.index, index) || other.index == index)&&(identical(other.withdrawBankTransferProccessState, withdrawBankTransferProccessState) || other.withdrawBankTransferProccessState == withdrawBankTransferProccessState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_bankAccounts),screenMode,isDeleteAccountButtonLoading,shouldShowDeleteAccountErrorBottomSheet,dismissDeleteConfirmationBottomSheet,banksLimit,index,withdrawBankTransferProccessState);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferState(bankAccounts: $bankAccounts, screenMode: $screenMode, isDeleteAccountButtonLoading: $isDeleteAccountButtonLoading, shouldShowDeleteAccountErrorBottomSheet: $shouldShowDeleteAccountErrorBottomSheet, dismissDeleteConfirmationBottomSheet: $dismissDeleteConfirmationBottomSheet, banksLimit: $banksLimit, index: $index, withdrawBankTransferProccessState: $withdrawBankTransferProccessState)';
}


}

/// @nodoc
abstract mixin class _$WithdrawBankTransferStateCopyWith<$Res> implements $WithdrawBankTransferStateCopyWith<$Res> {
  factory _$WithdrawBankTransferStateCopyWith(_WithdrawBankTransferState value, $Res Function(_WithdrawBankTransferState) _then) = __$WithdrawBankTransferStateCopyWithImpl;
@override @useResult
$Res call({
 List<BankAccountData> bankAccounts, ScreenMode screenMode, bool isDeleteAccountButtonLoading, bool shouldShowDeleteAccountErrorBottomSheet, bool dismissDeleteConfirmationBottomSheet, int? banksLimit, int? index, WithdrawBankTransferProccessState withdrawBankTransferProccessState
});


@override $WithdrawBankTransferProccessStateCopyWith<$Res> get withdrawBankTransferProccessState;

}
/// @nodoc
class __$WithdrawBankTransferStateCopyWithImpl<$Res>
    implements _$WithdrawBankTransferStateCopyWith<$Res> {
  __$WithdrawBankTransferStateCopyWithImpl(this._self, this._then);

  final _WithdrawBankTransferState _self;
  final $Res Function(_WithdrawBankTransferState) _then;

/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? bankAccounts = null,Object? screenMode = null,Object? isDeleteAccountButtonLoading = null,Object? shouldShowDeleteAccountErrorBottomSheet = null,Object? dismissDeleteConfirmationBottomSheet = null,Object? banksLimit = freezed,Object? index = freezed,Object? withdrawBankTransferProccessState = null,}) {
  return _then(_WithdrawBankTransferState(
bankAccounts: null == bankAccounts ? _self._bankAccounts : bankAccounts // ignore: cast_nullable_to_non_nullable
as List<BankAccountData>,screenMode: null == screenMode ? _self.screenMode : screenMode // ignore: cast_nullable_to_non_nullable
as ScreenMode,isDeleteAccountButtonLoading: null == isDeleteAccountButtonLoading ? _self.isDeleteAccountButtonLoading : isDeleteAccountButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,shouldShowDeleteAccountErrorBottomSheet: null == shouldShowDeleteAccountErrorBottomSheet ? _self.shouldShowDeleteAccountErrorBottomSheet : shouldShowDeleteAccountErrorBottomSheet // ignore: cast_nullable_to_non_nullable
as bool,dismissDeleteConfirmationBottomSheet: null == dismissDeleteConfirmationBottomSheet ? _self.dismissDeleteConfirmationBottomSheet : dismissDeleteConfirmationBottomSheet // ignore: cast_nullable_to_non_nullable
as bool,banksLimit: freezed == banksLimit ? _self.banksLimit : banksLimit // ignore: cast_nullable_to_non_nullable
as int?,index: freezed == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int?,withdrawBankTransferProccessState: null == withdrawBankTransferProccessState ? _self.withdrawBankTransferProccessState : withdrawBankTransferProccessState // ignore: cast_nullable_to_non_nullable
as WithdrawBankTransferProccessState,
  ));
}

/// Create a copy of WithdrawBankTransferState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawBankTransferProccessStateCopyWith<$Res> get withdrawBankTransferProccessState {
  
  return $WithdrawBankTransferProccessStateCopyWith<$Res>(_self.withdrawBankTransferProccessState, (value) {
    return _then(_self.copyWith(withdrawBankTransferProccessState: value));
  });
}
}

/// @nodoc
mixin _$WithdrawBankTransferProccessState implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferProccessState'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferProccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferProccessState()';
}


}

/// @nodoc
class $WithdrawBankTransferProccessStateCopyWith<$Res>  {
$WithdrawBankTransferProccessStateCopyWith(WithdrawBankTransferProccessState _, $Res Function(WithdrawBankTransferProccessState) __);
}


/// @nodoc


class WithdrawBankTransferProccessStateLoading with DiagnosticableTreeMixin implements WithdrawBankTransferProccessState {
  const WithdrawBankTransferProccessStateLoading();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferProccessState.loading'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferProccessStateLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferProccessState.loading()';
}


}




/// @nodoc


class WithdrawBankTransferProccessStateSuccess with DiagnosticableTreeMixin implements WithdrawBankTransferProccessState {
  const WithdrawBankTransferProccessStateSuccess();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferProccessState.success'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferProccessStateSuccess);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferProccessState.success()';
}


}




/// @nodoc


class WithdrawBankTransferProccessStateSuccessEmpty with DiagnosticableTreeMixin implements WithdrawBankTransferProccessState {
  const WithdrawBankTransferProccessStateSuccessEmpty();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferProccessState.successEmpty'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferProccessStateSuccessEmpty);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferProccessState.successEmpty()';
}


}




/// @nodoc


class WithdrawBankTransferProccessStateError with DiagnosticableTreeMixin implements WithdrawBankTransferProccessState {
  const WithdrawBankTransferProccessStateError();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'WithdrawBankTransferProccessState.error'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawBankTransferProccessStateError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'WithdrawBankTransferProccessState.error()';
}


}




// dart format on
