import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:payment/src/domain/model/screen_mode.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/add_bank_account_button_disabled.dart';
import 'package:payment/src/presentation/withdraw_bank_transfer/widgets/manage_account_bottom_sheet.dart';

class AddBankAccountButton extends StatelessWidget {
  const AddBankAccountButton({
    super.key,
    required this.bankTransferAmountModel,
  });
  final WithdrawFlowParams bankTransferAmountModel;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WithdrawBankTransferBloc, WithdrawBankTransferState>(
      buildWhen:
          (previous, current) => previous.screenMode != current.screenMode,
      builder: (builderContext, state) {
        return state.screenMode == ScreenMode.modify
            ? AddBankAccountButtonDisabled()
            : SizedBox(
              width: MediaQuery.sizeOf(context).width,
              child: DuploButton.secondary(
                leadingIcon: assets.Assets.images.addIc.keyName,
                title:
                    EquitiLocalization.of(
                      context,
                    ).payments_add_new_bank_account,
                onTap: () {
                  if (state.bankAccounts.length >= (state.banksLimit ?? 0)) {
                    DuploSheet.showModalSheet<Widget>(
                      context: context,
                      hasTopBarLayer: false,
                      hideCloseButton: true,
                      content: (contentContext) {
                        return BlocProvider<WithdrawBankTransferBloc>.value(
                          value:
                              builderContext.read<WithdrawBankTransferBloc>(),
                          child: const ManageAccountBottomSheet(),
                        );
                      },
                      title: '',
                    );
                  } else {
                    final bloc =
                        builderContext.read<WithdrawBankTransferBloc>();
                    bloc.add(
                      WithdrawBankTransferEvent.onAddNewAccountPressed(
                        bankTransferAmountModel,
                      ),
                    );
                  }
                },
              ),
            );
      },
    );
  }
}
