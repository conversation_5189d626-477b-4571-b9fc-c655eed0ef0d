import 'get_change_leverage_test_data.dart';
import 'scenarios/change_leverage_success_scenario.dart';
import 'scenarios/change_leverage_failure_scenario.dart';
import 'package:equiti_test/equiti_test.dart';


Feature: Change Leverage
  As a user
  I want to be able to change my trading leverage
  So that I can adjust my risk exposure

  Scenario: User sees a list of available leverages
    Given The {GetChangeLeverageTestData()} app runs with configuration {scenarios:[changeLeverageSuccessScenario]}
    Then i see {"1:1"} text

  @testMethodName: testGoldens
  Scenario: Screenshot changeLeverage screen success
    Given The {GetChangeLeverageTestData()} app is rendered {scenarios:[changeLeverageSuccessScenario]}
    Then screenshot verified {'change_leverage_success'}

  @testMethodName: testGoldens
  Scenario: Screenshot changeLeverage screen failure
    Given The {GetChangeLeverageTestData()} app is rendered {scenarios:[changeLeverageFailureScenario]}
    Then screenshot verified {'change_leverage_failure'}

  @testMethodName: testGoldens
  Scenario: User select on of the leverages
    Given The {GetChangeLeverageTestData()} app runs with configuration {scenarios:[changeLeverageSuccessScenario]}
    Then i tap {"1:1"} text
    Then screenshot verified {'leverage_selected'}

  @testMethodName: testGoldens
  Scenario: User select on of the leverages and press on Save to pop from screen
    Given The {GetChangeLeverageTestData()} app runs with configuration {scenarios:[changeLeverageSuccessScenario]}
    Then i tap {"1:1"} text
    Then i tap {"text_selection_component_button"} identifier
    Then screenshot verified {'leverage_saved'}

