// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_flow_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WithdrawFlowParams {

 String get tradingAccountId; WithdrawalMop get paymentType; String get accountCurrency; String get currency; String get conversionRateString; num get amount; num get convertedAmount; num get conversionRate; num get accountBalance;
/// Create a copy of WithdrawFlowParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawFlowParamsCopyWith<WithdrawFlowParams> get copyWith => _$WithdrawFlowParamsCopyWithImpl<WithdrawFlowParams>(this as WithdrawFlowParams, _$identity);

  /// Serializes this WithdrawFlowParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawFlowParams&&(identical(other.tradingAccountId, tradingAccountId) || other.tradingAccountId == tradingAccountId)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate)&&(identical(other.accountBalance, accountBalance) || other.accountBalance == accountBalance));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tradingAccountId,paymentType,accountCurrency,currency,conversionRateString,amount,convertedAmount,conversionRate,accountBalance);

@override
String toString() {
  return 'WithdrawFlowParams(tradingAccountId: $tradingAccountId, paymentType: $paymentType, accountCurrency: $accountCurrency, currency: $currency, conversionRateString: $conversionRateString, amount: $amount, convertedAmount: $convertedAmount, conversionRate: $conversionRate, accountBalance: $accountBalance)';
}


}

/// @nodoc
abstract mixin class $WithdrawFlowParamsCopyWith<$Res>  {
  factory $WithdrawFlowParamsCopyWith(WithdrawFlowParams value, $Res Function(WithdrawFlowParams) _then) = _$WithdrawFlowParamsCopyWithImpl;
@useResult
$Res call({
 String tradingAccountId, WithdrawalMop paymentType, String accountCurrency, String currency, String conversionRateString, num amount, num convertedAmount, num conversionRate, num accountBalance
});




}
/// @nodoc
class _$WithdrawFlowParamsCopyWithImpl<$Res>
    implements $WithdrawFlowParamsCopyWith<$Res> {
  _$WithdrawFlowParamsCopyWithImpl(this._self, this._then);

  final WithdrawFlowParams _self;
  final $Res Function(WithdrawFlowParams) _then;

/// Create a copy of WithdrawFlowParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tradingAccountId = null,Object? paymentType = null,Object? accountCurrency = null,Object? currency = null,Object? conversionRateString = null,Object? amount = null,Object? convertedAmount = null,Object? conversionRate = null,Object? accountBalance = null,}) {
  return _then(_self.copyWith(
tradingAccountId: null == tradingAccountId ? _self.tradingAccountId : tradingAccountId // ignore: cast_nullable_to_non_nullable
as String,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as WithdrawalMop,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as num,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as num,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as num,accountBalance: null == accountBalance ? _self.accountBalance : accountBalance // ignore: cast_nullable_to_non_nullable
as num,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _WithdrawFlowParams implements WithdrawFlowParams {
  const _WithdrawFlowParams({required this.tradingAccountId, required this.paymentType, required this.accountCurrency, required this.currency, required this.conversionRateString, required this.amount, required this.convertedAmount, required this.conversionRate, required this.accountBalance});
  factory _WithdrawFlowParams.fromJson(Map<String, dynamic> json) => _$WithdrawFlowParamsFromJson(json);

@override final  String tradingAccountId;
@override final  WithdrawalMop paymentType;
@override final  String accountCurrency;
@override final  String currency;
@override final  String conversionRateString;
@override final  num amount;
@override final  num convertedAmount;
@override final  num conversionRate;
@override final  num accountBalance;

/// Create a copy of WithdrawFlowParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawFlowParamsCopyWith<_WithdrawFlowParams> get copyWith => __$WithdrawFlowParamsCopyWithImpl<_WithdrawFlowParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WithdrawFlowParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawFlowParams&&(identical(other.tradingAccountId, tradingAccountId) || other.tradingAccountId == tradingAccountId)&&(identical(other.paymentType, paymentType) || other.paymentType == paymentType)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.convertedAmount, convertedAmount) || other.convertedAmount == convertedAmount)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate)&&(identical(other.accountBalance, accountBalance) || other.accountBalance == accountBalance));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tradingAccountId,paymentType,accountCurrency,currency,conversionRateString,amount,convertedAmount,conversionRate,accountBalance);

@override
String toString() {
  return 'WithdrawFlowParams(tradingAccountId: $tradingAccountId, paymentType: $paymentType, accountCurrency: $accountCurrency, currency: $currency, conversionRateString: $conversionRateString, amount: $amount, convertedAmount: $convertedAmount, conversionRate: $conversionRate, accountBalance: $accountBalance)';
}


}

/// @nodoc
abstract mixin class _$WithdrawFlowParamsCopyWith<$Res> implements $WithdrawFlowParamsCopyWith<$Res> {
  factory _$WithdrawFlowParamsCopyWith(_WithdrawFlowParams value, $Res Function(_WithdrawFlowParams) _then) = __$WithdrawFlowParamsCopyWithImpl;
@override @useResult
$Res call({
 String tradingAccountId, WithdrawalMop paymentType, String accountCurrency, String currency, String conversionRateString, num amount, num convertedAmount, num conversionRate, num accountBalance
});




}
/// @nodoc
class __$WithdrawFlowParamsCopyWithImpl<$Res>
    implements _$WithdrawFlowParamsCopyWith<$Res> {
  __$WithdrawFlowParamsCopyWithImpl(this._self, this._then);

  final _WithdrawFlowParams _self;
  final $Res Function(_WithdrawFlowParams) _then;

/// Create a copy of WithdrawFlowParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tradingAccountId = null,Object? paymentType = null,Object? accountCurrency = null,Object? currency = null,Object? conversionRateString = null,Object? amount = null,Object? convertedAmount = null,Object? conversionRate = null,Object? accountBalance = null,}) {
  return _then(_WithdrawFlowParams(
tradingAccountId: null == tradingAccountId ? _self.tradingAccountId : tradingAccountId // ignore: cast_nullable_to_non_nullable
as String,paymentType: null == paymentType ? _self.paymentType : paymentType // ignore: cast_nullable_to_non_nullable
as WithdrawalMop,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as num,convertedAmount: null == convertedAmount ? _self.convertedAmount : convertedAmount // ignore: cast_nullable_to_non_nullable
as num,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as num,accountBalance: null == accountBalance ? _self.accountBalance : accountBalance // ignore: cast_nullable_to_non_nullable
as num,
  ));
}


}

// dart format on
