name: onboarding
description: "A new Flutter package project."
version: 0.0.1
publish_to: none

environment:
  sdk: 3.8.1
  flutter: 3.32.6

dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: 9.1.1
  freezed_annotation: 3.0.0
  # flutter_localizations:
  #   sdk: flutter
  # intl: 0.19.0
  api_client:
    path: ../../utilities/api_client
  duplo:
    path: ../../core/duplo
  login:
    path: ../../core/login
  validator:
    path: ../../core/validator
  domain:
    path: ../../core/domain
  flutter_svg: 2.0.10+1
  get_it: 8.0.3
  injectable: 2.5.0
  dio: 5.8.0+1
  url_launcher: 6.3.1
  preferences:
    path: ../../utilities/preferences
  prelude:
    path: ../../utilities/prelude
  equiti_auth:
    path: ../../core/equiti_auth
  equiti_router:
    path: ../../utilities/equiti_router
  user_verification:
    path: ../../utilities/user_verification
  monitoring:
    path: ../../utilities/monitoring
  leancode_forms: 0.1.2
  flutter_inappwebview: 6.1.5
  user_account:
    path: ../../core/user_account
  broker_settings:
    path: ../../core/broker_settings  

  theme_manager:
    path: ../../utilities/theme_manager
  flutter_libphonenumber: 2.5.1

  equiti_localization:
    path: ../equiti_localization

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: 2.5.4
  dependency_validator: 5.0.2
  equiti_lint:
    path: ../../utilities/equiti_lint
  bdd_steps:
    path: ../../utilities/bdd_steps
  equiti_test:
    path: ../../utilities/equiti_test
  bdd_widget_test: 1.8.1
  flutter_gen_runner: 5.10.0
  freezed: 3.0.6
  injectable_generator: 2.7.0
  json_serializable: 6.9.5
  dart_code_metrics_presets: 2.22.0
  mocktail: 1.0.4
  build_verify: 3.1.1

dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ^3.0.1

flutter_gen:
  output: lib/src/assets/
  assets:
    exclude:
      - resources/mocks/**/*
    outputs:
      package_parameter_enabled: true
  integrations:
    flutter_svg: true

flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/images/
    - assets/videos/
    - assets/lotties/
    - resources/mocks/signup/email/
    - resources/mocks/signup/signup/
    - assets/images/
    - resources/mocks/verify_otp/
    - resources/mocks/verify_mobile_number/
    - resources/mocks/progress_tracker/
    - resources/mocks/get_countries/
    - resources/mocks/broker_settings/
