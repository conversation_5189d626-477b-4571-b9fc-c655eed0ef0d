// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/trading_tab.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/closed_trade_details.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/widgets/trading_filter_bottom_sheet.dart';
import 'scenarios/trading_empty_scenario.dart';
import 'scenarios/trading_success_scenario.dart';
import 'scenarios/trade_details_success_scenario.dart';
import 'package:e_trader/src/data/api/trading_response_model.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import './step/the_app_runs_with_configuration.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Trading Tab''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: User views Trading Tab (scenarios: [tradingSuccessScenario], 'trading_success_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Trading Tab (scenarios: [tradingSuccessScenario], 'trading_success_scenario')''',
          );
          await theAppIsRendered(
            tester,
            TradingTab(),
            scenarios: [tradingSuccessScenario],
          );
          await screenshotVerifiedWithCustomPump(
            tester,
            'trading_success_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Trading Tab (scenarios: [tradingSuccessScenario], 'trading_success_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views Trading Tab (scenarios: [tradingEmptyScenario], 'trading_empty_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Trading Tab (scenarios: [tradingEmptyScenario], 'trading_empty_scenario')''',
          );
          await theAppIsRendered(
            tester,
            TradingTab(),
            scenarios: [tradingEmptyScenario],
          );
          await screenshotVerifiedWithCustomPump(
            tester,
            'trading_empty_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Trading Tab (scenarios: [tradingEmptyScenario], 'trading_empty_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens('''User select sort Trading''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select sort Trading''');
        await theAppRunsWithConfiguration(
          tester,
          TradingTab(),
          scenarios: [tradingSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_sort");
        await screenshotVerifiedWithCustomPump(tester, 'trading_sort_scenario');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select sort Trading''', success);
      }
    });
    testGoldens('''User select filter Trading''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User select filter Trading''');
        await theAppIsRendered(
          tester,
          TradingTab(),
          scenarios: [tradingSuccessScenario],
        );
        await iWait(tester);
        await iTapIdentifier(tester, "grouped_buttons_filter");
        await screenshotVerifiedWithCustomPump(
          tester,
          'trading_filter_scenario',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User select filter Trading''', success);
      }
    });
    testGoldens(
      '''Outline: User views Trade Details (scenarios: [tradeDetailsSuccessScenario], 'trade_details_success_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views Trade Details (scenarios: [tradeDetailsSuccessScenario], 'trade_details_success_scenario')''',
          );
          await theAppIsRendered(
            tester,
            ClosedTradeDetails(
              tradingItem: TradingListModel(
                activityType: "CloseTrade",
                activityTypeName: "Close profit",
                dateTime: DateTime.tryParse(""),
                id: 284979,
                tradeDetail: TradeDetailModel(
                  tradeType: "Buy",
                  symbol: "EURUSD",
                  profit: 0,
                  entry: "In",
                  volume: 1000,
                  openPrice: 4125.87,
                  positionId: 465304,
                  productName: "Euro vs US Dollar",
                  friendlyName: "EURUSD",
                  logoUrl:
                      "https://eqdulcimer03z.blob.core.windows.net/public/logos%2Fa52a70e2-2917-442d-96c6-b7ffcfff7722_EURUSD.png",
                  positionOpenPrice: 0,
                  commission: 0,
                ),
              ),
            ),
            scenarios: [tradeDetailsSuccessScenario],
          );
          await iWait(tester);
          await screenshotVerified(tester, 'trade_details_success_scenario');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views Trade Details (scenarios: [tradeDetailsSuccessScenario], 'trade_details_success_scenario')''',
            success,
          );
        }
      },
    );
  });
}
