import 'dart:convert';

import 'package:duplo/duplo.dart';
import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:socket_client/socket_client.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent priceAlert() {
  return DisplayableComponent(
    title: "Price Alerts",
    children: [
      DisplayableComponent(
        title: 'Set Price Alert',
        children: [
          DisplayableComponent(
            title: 'Success',
            onTap: () {
              mockProductHub();
              mockActiveAlertsHub();
              mockCreateNewAlert();

              return prepareWidget(
                PriceAlertWidget(
                  symbol: "AUDCAD",
                  symbolImageUrl:
                      "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
                ),
                true,
                "Success",
              );
            },
          ),
          DisplayableComponent(
            title: 'Fail to Load',
            onTap: () {
              return prepareWidget(
                PriceAlertWidget(
                  symbol: "AUDCAD",
                  symbolImageUrl:
                      "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
                ),
                true,
                "Fail to Load",
              );
            },
          ),
          DisplayableComponent(
            title: 'Fail to Create New',
            onTap: () {
              mockProductHub();
              mockActiveAlertsHub();
              return prepareWidget(
                PriceAlertWidget(
                  symbol: "AUDCAD",
                  symbolImageUrl:
                      "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
                ),
                true,
                "Fail to Create New",
              );
            },
          ),
        ],
      ),
      DisplayableComponent(
        title: "Modify Alert",
        children: [
          DisplayableComponent(
            title: 'Success',
            onTap: () {
              mockProductHub();

              final alert = alertModel();
              final modifyWidget = ModifyPriceAlertWidget(alert: alert);
              return prepareWidget(modifyWidget, true, "Fail to Load");
            },
          ),
          DisplayableComponent(
            title: 'Failed to load',
            onTap: () {
              final alert = alertModel();
              final modifyWidget = ModifyPriceAlertWidget(alert: alert);
              return prepareWidget(modifyWidget, true, "Fail to Load");
            },
          ),
          DisplayableComponent(
            title: 'Failed to Modify',
            onTap: () {
              return Container();
            },
          ),
        ],
      ),
      DisplayableComponent(
        title: "Active Alerts",
        children: [
          DisplayableComponent(
            title: 'Without Header - Success',
            onTap: () {
              mockActiveAlertsHub();
              return prepareWidget(
                ActivePriceAlertsWidget(symbol: "AUDCAD"),
                true,
                "Without Header - Success",
              );
            },
          ),
          DisplayableComponent(
            title: 'Without Header - Failed',
            onTap: () {
              return prepareWidget(
                ActivePriceAlertsWidget(symbol: "AUDCAD"),
                true,
                "Without Header - Failed",
              );
            },
          ),
          DisplayableComponent(
            title: 'Without Header - Empty',
            onTap: () {
              mockEmptyActiveAlertsHub();
              return prepareWidget(
                ActivePriceAlertsWidget(symbol: "AUDCAD"),
                true,
                "Without Header - Failed",
              );
            },
          ),
          DisplayableComponent(
            title: 'With Header - Succcess',
            onTap: () {
              mockActiveAlertsHub();
              return prepareWidget(
                ActivePriceAlertsWidget(
                  symbol: "AUDCAD",

                  showEditableOptions: true,
                ),
                false,
                "With Header - Succcess",
              );
            },
          ),
          DisplayableComponent(
            title: 'With Header - Succcess All Alerts',
            onTap: () {
              mockActiveAlertsHub();
              return prepareWidget(
                ActivePriceAlertsWidget(showEditableOptions: true),
                false,
                "With Header - Succcess",
              );
            },
          ),
          DisplayableComponent(
            title: 'With Header - Failed',
            onTap: () {
              return prepareWidget(
                ActivePriceAlertsWidget(
                  symbol: "AUDCAD",

                  showEditableOptions: true,
                ),
                false,
                "With Header - Failed",
              );
            },
          ),
          DisplayableComponent(
            title: 'Without Header - Empty',
            onTap: () {
              mockEmptyActiveAlertsHub();
              return prepareWidget(
                ActivePriceAlertsWidget(symbol: "AUDCAD"),
                true,
                "Without Header - Failed",
              );
            },
          ),
        ],
      ),
    ],
  );
}

void mockProductHub() {
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
    eventType: TradingSocketEvent.quotes.subscribe,
    responses: [
      {
        "symbol": "AUDCAD",
        "ask": 100,
        "bid": 200,
        "date": "2024-11-22T05:15:03",
        "digits": 5,
        "dailyRateChange": 0,
        "direction": "Up",
      },
    ],
  );
}

void mockEmptyActiveAlertsHub() {
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/priceAlertHub",
    eventType: TradingSocketEvent.priceAlert.subscribe,
    responses: [],
  );
}

void mockActiveAlertsHub() {
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/priceAlertHub",
    eventType: TradingSocketEvent.priceAlert.subscribe,
    responses: [
      {
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c32a9311a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c33df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-8c2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "29f33-871e-4fc2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88223-871e-4fc2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-871e-4fc2-d-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "8822-871e-4fc2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-871e-4f8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
      {
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c32a93df11a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
    ],
  );
}

void mockCreateNewAlert() {
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/priceAlertHub",
    eventType: TradingSocketEvent.priceAlert.subscribe,
    responses: [
      {
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>",
      },
    ],
  );
}

PriceAlert alertModel() {
  final jsonString = """{
        "priceAlertId": "88229f33-871e-4fc2-8bdd-c32a93df611a",
        "accountNumber": ********,
        "symbolName": "AUDCAD",
        "friendlyName": "AUDCAD",
        "direction": 1,
        "currentPrice": 0.90112,
        "priceAlertPrice": 0.765969,
        "createdAt": "2025-02-14T17:17:30.0009698Z",
        "distance": 0.135151,
        "productCategory": "Currencies",
        "productLogoUrl":
            "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
        "productCategoryId": "4e4e1da3-75ec-40f1-8f71-dc2bb1de0178",
        "minLot": 0.01,
        "maxLot": 50,
        "digits": 5,
        "priceDirection": 1,
        "connectionId": "5nCi4wYfCl9B62x9_O3ueQ",
        "messageType": 2,
        "siloAddress": "************:11111@********",
        "email": "<EMAIL>"
      }""";

  final Map<String, dynamic> json =
      jsonDecode(jsonString) as Map<String, dynamic>;
  return PriceAlert.fromJson(json);
}

Widget prepareWidget(Widget widget, bool modal, String title) {
  if (modal) {
    return Scaffold(
      appBar: AppBar(automaticallyImplyLeading: true, title: Text(title)),
      body: Container(
        padding: EdgeInsets.all(16),
        child: PriceAlertPlayground(widget: widget),
      ),
    );
  }

  return Scaffold(
    appBar: AppBar(automaticallyImplyLeading: true, title: Text(title)),
    body: widget,
  );
}

class PriceAlertPlayground extends StatelessWidget {
  final Widget widget;
  const PriceAlertPlayground({Key? key, required this.widget})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: TextButton(
          child: Text("Click Here"),
          onPressed: () {
            DuploSheet.showNonScrollableModalSheet<void>(
              context: context,
              title: "Price Alert",
              content: (_) {
                return Builder(
                  builder: (_) {
                    return this.widget;
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}
