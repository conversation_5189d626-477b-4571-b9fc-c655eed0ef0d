// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'partial_close_trade_test_view.dart';
import 'scenarios/single_position_success_senario.dart';
import 'package:equiti_test/equiti_test.dart';
import 'scenarios/close_trade_failure_scenario.dart';
import 'scenarios/close_trade_success_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import './step/i_wait_for_seconds.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/i_tap_duplo_button.dart';
import 'package:bdd_widget_test/step/i_enter_into_input_field.dart';
import './step/is_displayed_in_the_amount_input_field.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Partial close Bottom Sheet''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''User views trade details bottom sheet''', (tester) async {
      var success = true;
      try {
        await beforeEach('''User views trade details bottom sheet''');
        await theAppIsRendered(
          tester,
          PartialCloseTradeTestView(),
          scenarios: [singlePositionSuccessSenario],
        );
        await iTapIdentifier(tester, 'open_partial_close_trade_sheet');
        await iWaitForSeconds(tester, 1);
        await screenshotVerifiedWithCustomPump(
          tester,
          'partial_close_bottom_sheet',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''User views trade details bottom sheet''', success);
      }
    });
    testGoldens('''Partially close a position failure''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Partially close a position failure''');
        await theAppIsRendered(
          tester,
          PartialCloseTradeTestView(),
          scenarios: [singlePositionSuccessSenario, closeTradefailureScenario],
        );
        await iTapIdentifier(tester, 'open_partial_close_trade_sheet');
        await iWait(tester);
        await iTapDuploButton(tester, "partial_close_button");
        await iWaitForSeconds(tester, 4);
        await screenshotVerifiedWithCustomPump(tester, 'partial_close_failure');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Partially close a position failure''', success);
      }
    });
    testGoldens(
      '''Outline: Input error when amount entered is outside valid range ('0', 'lot_too_low')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Input error when amount entered is outside valid range ('0', 'lot_too_low')''',
          );
          await theAppIsRendered(
            tester,
            PartialCloseTradeTestView(),
            scenarios: [singlePositionSuccessSenario],
          );
          await iTapIdentifier(tester, 'open_partial_close_trade_sheet');
          await iWaitForSeconds(tester, 1);
          await iEnterIntoInputField(tester, '0', 0);
          await isDisplayedInTheAmountInputField(tester, '0');
          await screenshotVerifiedWithCustomPump(tester, 'lot_too_low');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Input error when amount entered is outside valid range ('0', 'lot_too_low')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Input error when amount entered is outside valid range ('500', 'lot_too_high')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Input error when amount entered is outside valid range ('500', 'lot_too_high')''',
          );
          await theAppIsRendered(
            tester,
            PartialCloseTradeTestView(),
            scenarios: [singlePositionSuccessSenario],
          );
          await iTapIdentifier(tester, 'open_partial_close_trade_sheet');
          await iWaitForSeconds(tester, 1);
          await iEnterIntoInputField(tester, '500', 0);
          await isDisplayedInTheAmountInputField(tester, '500');
          await screenshotVerifiedWithCustomPump(tester, 'lot_too_high');
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Input error when amount entered is outside valid range ('500', 'lot_too_high')''',
            success,
          );
        }
      },
    );
  });
}
