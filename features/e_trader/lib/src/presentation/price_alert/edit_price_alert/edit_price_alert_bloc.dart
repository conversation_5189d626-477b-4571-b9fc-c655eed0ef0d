import 'dart:async';
import 'dart:math';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_price_alert_event.dart';
part 'edit_price_alert_state.dart';
part 'edit_price_alert_bloc.freezed.dart';

class EditPriceAlertBloc
    extends Bloc<EditPriceAlertEvent, EditPriceAlertState> {
  EditPriceAlertBloc() : super(const EditPriceAlertLoading()) {
    on<_PercentOptionChanged>(_onPercentOptionChanged);
    on<_InputFieldValueChanged>(_onInputFieldValueChanged);
    on<_SymbolPriceChanged>(_onSymbolInfoChanged);
    on<_TradeTypeChanged>(_onTradeTypeChanged);
    on<_SetInitialValues>(_onSetInitialValues);
  }

  EditedPriceAlertModel _validate(EditedPriceAlertModel model) {
    if (model.enteredPrice <= 0) {
      return model.copyWith(
        validationErrorMessage: "Price must be greater than 0",
      );
    }
    return model.copyWith(validationErrorMessage: null);
  }

  void _onPercentOptionChanged(
    _PercentOptionChanged event,
    Emitter<EditPriceAlertState> emit,
  ) {
    if (state case EditPriceAlertSuccess(info: final model)) {
      if (event.index == -1 ||
          model.selectedPercentOptionIndex == event.index) {
        return;
      }
      final percent =
          EditedPriceAlertModel.percentOptions.elementAtOrNull(event.index) ??
          0;
      final price = model.enteredPrice;
      final newVal = price + (price * percent / 100);
      final distance = (newVal - model.symbolPrice);

      final newModel = model.copyWith(
        enteredPrice: newVal,
        textFieldValue: null,
        selectedPercentOptionIndex: -1,
        distance: distance,
      );
      emit(EditPriceAlertState.success(_validate(newModel)));
    }
  }

  FutureOr<void> _onTradeTypeChanged(
    _TradeTypeChanged event,
    Emitter<EditPriceAlertState> emit,
  ) {
    if (state case EditPriceAlertSuccess(info: final model)) {
      final newDistance = model.enteredPrice - event.symbolPrice;
      final newModel = model.copyWith(
        symbolPrice: model.symbolPrice,
        distance: newDistance,
        selectedPercentOptionIndex: -1,
        textFieldValue: null,
      );

      emit(EditPriceAlertState.success(_validate(newModel)));
    }
  }

  FutureOr<void> _onInputFieldValueChanged(
    _InputFieldValueChanged event,
    Emitter<EditPriceAlertState> emit,
  ) {
    if (state case EditPriceAlertSuccess(info: final model)) {
      double value = 0;
      try {
        value = double.parse(event.value);
      } catch (e) {
        value = 0;
      }
      final newModel = model.copyWith(
        enteredPrice: value,
        selectedPercentOptionIndex: -1,
        distance: value - model.symbolPrice,
        textFieldValue: event.value,
      );
      emit(EditPriceAlertState.success(_validate(newModel)));
    }
  }

  FutureOr<void> _onSymbolInfoChanged(
    _SymbolPriceChanged event,
    Emitter<EditPriceAlertState> emit,
  ) {
    if (state case EditPriceAlertSuccess(info: final model)) {
      final newModel = model.copyWith(
        symbolPrice: event.symbolPrice,
        distance: model.enteredPrice - event.symbolPrice,
      );
      emit(EditPriceAlertState.success(newModel));
    }
  }

  FutureOr<void> _onSetInitialValues(
    _SetInitialValues event,
    Emitter<EditPriceAlertState> emit,
  ) {
    final changeFactor = _getPIP(event.digits);
    final newModel = EditedPriceAlertModel(
      digits: event.digits,
      symbolPrice: event.symbolPrice,
      tardeType: event.tradeType,
      selectedPercentOptionIndex: -1,
      enteredPrice: event.enteredPrice ?? event.symbolPrice,
      distance:
          event.enteredPrice != null
              ? (event.enteredPrice! - event.symbolPrice)
              : 0,
      changeFactor: changeFactor,
      validationErrorMessage: null,
      editing: false,
      textFieldValue: null,
    );
    emit(EditPriceAlertState.success(_validate(newModel)));
  }

  double _getPIP(int digits) {
    return pow(10, 1 - digits).toDouble();
  }
}
