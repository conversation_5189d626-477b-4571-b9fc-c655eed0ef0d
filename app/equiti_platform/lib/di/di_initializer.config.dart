// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:api_client/api_client.dart' as _i633;
import 'package:broker_settings/broker_settings.dart' as _i389;
import 'package:clock/clock.dart' as _i454;
import 'package:customer_support_chat/customer_support_chat.dart' as _i857;
import 'package:domain/domain.dart' as _i494;
import 'package:duplo/duplo.dart' as _i323;
import 'package:e_trader/fusion.dart' as _i665;
import 'package:equiti_auth/equiti_auth.dart' as _i313;
import 'package:equiti_platform/config/app_config.dart' as _i40;
import 'package:equiti_platform/deep_links/navigation/deep_link_navigation_registry.dart'
    as _i1060;
import 'package:equiti_platform/deep_links/navigation/delegates/hub_navigation_delegate.dart'
    as _i951;
import 'package:equiti_platform/di/api_module.dart' as _i26;
import 'package:equiti_platform/di/app_module.dart' as _i153;
import 'package:equiti_platform/di/deep_link_module.dart' as _i17;
import 'package:equiti_platform/di/navigation_module.dart' as _i959;
import 'package:equiti_router/equiti_router.dart' as _i955;
import 'package:equiti_secure_storage/equiti_secure_storage.dart' as _i704;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:hub/hub.dart' as _i511;
import 'package:injectable/injectable.dart' as _i526;
import 'package:locale_manager/locale_manager.dart' as _i385;
import 'package:login/login.dart' as _i944;
import 'package:monitoring/monitoring.dart' as _i472;
import 'package:network_logging/network_logging.dart' as _i0;
import 'package:onboarding/onboarding.dart' as _i706;
import 'package:payment/payments.dart' as _i702;
import 'package:preferences/preferences.dart' as _i695;
import 'package:prelude/prelude.dart' as _i813;
import 'package:socket_client/socket_client.dart' as _i688;
import 'package:theme_manager/theme_manager.dart' as _i811;
import 'package:user_account/user_account.dart' as _i43;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    await _i472.MonitoringPackageModule().init(gh);
    await _i0.NetworkLoggingPackageModule().init(gh);
    final navigationModule = _$NavigationModule();
    final deepLinkModule = _$DeepLinkModule();
    final apiModule = _$ApiModule();
    final appModule = _$AppModule();
    gh.singleton<_i511.HubLocation>(() => navigationModule.hubLocation());
    gh.singleton<_i944.LoginRouteLocation>(
      () => navigationModule.loginRouteLocation(),
    );
    gh.singleton<_i706.OnboardingRouteLocation>(
      () => navigationModule.onboardingRouteLocation(),
    );
    gh.singleton<_i702.PaymentRouteLocation>(
      () => navigationModule.paymentRouteLocation(),
    );
    gh.singleton<_i665.EquitiTraderLocation>(
      () => navigationModule.equitiTraderLocation(),
    );
    gh.singleton<_i813.PerformanceNavigatorObserver>(
      () => navigationModule.performanceNavigatorObserver(),
    );
    gh.lazySingleton<_i951.HubNavigationDelegate>(
      () => deepLinkModule.hubNavigationDelegate(),
    );
    gh.lazySingleton<_i633.CurlInterceptor>(() => apiModule.curlInterceptor());
    gh.lazySingleton<_i665.GetOfficeCodeUseCase>(
      () => appModule.getOfficeCodeUseCase,
    );
    gh.lazySingleton<_i454.Clock>(() => appModule.clock);
    await gh.lazySingletonAsync<_i695.EquitiPreferences>(
      () => appModule.sharedPreferences(),
      preResolve: true,
    );
    gh.lazySingleton<_i704.SecureStorage>(
      () => appModule.equitiSecureStorage(),
    );
    gh.lazySingleton<_i857.CustomerSupportChat>(
      () => appModule.customerSupportChat(),
    );
    gh.lazySingleton<_i944.LoginNavigation>(
      () => navigationModule.loginNavigation(),
    );
    gh.lazySingleton<_i511.HubNavigation>(
      () => navigationModule.hubNavigation(),
    );
    gh.singleton<String>(
      () => appModule.environment(gh<_i40.AppConfig>()),
      instanceName: 'environment',
    );
    gh.lazySingleton<_i955.EquitiNavigatorBase>(
      () => navigationModule.equitiPlatformNavigator(
        gh<_i511.HubLocation>(),
        gh<_i944.LoginRouteLocation>(),
        gh<_i665.EquitiTraderLocation>(),
        gh<_i706.OnboardingRouteLocation>(),
        gh<_i702.PaymentRouteLocation>(),
      ),
    );
    gh.singleton<String>(
      () => apiModule.socketBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'SocketBaseUrl',
    );
    gh.singleton<String>(
      () => apiModule.tradeBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'TradeBaseUrl',
    );
    gh.singleton<String>(
      () => apiModule.demoSocketBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'SocketBaseDemoUrl',
    );
    gh.lazySingleton<_i702.PaymentNavigation>(
      () => navigationModule.paymentNavigationImpl(),
    );
    gh.singleton<String>(
      () => apiModule.tradeBaseDemoUrl(gh<_i40.AppConfig>()),
      instanceName: 'TradeBaseDemoUrl',
    );
    gh.lazySingleton<_i1060.DeepLinkNavigationRegistry>(
      () => deepLinkModule.deepLinkNavigationRegistry(
        gh<_i951.HubNavigationDelegate>(),
      ),
    );
    gh.lazySingleton<_i665.EquitiTraderNavigation>(
      () => navigationModule.withdrawNavigation(),
    );
    gh.lazySingleton<_i706.OnboardingNavigation>(
      () => navigationModule.onboardingNavigationImpl(),
    );
    gh.lazySingleton<_i409.GlobalKey<_i409.NavigatorState>>(
      () => navigationModule.navigatorKey(gh<_i955.EquitiNavigatorBase>()),
    );
    gh.singleton<_i313.AuthService>(
      () => apiModule.authService(gh<_i472.LoggerBase>(), gh<_i40.AppConfig>()),
    );
    gh.singleton<_i385.LocaleManager>(
      () => appModule.localeModel(gh<_i695.EquitiPreferences>()),
    );
    gh.singleton<_i811.ThemeManager>(
      () => appModule.themeManager(gh<_i695.EquitiPreferences>()),
    );
    gh.factory<_i313.RefreshTokenRepository>(
      () => apiModule.refreshTokenRepository(gh<_i313.AuthService>()),
    );
    gh.lazySingleton<_i313.TokenManager>(
      () => apiModule.tokenManager(
        gh<_i704.SecureStorage>(),
        gh<_i695.EquitiPreferences>(),
        gh<_i313.RefreshTokenRepository>(),
      ),
    );
    gh.lazySingleton<_i313.AuthInterceptor>(
      () => apiModule.mobileBffAuthInterceptor(gh<_i313.TokenManager>()),
      instanceName: 'mobileBffAuthInterceptor',
    );
    gh.lazySingleton<_i633.DioBuilder>(
      () => apiModule.mobileBffDioBuilder(
        gh<_i472.PrettyDioLogger>(),
        gh<_i40.AppConfig>(),
        gh<_i313.AuthInterceptor>(instanceName: 'mobileBffAuthInterceptor'),
        gh<_i633.CurlInterceptor>(),
        gh<_i0.NetworkLoggerInterceptorBase>(),
      ),
      instanceName: 'mobileBffDioBuilder',
    );
    gh.lazySingleton<_i313.AuthInterceptor>(
      () => apiModule.authInterceptor(gh<_i313.TokenManager>()),
    );
    gh.singleton<_i688.TokenInterceptor>(
      () => apiModule.tokenInterceptor(gh<_i313.TokenManager>()),
    );
    gh.lazySingleton<_i633.DioBuilder>(
      () => apiModule.dioBuilder(
        gh<_i472.PrettyDioLogger>(),
        gh<String>(instanceName: 'TradeBaseUrl'),
        gh<_i313.AuthInterceptor>(),
        gh<_i633.CurlInterceptor>(),
        gh<_i0.NetworkLoggerInterceptorBase>(),
      ),
    );
    gh.lazySingleton<_i633.ApiClientBase>(
      () => apiModule.apiClient(gh<_i633.DioBuilder>()),
    );
    gh.lazySingleton<_i633.ApiClientBase>(
      () => apiModule.mobileBffApiClient(
        gh<_i633.DioBuilder>(instanceName: 'mobileBffDioBuilder'),
      ),
      instanceName: 'mobileBffApiClient',
    );
    gh.lazySingleton<_i688.SocketClient>(
      () => apiModule.socketClient(
        gh<_i472.LoggerBase>(),
        gh<String>(instanceName: 'SocketBaseUrl'),
        gh<_i688.TokenInterceptor>(),
      ),
    );
    await _i43.UserAccountPackageModule().init(gh);
    await _i389.BrokerSettingsPackageModule().init(gh);
    await _i944.LoginPackageModule().init(gh);
    await _i323.DuploPackageModule().init(gh);
    await _i706.OnboardingPackageModule().init(gh);
    await _i702.PaymentPackageModule().init(gh);
    await _i511.HubPackageModule().init(gh);
    await _i665.ETraderPackageModule().init(gh);
    await _i494.DomainPackageModule().init(gh);
    return this;
  }
}

class _$NavigationModule extends _i959.NavigationModule {}

class _$DeepLinkModule extends _i17.DeepLinkModule {}

class _$ApiModule extends _i26.ApiModule {}

class _$AppModule extends _i153.AppModule {}
