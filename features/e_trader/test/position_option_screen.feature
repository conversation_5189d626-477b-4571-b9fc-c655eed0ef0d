import 'package:equiti_test/equiti_test.dart';
import 'position_option_test_data.dart';
import 'package:e_trader/fusion.dart';
import 'scenarios/position_option_success_scenario.dart';
import 'scenarios/position_option_failure_scenario.dart';

Feature: PositionOption Golden Tests
@testMethodName: testGoldens
Scenario: User views position details bottom sheet
  Given The {PositionOptionTestData()} app is rendered {scenarios:[positionOptionSuccessScenario]}
  And I tap {'show_position_option_sheet'} identifier
  Then screenshot verified {'position_success_sheet'} with custom pump

@testMethodName: testGoldens
Scenario: User views position details bottom sheet with hub error
  Given The {PositionOptionTestData()} app is rendered {scenarios:[positionOptionFailureScenario]}
  And I tap {'show_position_option_sheet'} identifier
  Then screenshot verified {'position_success_sheet_with_error'} with custom pump


@testMethodName: testGoldens
Scenario: User clicks on one of the close trade types
  Given The {PositionOptionTestData()} app is rendered {scenarios:[positionOptionSuccessScenario]}
  And I tap {'show_position_option_sheet'} identifier
  And I wait
  And I tap {'all_buy_trades'} identifier
  Then screenshot verified {'position_close_trade_example'} with custom pump