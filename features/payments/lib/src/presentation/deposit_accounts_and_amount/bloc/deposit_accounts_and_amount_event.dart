part of 'deposit_accounts_and_amount_bloc.dart';

@freezed
sealed class DepositAccountsAndAmountEvent
    with _$DepositAccountsAndAmountEvent {
  const factory DepositAccountsAndAmountEvent.onAccountChange(Account account) =
      _OnAccountChange;

  // TODO (Aakash): Check if this event is required or not
  const factory DepositAccountsAndAmountEvent.naviagteToSelectedPaymentMethod(
    DepositPaymentMethod? mop,
  ) = _NaviagteToSelectedPaymentMethod;

  const factory DepositAccountsAndAmountEvent.getDepositDetails({
    required DepositPaymentMethod paymentMethod,
  }) = _GetDepositDetails;

  const factory DepositAccountsAndAmountEvent.onAmountChange(
    String amount,
    String convertedAmount,
    RatesModel? ratesModel,
    String? conversionRateString,
    String? targetCurrency,
  ) = _OnAmountChange;

  const factory DepositAccountsAndAmountEvent.changeButtonState(bool isValid) =
      _ChangeButtonState;

  const factory DepositAccountsAndAmountEvent.onApplePayResult({
    required ApplePayStatus paymentStatus,
    required String? gatewayCode,
    required ApplePayUtils applePay,
  }) = _OnApplePayResult;

  const factory DepositAccountsAndAmountEvent.onGooglePayResult({
    required GooglePayStatus paymentStatus,
    required String? gatewayCode,
    required GooglePayUtils googlePay,
  }) = _OnGooglePayResult;

  const factory DepositAccountsAndAmountEvent.onPaymentMethodChange() =
      OnPaymentMethodChange;
  const factory DepositAccountsAndAmountEvent.onPaymentSuccessContinuePressed() =
      OnPaymentSuccessContinuePressed;
}
