import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/fusion.dart';
import 'package:get_it/get_it.dart';
import 'package:hub/hub.dart';
import 'package:injectable/injectable.dart';
import 'package:login/login.dart';
import 'package:monitoring/monitoring.dart';
import 'package:network_logging/network_logging.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:user_account/user_account.dart';

import 'di_initializer.config.dart';

@InjectableInit(
  externalPackageModulesBefore: [
    ExternalModule(MonitoringPackageModule),
    ExternalModule(NetworkLoggingPackageModule),
  ],
  externalPackageModulesAfter: [
    ExternalModule(UserAccountPackageModule),
    ExternalModule(BrokerSettingsPackageModule),
    ExternalModule(LoginPackageModule),
    ExternalModule(DuploPackageModule),
    ExternalModule(OnboardingPackageModule),
    ExternalModule(PaymentPackageModule),
    ExternalModule(HubPackageModule),
    ExternalModule(ETraderPackageModule),
    ExternalModule(DomainPackageModule),
  ],
)
Future<void> configureDependencies(GetIt getIt, {String? env}) =>
    getIt.init(environment: env);
