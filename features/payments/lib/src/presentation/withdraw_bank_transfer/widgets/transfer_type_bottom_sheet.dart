import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;

class TransferTypeBottomSheet extends StatelessWidget {
  const TransferTypeBottomSheet({
    super.key,
    required this.transferTypes,
    required this.onSelectItem,
  });
  final List<String> transferTypes;
  final void Function(String item) onSelectItem;
  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Column(
      children: [
        SizedBox(height: 10),
        for (String item in transferTypes)
          InkWell(
            key: Key('transfer_type_${item.toLowerCase()}'),
            onTap: () {
              onSelectItem(item);
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(18.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DuploText(
                    text: item,
                    fontWeight: DuploFontWeight.medium,
                    style: DuploTextStyles.of(context).textSm,
                    color: theme.text.textSecondary,
                  ),
                  assets.Assets.images.arrowRightIc.svg(),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
