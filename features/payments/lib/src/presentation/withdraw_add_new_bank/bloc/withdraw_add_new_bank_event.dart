part of 'withdraw_add_new_bank_bloc.dart';

@freezed
sealed class WithdrawAddNewBankEvent with _$WithdrawAddNewBankEvent {
  const factory WithdrawAddNewBankEvent.onLocationOfBankChanged({
    required int index,
  }) = OnLocationOfBankChangedEvent;
  const factory WithdrawAddNewBankEvent.onTransferTypeChanged({
    required int index,
  }) = OnTransferTypeChangedEvent;
  const factory WithdrawAddNewBankEvent.getCountriesForLocationOfBank() =
      GetCountriesForLocationOfBankEvent;
  const factory WithdrawAddNewBankEvent.getTransferType({
    required String brokerId,
    required String countryCodeInThreeCharacter,
  }) = GetTransferTypeEvent;
  const factory WithdrawAddNewBankEvent.onFormChanged({
    required WithdrawAddNewAccountParam withdrawAddNewAccountParam,
  }) = OnFormChangedEvent;
  const factory WithdrawAddNewBankEvent.onRequestWithdrawButtonPressed({
    required WithdrawAddNewAccountParam withdrawAddNewAccountParam,
    required WithdrawFlowParams withdrawFlowParams,
  }) = OnRequestWithdrawButtonPressedEvent;
  const factory WithdrawAddNewBankEvent.onWithdrawalDetailsPressed() =
      OnWithdrawalDetailsPressedEvent;
  const factory WithdrawAddNewBankEvent.onAccountManuallyEntered() =
      OnAccountManuallyEnteredEvent;
  const factory WithdrawAddNewBankEvent.resetProcessState() =
      ResetProcessStateEvent;
  const factory WithdrawAddNewBankEvent.onFeesChanged(
    num fees,
    WithdrawFlowParams withdrawFlowParams,
  ) = OnFeesChangedEvent;
}
