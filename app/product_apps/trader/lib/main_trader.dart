import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:preferences/preferences.dart';
import 'package:trader/di/di_initializer.dart';
import 'package:trader/presentation/create_account.dart';
import 'package:trader/presentation/buy_sell_buttons.dart';
import 'package:trader/presentation/change_leverage.dart';
import 'package:trader/presentation/change_account_password.dart';
import 'package:trader/presentation/confirmation_sheet.dart';
import 'package:trader/presentation/country_selection_section.dart';
import 'package:trader/presentation/create_trade.dart';
import 'package:trader/presentation/deposit_amount.dart';
import 'package:trader/presentation/dialogs/custom_dialogs.dart';
import 'package:trader/presentation/duplo_demo/duplo_demo.dart';
import 'package:trader/presentation/duplo_demo/duplo_payment_account_tile.dart';
import 'package:trader/presentation/empty_or_error_state.dart';
import 'package:trader/presentation/funding_tab.dart';
import 'package:trader/presentation/grouped_buttons.dart';
import 'package:trader/presentation/image_name.dart';
import 'package:trader/presentation/keyboard_playground.dart';
import 'package:trader/presentation/legal_documents.dart';
import 'package:trader/presentation/login.dart';
import 'package:trader/presentation/login_options.dart';
import 'package:trader/presentation/main_container.dart';
import 'package:trader/presentation/morph_ui.dart';
import 'package:trader/presentation/transfer_funds_screen.dart';
import 'package:trader/presentation/withdraw_accounts_and_amount.dart';
import 'package:trader/presentation/withdraw_bank_transfer.dart';
import 'package:trader/presentation/withdraw_options.dart';
import 'package:trader/presentation/withdraw_skrill_and_neteller.dart';
import 'presentation/more_screens_demo.dart';
import 'package:trader/presentation/market_hours.dart';
import 'package:trader/presentation/mobile_otp_verification_flow.dart';
import 'package:trader/presentation/onboarding.dart';
import 'package:trader/presentation/payments/payments_screens.dart';
import 'package:trader/presentation/portfolio_position.dart';
import 'package:trader/presentation/position_options/position_option.dart';
import 'package:trader/presentation/price_percentage.dart';
import 'package:trader/presentation/product_details.dart';
import 'package:trader/presentation/reset_balance.dart';
import 'package:trader/presentation/search.dart';
import 'package:trader/presentation/signup_options.dart';
import 'package:trader/presentation/statements_tab.dart';
import 'package:trader/presentation/switch_account.dart';
import 'package:trader/presentation/symbols.dart';
import 'package:trader/presentation/text_chevron.dart';
import 'package:trader/presentation/text_selection_component_demo.dart';
import 'package:trader/presentation/toast_messages.dart';
import 'package:trader/presentation/trade_details.dart';
import 'package:trader/presentation/trade_options.dart';
import 'package:trader/presentation/trade_partial_close.dart';
import 'package:trader/presentation/trading_chart.dart';
import 'package:trader/presentation/trading_tab.dart';
import 'package:trader/presentation/user_verification.dart';
import 'package:trader/presentation/wallet_details.dart';
import 'package:trader/presentation/watchlisted_symbol_indicator.dart';
import 'package:trader/presentation/withdraw_add_new_bank_demo.dart';
import 'presentation/event_components.dart';
import 'presentation/payment_accounts_and_amount.dart';
import 'presentation/payment_options.dart';
import 'presentation/positions_and_trades.dart';
import 'presentation/price_alert_playground.dart';
import 'presentation/progress_tracker.dart';
import 'presentation/select_payment_account_list.dart';
import 'presentation/signup_flow.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EquitiLocalizationManager.init();
  EquitiLocalizationManager.update();
  await configureDependencies();

  // Set user details for trader app
  diContainer<EquitiPreferences>().setValue("broker_id", "trader_broker_id");
  diContainer<EquitiPreferences>().setValue("user_id", "trader_user_id");
  diContainer<EquitiPreferences>().setValue(
    "user_registration_id",
    "trader_user_registration_id",
  );

  runApp(
    MainTrader(
      supportedLocales: EquitiLocalization.supportedLocales,
      localizationsDelegates: EquitiLocalization.localizationsDelegates,
    ),
  );
}

class MainTrader extends ProductHost {
  MainTrader({
    super.key,
    required super.supportedLocales,
    required super.localizationsDelegates,
  });

  @override
  final List<DisplayableComponent> displayableComponents = [
    withdrawAddNewBank(),
    paymentsScreens(),
    tradingChartView(),
    morphUI(),
    keyboardPlayground(),
    resetBalance(),
    legalDocuments(),
    changeLeverage(),
    buySellButtons(),
    productDetails(),
    symbols(),
    symbolNameAndImage(),
    pricePercentageView(),
    textChevronView(),
    countrySelectionSection(),
    duploDemo(),
    textSelectionDisplay(),
    emptyOrErrorStateView(),
    marketHours(),
    groupedButtons(),
    search(),
    switchAccount(),
    priceAlert(),
    dialogs(),
    login(),
    toastMessages(),
    watchlistedSymbolIndicator(),
    createOrder(),
    navigationBottomBar(),
    tradeOptions(),
    positionAndTrades(),
    potitionOptions(),
    portfolio(),
    tradeDetials(),
    moreScreensDemo(),
    confirmationSheet(),
    partialClose(),
    progressTracker(),
    loginOptions(),
    signupOptions(),
    onboarding(),
    userVerification(),
    signupFlow(),
    mobileOtpVerificationFlow(),
    statementsTab(),
    eventComponents(),
    fundingTab(),
    tradingTab(),
    duploPaymentAccountTileDemo(),
    selectPaymentAccountList(),
    paymentOptions(),
    walletDetails(),
    depositAmount(),
    paymentAccountsAndAmount(),
    withdrawOptions(),
    withdrawAccountsAndAmount(),
    withdrawSkrillAndNeteller(),
    createAccount(),
    withdrawBankTransfer(),
    newTransferFunds(),
    changeAccountPassword(),
  ];

  @override
  final String appName = 'Trader Host App';
}
