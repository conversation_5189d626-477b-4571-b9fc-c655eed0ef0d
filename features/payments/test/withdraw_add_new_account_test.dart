// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'scenarios/withdraw_add_new_account_success.dart';
import 'scenarios/withdraw_add_new_account_failure.dart';
import 'package:payment/src/domain/data/withdrawal_mop_types.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/withdraw_add_new_bank/withdraw_add_new_bank.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Withdraw Add New Account Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Add new account''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Add new account''');
        await theAppIsRendered(
          tester,
          WithdrawAddNewBank(
            withdrawFlowParams: WithdrawFlowParams(
              tradingAccountId: '',
              paymentType: WithdrawalMop.cards,
              accountCurrency: '',
              currency: '',
              conversionRateString: '',
              amount: 0,
              convertedAmount: 0,
              conversionRate: 0,
              accountBalance: 1000,
            ),
          ),
          scenarios: [withdrawAddNewAccountSuccess],
        );
        await screenshotVerifiedWithCustomPump(
          tester,
          'withdraw_add_new_account/step_1_loaded',
        );
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Add new account''', success);
      }
    });
  });
}
