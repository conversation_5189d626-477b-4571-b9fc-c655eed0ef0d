import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResetBalanceAmountField extends StatelessWidget {
  const ResetBalanceAmountField({
    super.key,
    required this.controller,
    required this.homeCurrency,
    required this.onChanged,
  });

  final TextEditingController controller;
  final String homeCurrency;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    final style = context.duploTextStyles;
    final theme = context.duploTheme;

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        BlocBuilder<ResetBalanceBloc, ResetBalanceState>(
          buildWhen: (previous, current) => false,
          builder: (builderContext, state) {
            return DuploTextField(
              semanticsIdentifier: 'reset_balance_amount_field',
              controller: controller,
              keyboardType: TextInputType.number,
              label: EquitiLocalization.of(context).trader_add_amount,
              hint: EquitiLocalization.of(context).trader_add_amount,

              onChanged: onChanged,
            );
          },
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 10, right: 12),
          child: Container(
            decoration: BoxDecoration(
              color: theme.background.bgTertiary,
              borderRadius: BorderRadius.circular(DuploRadius.radius_sm_6),
            ),
            padding: const EdgeInsets.all(5),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                FlagProvider.getFlagFromCurrencyCode(homeCurrency),
                const SizedBox(width: 5),
                DuploText(
                  text: homeCurrency,
                  style: style.textMd,
                  fontWeight: DuploFontWeight.regular,
                  color: theme.text.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
