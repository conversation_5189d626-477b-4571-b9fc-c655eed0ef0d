import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:payment/src/data/payment_status_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'dart:async';

import 'package:payment/src/presentation/widgets/leave_confirmation_bottom_sheet.dart';

class AdditionalPaymentMethodsScreen extends StatefulWidget {
  final String url;
  final String title;
  final String transactionId;
  final String accountNumber;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;
  final DepositFlowConfig depositFlowConfig;

  const AdditionalPaymentMethodsScreen({
    required this.url,
    required this.title,
    required this.transactionId,
    required this.accountNumber,
    required this.depositFlowConfig,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
  });

  @override
  _AdditionalPaymentMethodsScreenState createState() =>
      _AdditionalPaymentMethodsScreenState();
}

class _AdditionalPaymentMethodsScreenState
    extends State<AdditionalPaymentMethodsScreen> {
  bool isLoading = true;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    // Start a 10-minute timer when the screen is initialized
    _startTimeoutTimer();
  }

  @override
  void dispose() {
    // Cancel the timer when the screen is disposed
    _timeoutTimer?.cancel();
    _cancelTimeoutTimer();
    super.dispose();
  }

  void _startTimeoutTimer() {
    // Cancel any existing timer first
    _cancelTimeoutTimer();

    // Set a 10-minute timeout (600 seconds)
    _timeoutTimer = Timer(const Duration(minutes: 10), () {
      print("⚠️ Payment timeout: No callback received after 10 minutes");
      _navigateToPaymentStatusPage();
    });
  }

  void _cancelTimeoutTimer() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }

  /// Handles payment events received from the WebView
  ///
  /// Processes the status received from the WebView,
  /// navigating to PaymentStatusScreen based on the status.
  ///
  /// For declined status we directly navigate to PaymentStatusScreen with
  /// rejected status, for all else we'll be polling for the final status on
  /// the PaymentStatusScreen.
  void _handlePaymentEvent(AdditionalPaymentMethodsStatus status) {
    // Cancel the timeout timer as we received a callback
    _cancelTimeoutTimer();

    switch (status) {
      case AdditionalPaymentMethodsStatus.declined:
        _navigateToPaymentStatusPage(status: PaymentStatus.rejected);
        break;
      case AdditionalPaymentMethodsStatus.approved:
      case AdditionalPaymentMethodsStatus.pending:
        _navigateToPaymentStatusPage();
        break;
      case AdditionalPaymentMethodsStatus.unknown:
        break;
    }
  }

  void _navigateToPaymentStatusPage({PaymentStatus? status}) {
    diContainer<PaymentNavigation>().goToPaymentStatusScreen(
      paymentStatus: status,
      transactionId: widget.transactionId,
      accountNumber: widget.accountNumber,
      depositFlowConfig: widget.depositFlowConfig,
      maxPollingAttempts: widget.maxPollingAttempts,
      pollingFrequencySeconds: widget.pollingFrequencySeconds,
    );
  }

  @override
  Widget build(BuildContext context) {
    print(widget.url);
    final theme = context.duploTheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          DuploSheet.showModalSheet<Widget>(
            context: context,
            hasTopBarLayer: false,
            hideCloseButton: true,
            content: (contextContext) {
              return LeaveConfirmationBottomSheet();
            },
            title: '',
          );
        }
      },
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(title: widget.title),
        body: Stack(
          children: [
            Opacity(
              opacity: isLoading ? 0.0 : 1.0, // Hide WebView when loading
              child: InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                onLoadStart: (controller, url) {
                  setState(() {
                    isLoading = true;
                  });
                },
                onLoadStop: (controller, url) {
                  final status = getAdditionalPaymentMethodsStatus(
                    url?.toString() ?? '',
                  );
                  _handlePaymentEvent(status);
                  setState(() {
                    isLoading = false;
                  });
                },
              ),
            ),
            if (isLoading)
              Center(
                child: DuploLoadingIndicator(
                  color: theme.foreground.fgBrandPrimary,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

enum AdditionalPaymentMethodsStatus { approved, pending, declined, unknown }

AdditionalPaymentMethodsStatus getAdditionalPaymentMethodsStatus(String url) {
  final uri = Uri.parse(url);
  final status = uri.queryParameters['status'];
  switch (status) {
    case 'approved':
      return AdditionalPaymentMethodsStatus.approved;
    case 'pending':
      return AdditionalPaymentMethodsStatus.pending;
    case 'declined':
      return AdditionalPaymentMethodsStatus.declined;
    default:
      print("⚠️ Unknown payment status: ${status ?? ''}");
      return AdditionalPaymentMethodsStatus.unknown;
  }
}
