part of 'edit_price_alert_bloc.dart';

@freezed
sealed class EditPriceAlertEvent with _$EditPriceAlertEvent {
  const factory EditPriceAlertEvent.percentOptionChanged(int index) =
      _PercentOptionChanged;
  const factory EditPriceAlertEvent.symbolPriceChanged(double symbolPrice) =
      _SymbolPriceChanged;
  const factory EditPriceAlertEvent.tradeTypeChanged(
    TradeType tradeType,
    double symbolPrice,
  ) = _TradeTypeChanged;
  const factory EditPriceAlertEvent.setInitialValues(
    TradeType tradeType,
    double symbolPrice,
    int digits,
    double? enteredPrice,
  ) = _SetInitialValues;
  const factory EditPriceAlertEvent.inputFieldValueChanged(String value) =
      _InputFieldValueChanged;
}
