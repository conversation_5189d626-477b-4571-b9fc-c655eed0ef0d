// ignore_for_file: prefer-match-file-name

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:domain/domain.dart';

part 'payment_method_args.freezed.dart';

@freezed
abstract class DepositPaymentMethodArgs with _$DepositPaymentMethodArgs {
  const factory DepositPaymentMethodArgs({
    required DepositPaymentMethod method,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    required DepositFlowConfig depositFlowConfig,
  }) = _DepositPaymentMethodArgs;
}

@freezed
abstract class WithdrawalPaymentMethodArgs with _$WithdrawalPaymentMethodArgs {
  const factory WithdrawalPaymentMethodArgs({
    required WithdrawalPaymentMethod method,
  }) = _WithdrawalPaymentMethodArgs;
}
