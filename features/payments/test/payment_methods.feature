import 'scenarios/payment_methods_success.dart';
import 'scenarios/payment_methods_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/deposit_payment_methods/deposit_payment_options.dart';
import 'package:domain/domain.dart';


Feature: Payment Methods Screen
  @testMethodName: testGoldens
  Scenario: List all payment methods
    Given The {DepositPaymentOptions(depositFlowConfig: DepositFlowConfig(  origin: "",depositType: DepositType.additional,),)} app is rendered {scenarios:[paymentMethodsSuccess]}
    Then screenshot verified {'payment_methods_success'}


  @testMethodName: testGoldens
  Scenario: Failed to load payment methods
    Given The {DepositPaymentOptions(depositFlowConfig: DepositFlowConfig(  origin: "",depositType: DepositType.additional,),)} app is rendered {scenarios:[paymentMethodsFailure]}
    Then screenshot verified {'payment_methods_failure'}

