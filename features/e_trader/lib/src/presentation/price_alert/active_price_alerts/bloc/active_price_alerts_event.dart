part of 'active_price_alerts_bloc.dart';

@freezed
sealed class ActivePriceAlertsEvent with _$ActivePriceAlertsEvent {
  const factory ActivePriceAlertsEvent.startActiveAlerts({String? symbol}) =
      _StartActiveAlerts;
  const factory ActivePriceAlertsEvent.processAlert(
    ActiveAlertResponse? alertResponse,
  ) = _ProcessAlert;
  const factory ActivePriceAlertsEvent.emitError() = _EmitError;
  const factory ActivePriceAlertsEvent.updateActiveAlerts(
    EventType eventType,
    String? symbol,
  ) = _UpdateActiveAlerts;
  const factory ActivePriceAlertsEvent.removeDeletedAlert(String alertId) =
      _RemoveDeletedAlert;
}
