// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'modify_trade_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ModifyTradeEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeEvent()';
}


}

/// @nodoc
class $ModifyTradeEventCopyWith<$Res>  {
$ModifyTradeEventCopyWith(ModifyTradeEvent _, $Res Function(ModifyTradeEvent) __);
}


/// @nodoc


class _Initialize implements ModifyTradeEvent {
  const _Initialize();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Initialize);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeEvent.initialize()';
}


}




/// @nodoc


class _ModifyTradeTakeProfitChanged implements ModifyTradeEvent {
  const _ModifyTradeTakeProfitChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyTradeTakeProfitChangedCopyWith<_ModifyTradeTakeProfitChanged> get copyWith => __$ModifyTradeTakeProfitChangedCopyWithImpl<_ModifyTradeTakeProfitChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyTradeTakeProfitChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'ModifyTradeEvent.takeProfitChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$ModifyTradeTakeProfitChangedCopyWith<$Res> implements $ModifyTradeEventCopyWith<$Res> {
  factory _$ModifyTradeTakeProfitChangedCopyWith(_ModifyTradeTakeProfitChanged value, $Res Function(_ModifyTradeTakeProfitChanged) _then) = __$ModifyTradeTakeProfitChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$ModifyTradeTakeProfitChangedCopyWithImpl<$Res>
    implements _$ModifyTradeTakeProfitChangedCopyWith<$Res> {
  __$ModifyTradeTakeProfitChangedCopyWithImpl(this._self, this._then);

  final _ModifyTradeTakeProfitChanged _self;
  final $Res Function(_ModifyTradeTakeProfitChanged) _then;

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_ModifyTradeTakeProfitChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ModifyTradeStopLossChanged implements ModifyTradeEvent {
  const _ModifyTradeStopLossChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyTradeStopLossChangedCopyWith<_ModifyTradeStopLossChanged> get copyWith => __$ModifyTradeStopLossChangedCopyWithImpl<_ModifyTradeStopLossChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyTradeStopLossChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'ModifyTradeEvent.stopLossChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$ModifyTradeStopLossChangedCopyWith<$Res> implements $ModifyTradeEventCopyWith<$Res> {
  factory _$ModifyTradeStopLossChangedCopyWith(_ModifyTradeStopLossChanged value, $Res Function(_ModifyTradeStopLossChanged) _then) = __$ModifyTradeStopLossChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$ModifyTradeStopLossChangedCopyWithImpl<$Res>
    implements _$ModifyTradeStopLossChangedCopyWith<$Res> {
  __$ModifyTradeStopLossChangedCopyWithImpl(this._self, this._then);

  final _ModifyTradeStopLossChanged _self;
  final $Res Function(_ModifyTradeStopLossChanged) _then;

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_ModifyTradeStopLossChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of ModifyTradeEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ModifyTradeSubmit implements ModifyTradeEvent {
  const _ModifyTradeSubmit();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ModifyTradeSubmit);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeEvent.submit()';
}


}




/// @nodoc
mixin _$ModifyTradeState {

 PositionModel get positionModel; set positionModel(PositionModel value); MarginInformationModel? get marginInformation; set marginInformation(MarginInformationModel? value); String? get accountNumber; set accountNumber(String? value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get takeProfitState; set takeProfitState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get stopLossState; set stopLossState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); ModifyTradeProcessState get currentState; set currentState(ModifyTradeProcessState value);
/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ModifyTradeStateCopyWith<ModifyTradeState> get copyWith => _$ModifyTradeStateCopyWithImpl<ModifyTradeState>(this as ModifyTradeState, _$identity);





@override
String toString() {
  return 'ModifyTradeState(positionModel: $positionModel, marginInformation: $marginInformation, accountNumber: $accountNumber, takeProfitState: $takeProfitState, stopLossState: $stopLossState, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $ModifyTradeStateCopyWith<$Res>  {
  factory $ModifyTradeStateCopyWith(ModifyTradeState value, $Res Function(ModifyTradeState) _then) = _$ModifyTradeStateCopyWithImpl;
@useResult
$Res call({
 PositionModel positionModel, MarginInformationModel? marginInformation, String? accountNumber, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, ModifyTradeProcessState currentState
});


$PositionModelCopyWith<$Res> get positionModel;$MarginInformationModelCopyWith<$Res>? get marginInformation;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;$ModifyTradeProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$ModifyTradeStateCopyWithImpl<$Res>
    implements $ModifyTradeStateCopyWith<$Res> {
  _$ModifyTradeStateCopyWithImpl(this._self, this._then);

  final ModifyTradeState _self;
  final $Res Function(ModifyTradeState) _then;

/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? positionModel = null,Object? marginInformation = freezed,Object? accountNumber = freezed,Object? takeProfitState = null,Object? stopLossState = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
positionModel: null == positionModel ? _self.positionModel : positionModel // ignore: cast_nullable_to_non_nullable
as PositionModel,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ModifyTradeProcessState,
  ));
}
/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionModelCopyWith<$Res> get positionModel {
  
  return $PositionModelCopyWith<$Res>(_self.positionModel, (value) {
    return _then(_self.copyWith(positionModel: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModifyTradeProcessStateCopyWith<$Res> get currentState {
  
  return $ModifyTradeProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _ModifyTradeState extends ModifyTradeState {
   _ModifyTradeState({required this.positionModel, this.marginInformation, this.accountNumber, this.takeProfitState = const TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.inactive(), this.stopLossState = const TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>.inactive(), this.currentState = const ModifyTradeProcessState.loading()}): super._();
  

@override  PositionModel positionModel;
@override  MarginInformationModel? marginInformation;
@override  String? accountNumber;
@override@JsonKey()  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState;
@override@JsonKey()  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState;
@override@JsonKey()  ModifyTradeProcessState currentState;

/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ModifyTradeStateCopyWith<_ModifyTradeState> get copyWith => __$ModifyTradeStateCopyWithImpl<_ModifyTradeState>(this, _$identity);





@override
String toString() {
  return 'ModifyTradeState(positionModel: $positionModel, marginInformation: $marginInformation, accountNumber: $accountNumber, takeProfitState: $takeProfitState, stopLossState: $stopLossState, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$ModifyTradeStateCopyWith<$Res> implements $ModifyTradeStateCopyWith<$Res> {
  factory _$ModifyTradeStateCopyWith(_ModifyTradeState value, $Res Function(_ModifyTradeState) _then) = __$ModifyTradeStateCopyWithImpl;
@override @useResult
$Res call({
 PositionModel positionModel, MarginInformationModel? marginInformation, String? accountNumber, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, ModifyTradeProcessState currentState
});


@override $PositionModelCopyWith<$Res> get positionModel;@override $MarginInformationModelCopyWith<$Res>? get marginInformation;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;@override $ModifyTradeProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$ModifyTradeStateCopyWithImpl<$Res>
    implements _$ModifyTradeStateCopyWith<$Res> {
  __$ModifyTradeStateCopyWithImpl(this._self, this._then);

  final _ModifyTradeState _self;
  final $Res Function(_ModifyTradeState) _then;

/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? positionModel = null,Object? marginInformation = freezed,Object? accountNumber = freezed,Object? takeProfitState = null,Object? stopLossState = null,Object? currentState = null,}) {
  return _then(_ModifyTradeState(
positionModel: null == positionModel ? _self.positionModel : positionModel // ignore: cast_nullable_to_non_nullable
as PositionModel,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,accountNumber: freezed == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String?,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ModifyTradeProcessState,
  ));
}

/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionModelCopyWith<$Res> get positionModel {
  
  return $PositionModelCopyWith<$Res>(_self.positionModel, (value) {
    return _then(_self.copyWith(positionModel: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of ModifyTradeState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ModifyTradeProcessStateCopyWith<$Res> get currentState {
  
  return $ModifyTradeProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$ModifyTradeProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState()';
}


}

/// @nodoc
class $ModifyTradeProcessStateCopyWith<$Res>  {
$ModifyTradeProcessStateCopyWith(ModifyTradeProcessState _, $Res Function(ModifyTradeProcessState) __);
}


/// @nodoc


class ModifyTradeLoadingProcessState implements ModifyTradeProcessState {
  const ModifyTradeLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.loading()';
}


}




/// @nodoc


class ModifyTradeConnectedProcessState implements ModifyTradeProcessState {
  const ModifyTradeConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.connected()';
}


}




/// @nodoc


class ModifyTradeDisconnectedProcessState implements ModifyTradeProcessState {
  const ModifyTradeDisconnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeDisconnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.disconnected()';
}


}




/// @nodoc


class ModifyPlacingTradeProcessState implements ModifyTradeProcessState {
  const ModifyPlacingTradeProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyPlacingTradeProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.placingOrder()';
}


}




/// @nodoc


class ModifyTradeSuccessProcessState implements ModifyTradeProcessState {
  const ModifyTradeSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.orderSuccess()';
}


}




/// @nodoc


class ModifyTradeErrorProcessState implements ModifyTradeProcessState {
  const ModifyTradeErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.orderError()';
}


}




/// @nodoc


class ModifyTradeMarketClosedProcessState implements ModifyTradeProcessState {
  const ModifyTradeMarketClosedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ModifyTradeMarketClosedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ModifyTradeProcessState.marketClosed()';
}


}




// dart format on
