// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/widget/set_price_alert_widget.dart';
import 'scenarios/set_price_alert_success_scenario.dart';
import 'scenarios/set_price_alert_failure_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import './step/i_tap_key.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Set Alert Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: Set Alert default state (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/all_good_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert default state (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/all_good_success')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/all_good_success',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert default state (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/all_good_success')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert when Buy is selected -5% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/minus_5_percent')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert when Buy is selected -5% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/minus_5_percent')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapIdentifier(tester, 'button_segment_index_1');
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/minus_5_percent',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert when Buy is selected -5% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/minus_5_percent')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert when Buy is selected +15% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/plus_15_percent')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert when Buy is selected +15% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/plus_15_percent')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapIdentifier(tester, 'button_segment_index_3');
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/plus_15_percent',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert when Buy is selected +15% (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/plus_15_percent')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert when Sell is selected (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert when Sell is selected (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapKey(tester, "sell_trade_button");
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/sell_selected',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert when Sell is selected (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert when Sell is selected and then Buy (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected_then_buy')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert when Sell is selected and then Buy (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected_then_buy')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapKey(tester, "sell_trade_button");
          await iWait(tester);
          await iTapKey(tester, "buy_trade_button");
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/sell_selected_then_buy',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert when Sell is selected and then Buy (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/sell_selected_then_buy')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert default state and then increment (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_increment')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert default state and then increment (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_increment')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapKey(tester, "increment_button");
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/buy_default_increment',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert default state and then increment (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_increment')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert default state and then decrement (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_decrement')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert default state and then decrement (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_decrement')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapKey(tester, "decrement_button");
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/buy_default_decrement',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert default state and then decrement (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/buy_default_decrement')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert default state reset percent (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/reset_percent_selection')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert default state reset percent (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/reset_percent_selection')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertDefaultSuccesSenario],
          );
          await iWait(tester);
          await iTapIdentifier(tester, 'button_segment_index_3');
          await iWait(tester);
          await iTapKey(tester, "decrement_button");
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/reset_percent_selection',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert default state reset percent (scenarios: [setPriceAlertDefaultSuccesSenario], 'set_price_alert/reset_percent_selection')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Set Alert error state (scenarios: [setPriceAlertFailureSenario], 'set_price_alert/set_price_fails')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Set Alert error state (scenarios: [setPriceAlertFailureSenario], 'set_price_alert/set_price_fails')''',
          );
          await theAppIsRendered(
            tester,
            SetPriceAlertWidget(
              symbol: "EURCAD",
              symbolImageUrl: "",
              onHideTabbar: null,
            ),
            scenarios: [setPriceAlertFailureSenario],
          );
          await screenshotVerifiedWithCustomPump(
            tester,
            'set_price_alert/set_price_fails',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Set Alert error state (scenarios: [setPriceAlertFailureSenario], 'set_price_alert/set_price_fails')''',
            success,
          );
        }
      },
    );
  });
}
