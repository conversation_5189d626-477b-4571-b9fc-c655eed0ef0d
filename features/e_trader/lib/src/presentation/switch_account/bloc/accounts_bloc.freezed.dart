// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accounts_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AccountsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent()';
}


}

/// @nodoc
class $AccountsEventCopyWith<$Res>  {
$AccountsEventCopyWith(AccountsEvent _, $Res Function(AccountsEvent) __);
}


/// @nodoc


class _FetchAccounts implements AccountsEvent {
  const _FetchAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FetchAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.fetch()';
}


}




/// @nodoc


class _RefreshAccounts implements AccountsEvent {
  const _RefreshAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefreshAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.refreshAccounts()';
}


}




/// @nodoc


class _OnAccountSelected implements AccountsEvent {
  const _OnAccountSelected({required this.accountId});
  

 final  String accountId;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAccountSelectedCopyWith<_OnAccountSelected> get copyWith => __$OnAccountSelectedCopyWithImpl<_OnAccountSelected>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAccountSelected&&(identical(other.accountId, accountId) || other.accountId == accountId));
}


@override
int get hashCode => Object.hash(runtimeType,accountId);

@override
String toString() {
  return 'AccountsEvent.onAccountSelected(accountId: $accountId)';
}


}

/// @nodoc
abstract mixin class _$OnAccountSelectedCopyWith<$Res> implements $AccountsEventCopyWith<$Res> {
  factory _$OnAccountSelectedCopyWith(_OnAccountSelected value, $Res Function(_OnAccountSelected) _then) = __$OnAccountSelectedCopyWithImpl;
@useResult
$Res call({
 String accountId
});




}
/// @nodoc
class __$OnAccountSelectedCopyWithImpl<$Res>
    implements _$OnAccountSelectedCopyWith<$Res> {
  __$OnAccountSelectedCopyWithImpl(this._self, this._then);

  final _OnAccountSelected _self;
  final $Res Function(_OnAccountSelected) _then;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountId = null,}) {
  return _then(_OnAccountSelected(
accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _GoToSymbols implements AccountsEvent {
  const _GoToSymbols();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSymbols);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToSymbols()';
}


}




/// @nodoc


class _GoToDepositPaymentOptions implements AccountsEvent {
  const _GoToDepositPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToDepositPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToDepositPaymentOptions()';
}


}




/// @nodoc


class _GoToWithdrawPaymentOptions implements AccountsEvent {
  const _GoToWithdrawPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToWithdrawPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToWithdrawPaymentOptions()';
}


}




/// @nodoc


class _GoToTransferOptions implements AccountsEvent {
  const _GoToTransferOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToTransferOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToTransferOptions()';
}


}




/// @nodoc
mixin _$AccountsState {

 List<AccountViewModel> get tradingAccounts; List<AccountViewModel> get wallets; List<TradingAccountModel> get accounts; double? get totalEquity; double? get totalProfit; double? get totalCredit; double? get totalBalance; AccountsProcessState get processState;
/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountsStateCopyWith<AccountsState> get copyWith => _$AccountsStateCopyWithImpl<AccountsState>(this as AccountsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsState&&const DeepCollectionEquality().equals(other.tradingAccounts, tradingAccounts)&&const DeepCollectionEquality().equals(other.wallets, wallets)&&const DeepCollectionEquality().equals(other.accounts, accounts)&&(identical(other.totalEquity, totalEquity) || other.totalEquity == totalEquity)&&(identical(other.totalProfit, totalProfit) || other.totalProfit == totalProfit)&&(identical(other.totalCredit, totalCredit) || other.totalCredit == totalCredit)&&(identical(other.totalBalance, totalBalance) || other.totalBalance == totalBalance)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(tradingAccounts),const DeepCollectionEquality().hash(wallets),const DeepCollectionEquality().hash(accounts),totalEquity,totalProfit,totalCredit,totalBalance,processState);

@override
String toString() {
  return 'AccountsState(tradingAccounts: $tradingAccounts, wallets: $wallets, accounts: $accounts, totalEquity: $totalEquity, totalProfit: $totalProfit, totalCredit: $totalCredit, totalBalance: $totalBalance, processState: $processState)';
}


}

/// @nodoc
abstract mixin class $AccountsStateCopyWith<$Res>  {
  factory $AccountsStateCopyWith(AccountsState value, $Res Function(AccountsState) _then) = _$AccountsStateCopyWithImpl;
@useResult
$Res call({
 List<AccountViewModel> tradingAccounts, List<AccountViewModel> wallets, List<TradingAccountModel> accounts, double? totalEquity, double? totalProfit, double? totalCredit, double? totalBalance, AccountsProcessState processState
});


$AccountsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$AccountsStateCopyWithImpl<$Res>
    implements $AccountsStateCopyWith<$Res> {
  _$AccountsStateCopyWithImpl(this._self, this._then);

  final AccountsState _self;
  final $Res Function(AccountsState) _then;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tradingAccounts = null,Object? wallets = null,Object? accounts = null,Object? totalEquity = freezed,Object? totalProfit = freezed,Object? totalCredit = freezed,Object? totalBalance = freezed,Object? processState = null,}) {
  return _then(_self.copyWith(
tradingAccounts: null == tradingAccounts ? _self.tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<AccountViewModel>,wallets: null == wallets ? _self.wallets : wallets // ignore: cast_nullable_to_non_nullable
as List<AccountViewModel>,accounts: null == accounts ? _self.accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>,totalEquity: freezed == totalEquity ? _self.totalEquity : totalEquity // ignore: cast_nullable_to_non_nullable
as double?,totalProfit: freezed == totalProfit ? _self.totalProfit : totalProfit // ignore: cast_nullable_to_non_nullable
as double?,totalCredit: freezed == totalCredit ? _self.totalCredit : totalCredit // ignore: cast_nullable_to_non_nullable
as double?,totalBalance: freezed == totalBalance ? _self.totalBalance : totalBalance // ignore: cast_nullable_to_non_nullable
as double?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountsProcessState,
  ));
}
/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountsProcessStateCopyWith<$Res> get processState {
  
  return $AccountsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _AccountsState implements AccountsState {
  const _AccountsState({final  List<AccountViewModel> tradingAccounts = const [], final  List<AccountViewModel> wallets = const [], final  List<TradingAccountModel> accounts = const [], this.totalEquity, this.totalProfit, this.totalCredit, this.totalBalance, this.processState = const AccountsProcessState.loading()}): _tradingAccounts = tradingAccounts,_wallets = wallets,_accounts = accounts;
  

 final  List<AccountViewModel> _tradingAccounts;
@override@JsonKey() List<AccountViewModel> get tradingAccounts {
  if (_tradingAccounts is EqualUnmodifiableListView) return _tradingAccounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tradingAccounts);
}

 final  List<AccountViewModel> _wallets;
@override@JsonKey() List<AccountViewModel> get wallets {
  if (_wallets is EqualUnmodifiableListView) return _wallets;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wallets);
}

 final  List<TradingAccountModel> _accounts;
@override@JsonKey() List<TradingAccountModel> get accounts {
  if (_accounts is EqualUnmodifiableListView) return _accounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accounts);
}

@override final  double? totalEquity;
@override final  double? totalProfit;
@override final  double? totalCredit;
@override final  double? totalBalance;
@override@JsonKey() final  AccountsProcessState processState;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountsStateCopyWith<_AccountsState> get copyWith => __$AccountsStateCopyWithImpl<_AccountsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountsState&&const DeepCollectionEquality().equals(other._tradingAccounts, _tradingAccounts)&&const DeepCollectionEquality().equals(other._wallets, _wallets)&&const DeepCollectionEquality().equals(other._accounts, _accounts)&&(identical(other.totalEquity, totalEquity) || other.totalEquity == totalEquity)&&(identical(other.totalProfit, totalProfit) || other.totalProfit == totalProfit)&&(identical(other.totalCredit, totalCredit) || other.totalCredit == totalCredit)&&(identical(other.totalBalance, totalBalance) || other.totalBalance == totalBalance)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_tradingAccounts),const DeepCollectionEquality().hash(_wallets),const DeepCollectionEquality().hash(_accounts),totalEquity,totalProfit,totalCredit,totalBalance,processState);

@override
String toString() {
  return 'AccountsState(tradingAccounts: $tradingAccounts, wallets: $wallets, accounts: $accounts, totalEquity: $totalEquity, totalProfit: $totalProfit, totalCredit: $totalCredit, totalBalance: $totalBalance, processState: $processState)';
}


}

/// @nodoc
abstract mixin class _$AccountsStateCopyWith<$Res> implements $AccountsStateCopyWith<$Res> {
  factory _$AccountsStateCopyWith(_AccountsState value, $Res Function(_AccountsState) _then) = __$AccountsStateCopyWithImpl;
@override @useResult
$Res call({
 List<AccountViewModel> tradingAccounts, List<AccountViewModel> wallets, List<TradingAccountModel> accounts, double? totalEquity, double? totalProfit, double? totalCredit, double? totalBalance, AccountsProcessState processState
});


@override $AccountsProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$AccountsStateCopyWithImpl<$Res>
    implements _$AccountsStateCopyWith<$Res> {
  __$AccountsStateCopyWithImpl(this._self, this._then);

  final _AccountsState _self;
  final $Res Function(_AccountsState) _then;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tradingAccounts = null,Object? wallets = null,Object? accounts = null,Object? totalEquity = freezed,Object? totalProfit = freezed,Object? totalCredit = freezed,Object? totalBalance = freezed,Object? processState = null,}) {
  return _then(_AccountsState(
tradingAccounts: null == tradingAccounts ? _self._tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<AccountViewModel>,wallets: null == wallets ? _self._wallets : wallets // ignore: cast_nullable_to_non_nullable
as List<AccountViewModel>,accounts: null == accounts ? _self._accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>,totalEquity: freezed == totalEquity ? _self.totalEquity : totalEquity // ignore: cast_nullable_to_non_nullable
as double?,totalProfit: freezed == totalProfit ? _self.totalProfit : totalProfit // ignore: cast_nullable_to_non_nullable
as double?,totalCredit: freezed == totalCredit ? _self.totalCredit : totalCredit // ignore: cast_nullable_to_non_nullable
as double?,totalBalance: freezed == totalBalance ? _self.totalBalance : totalBalance // ignore: cast_nullable_to_non_nullable
as double?,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountsProcessState,
  ));
}

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountsProcessStateCopyWith<$Res> get processState {
  
  return $AccountsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$AccountsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState()';
}


}

/// @nodoc
class $AccountsProcessStateCopyWith<$Res>  {
$AccountsProcessStateCopyWith(AccountsProcessState _, $Res Function(AccountsProcessState) __);
}


/// @nodoc


class AccountsLoadingProcessState implements AccountsProcessState {
  const AccountsLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.loading()';
}


}




/// @nodoc


class AccountsSuccessProcessState implements AccountsProcessState {
  const AccountsSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.success()';
}


}




/// @nodoc


class AccountsErrorProcessState implements AccountsProcessState {
  const AccountsErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.error()';
}


}




// dart format on
