import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';

Widget _stepperControlWithOptions(
  List<String> options,
  int selectedIndex,
  String title,
  bool addFooter, [
  bool addBorder = true,
]) {
  Widget? segmentControlWidget;
  if (options.isNotEmpty) {
    segmentControlWidget = HighlightOptionBoxWidget(
      selectedIndex: selectedIndex,
      options: options,
      onSelectionChange: (value, index) {
        print(index);
      },
    );
  }

  final inputWidget = StepperNumberInputWidget(
    // ignore: prefer-number-format
    controller: TextEditingController(text: 2.56.toString()),
    prescisionFactor: 2,
    hintText: 'Enter a number',
    onValueChange: (value) {
      print(value);
    },
    changeFactor: 0.1,
  );

  Widget? footer;
  if (addFooter) {
    footer = OrderLimitFooterWidget(
      firstPair: KeyValuePair(label: "Price", value: "32"),
      firstColor: Colors.red,
      secondColor: Colors.green,
    );
  }

  final stepper = StepperControlWidget(
    title: title,
    segmentControWidget: segmentControlWidget,
    inputWidget: inputWidget,
    footerWidget: footer,
    bordered: addBorder,
  );
  return Scaffold(
    appBar: AppBar(
      automaticallyImplyLeading: true,
      title: Text("Stepper Number Input Widget"),
    ),
    body: Container(padding: EdgeInsets.all(16), child: stepper),
  );
}

DisplayableComponent stepperWidgets() {
  return DisplayableComponent(
    title: 'Stepper Widgets',
    children: [
      DisplayableComponent(
        title: 'Default',
        onTap: () {
          final options = ["-25", "-15", "-5", "5", "15", "25"];
          return _stepperControlWithOptions(options, 0, "Some title", true);
        },
      ),
      DisplayableComponent(
        title: 'Default - Without Border',
        onTap: () {
          final options = ["-25", "-15", "-5", "5", "15", "25"];
          return _stepperControlWithOptions(
            options,
            0,
            "Some title",
            true,
            false,
          );
        },
      ),
      DisplayableComponent(
        title: 'Default - Without Footer',
        onTap: () {
          final options = ["-25", "-15", "-5", "5", "15", "25"];
          return _stepperControlWithOptions(options, 0, "Some title", false);
        },
      ),
      DisplayableComponent(
        title: 'Default - Without Segment Control',
        onTap: () {
          return _stepperControlWithOptions([], 0, "Some title", true);
        },
      ),
      DisplayableComponent(
        title: 'Less Options',
        onTap: () {
          final options = ["-25", "-15"];
          return _stepperControlWithOptions(options, 0, "Some title", true);
        },
      ),
      DisplayableComponent(
        title: 'More Options',
        onTap: () {
          final options = [
            "-25",
            "-15",
            "-5",
            "5",
            "15",
            "25",
            "35",
            "45",
            "55",
            "65",
          ];
          return _stepperControlWithOptions(options, 0, "Some title", true);
        },
      ),
      DisplayableComponent(
        title: 'Long Title',
        onTap: () {
          final options = [
            "-25",
            "-15",
            "-5",
            "5",
            "15",
            "25",
            "35",
            "45",
            "55",
            "65",
          ];
          return _stepperControlWithOptions(
            options,
            0,
            "Some title sdf sdfs sdf sdf sdfsdfsf sd435345 et34",
            true,
          );
        },
      ),
      DisplayableComponent(
        title: 'Different Selected Index',
        onTap: () {
          final options = ["-25", "-15", "-5", "5", "15", "25"];
          return _stepperControlWithOptions(
            options,
            3,
            "Some title sdf sdfs sdf sdf sdfsdfsf sd435345 et34",
            true,
          );
        },
      ),
    ],
  );
}
