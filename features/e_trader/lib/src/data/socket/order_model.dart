import 'package:e_trader/src/domain/model/entry_order_type.dart';
import 'package:e_trader/src/domain/model/price_direction.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_model.freezed.dart';
part 'order_model.g.dart';

@freezed
abstract class OrderModel with _$OrderModel {
  const OrderModel._();
  factory OrderModel({
    @JsonKey(name: 'priceDirection')
    @PriceDirectionIntConverter()
    required PriceDirection priceDirection,
    required int event,
    required int id,
    required String symbol,
    @TradeTypeIntConverter() required TradeType tradeType,
    required String dateSetup,
    required int dateDone,
    required double openPrice,
    required double currentPrice,
    required double stopLoss,
    required double takeProfit,
    required int volume,
    required double marginRate,
    required int accountNumber,
    required int tradeAction,
    double? stopLimit,
    required int digits,
    required String productName,
    required String productLogoUrl,
    required int volumeInitial,
    required String comment,
    required int state,
    required int type,
  }) = _OrderModel;

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);

  double get priceChange => ((openPrice - currentPrice) / currentPrice) * 100;

  EntryOrderType get entryOrderType {
    switch (tradeType) {
      case TradeType.buy:
        if (openPrice < currentPrice) {
          return EntryOrderType.buyLimit;
        }
        return EntryOrderType.buyStop;
      case TradeType.sell:
        if (openPrice > currentPrice) {
          return EntryOrderType.sellLimit;
        }
        return EntryOrderType.sellStop;
    }
  }

  double get lotSize => volume / 10000;
}
